# Savory Tunisian Bounty 🌿

An e-commerce platform showcasing authentic Tunisian products including spices, olive oils, dates, and traditional artisanal goods.

## 🚀 Project Overview

This project is built following a structured sprint plan to deliver a complete e-commerce solution for Tunisian specialty products. We're currently in **Sprint 3: Shopping Cart Core Functionality**.

### Current Status: ✅ Sprint 4 Complete - MVP Ready!

- ✅ Supabase project created and configured
- ✅ Database schema with RLS policies
- ✅ Vue.js 3 + TypeScript + Vite setup
- ✅ Tailwind CSS styling
- ✅ Pinia state management
- ✅ Vue Router navigation
- ✅ Authentication system (Supabase Auth)
- ✅ Product catalog with categories
- ✅ Shopping cart functionality
- ✅ **Multi-step checkout process**
- ✅ **Address management**
- ✅ **Payment processing (Stripe integration)**
- ✅ **Order placement and management**
- ✅ **Order confirmation and history**
- ✅ Responsive design

## 🛠 Tech Stack

- **Frontend**: Vue 3, TypeScript, Vite
- **Styling**: Tailwind CSS
- **State Management**: Pinia
- **Routing**: Vue Router
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **Deployment**: Ready for Vercel/Netlify

## 📦 Features Implemented

### Sprint 1 Features (Core Authentication & Basic Product Browsing)

- ✅ User registration and login
- ✅ Secure authentication with Supabase
- ✅ Product browsing by category
- ✅ Basic product listing
- ✅ Responsive navigation

### Sprint 3 Features (Shopping Cart Core Functionality)

- ✅ Add products to shopping cart
- ✅ View cart contents with detailed item information
- ✅ Update item quantities with stock validation
- ✅ Remove items from cart
- ✅ Cart persistence with localStorage
- ✅ Toast notifications for user feedback
- ✅ Responsive cart design
- ✅ Empty cart state handling

### Sprint 4 Features (Checkout & Order Placement - MVP)

- ✅ **Multi-step checkout process** (Shipping → Payment → Review)
- ✅ **Address management** (Add, edit, delete, set default addresses)
- ✅ **Payment processing** with Stripe integration (demo mode)
- ✅ **Order creation** with automatic stock updates
- ✅ **Order confirmation** page with detailed order information
- ✅ **Order history** with status tracking and reorder functionality
- ✅ **Secure checkout** with form validation and error handling
- ✅ **Responsive design** across all checkout steps

### Additional Foundation Features

- ✅ Shopping cart management
- ✅ Product search functionality
- ✅ Product detail pages
- ✅ User profile management
- ✅ Modern UI/UX design

## 🗄 Database Schema

The application uses the following main tables:

- `profiles` - User profile information
- `categories` - Product categories
- `products` - Product catalog
- `user_addresses` - User shipping addresses
- `orders` - Order management
- `order_items` - Order line items

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository

```bash
git clone <repository-url>
cd savory-tunisian-bounty
```

2. Install dependencies

```bash
npm install
```

3. Set up environment variables

```bash
cp .env.example .env
```

Update the `.env` file with your Supabase credentials:

```
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. Start the development server

```bash
npm run dev
```

5. Open http://localhost:5173 in your browser

### Build for Production

```bash
npm run build
```

## 🎯 Sprint Roadmap

### ✅ Pre-Sprint 0: Foundation & Setup (COMPLETED)

- Supabase project setup
- Vue.js application foundation
- Basic authentication and product browsing

### 🔄 Sprint 1: Core Authentication & Basic Product Browsing (IN PROGRESS)

- Enhanced user authentication flows
- Product category filtering
- Search functionality
- Responsive design improvements

### 📋 Upcoming Sprints

- **Sprint 2**: Dynamic Product Catalog & Search
- **Sprint 3**: Shopping Cart Core Functionality
- **Sprint 4**: Checkout & Order Placement (MVP)
- **Sprint 5**: Admin Product & Order Management
- **Sprint 6**: Profile Management & Order History
- **Sprint 7**: Enhanced Product Discovery & Producer Stories
- **Sprint 8**: Reviews & Newsletter

## 🔐 Environment Variables

Create a `.env` file in the root directory:

```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

## 📱 Features

### Current Features (MVP Complete!)

- 🔐 **User authentication** (register/login/logout)
- 📦 **Product catalog** with categories and search
- 🛒 **Shopping cart** with persistence and management
- 🏠 **Address management** (add, edit, delete, set default)
- 💳 **Checkout process** (multi-step: shipping → payment → review)
- 💰 **Payment processing** with Stripe integration (demo mode)
- 📋 **Order management** (create, view, track status)
- 📧 **Order confirmation** with detailed order information
- 📚 **Order history** with reorder functionality
- 👤 **User profile** management
- 📱 **Responsive design** across all devices
- 🎨 **Modern UI** with Tailwind CSS
- 🔔 **Toast notifications** for user feedback

### Coming Soon (Future Sprints)

- 📊 Admin dashboard for product and order management
- ⭐ Product reviews and ratings system
- 📰 Newsletter subscription
- 🏪 Producer stories and backgrounds
- 🎁 Custom product mix builder
- 🏆 Loyalty program and referrals
- 📈 Advanced analytics and reporting

## 🤝 Contributing

This project follows a structured development approach with defined sprints. Please refer to the sprint plan for current priorities and upcoming features.

## 📄 License

This project is licensed under the MIT License.
