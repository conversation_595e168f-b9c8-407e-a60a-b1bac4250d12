import{d as h,x as k,c as d,a,b as t,e as c,w as u,f as w,t as o,F as C,g as S,k as j,h as p,i as r}from"./index-COfeaTnR.js";const q={class:"min-h-screen bg-gray-50"},F={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},$={key:0,class:"text-center py-12"},B={key:1,class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},V={class:"lg:col-span-2"},N={class:"bg-white rounded-lg shadow-sm"},T={class:"p-6"},A={class:"text-xl font-semibold text-gray-900 mb-6"},E={class:"space-y-6"},I={class:"flex-shrink-0"},D=["src","alt"],H={class:"flex-1 min-w-0"},M={class:"text-lg font-medium text-gray-900 truncate"},Q={class:"text-sm text-gray-600 mt-1"},L={class:"text-sm text-gray-500 mt-1"},O={class:"flex items-center space-x-2"},P=["onClick","disabled"],R={class:"w-12 text-center font-medium"},Y=["onClick","disabled"],z={class:"text-right"},G={class:"text-lg font-semibold text-gray-900"},J=["onClick"],K={class:"lg:col-span-1"},U={class:"bg-white rounded-lg shadow-sm p-6 sticky top-24"},W={class:"space-y-4"},X={class:"flex justify-between"},Z={class:"font-medium"},tt={class:"border-t border-gray-200 pt-4"},st={class:"flex justify-between"},et={class:"text-lg font-semibold text-gray-900"},ot={class:"mt-6 space-y-3"},rt=h({__name:"CartView",setup(nt){const n=k(),_=d(()=>n.items),g=d(()=>n.itemCount),x=d(()=>n.totalAmount),b=d(()=>n.isEmpty),m=(l,s)=>{n.updateQuantity(l,s)},v=l=>{n.removeItem(l)},f=()=>{confirm("Are you sure you want to clear your cart?")&&n.clearCart()};return(l,s)=>{const i=w("router-link");return r(),a("div",q,[t("div",F,[s[12]||(s[12]=t("h1",{class:"text-3xl font-serif font-bold text-gray-900 mb-8"},"Shopping Cart",-1)),b.value?(r(),a("div",$,[s[1]||(s[1]=t("div",{class:"text-6xl mb-4"},"🛒",-1)),s[2]||(s[2]=t("h2",{class:"text-2xl font-semibold text-gray-900 mb-4"},"Your cart is empty",-1)),s[3]||(s[3]=t("p",{class:"text-gray-600 mb-8"}," Discover our authentic Tunisian products and add them to your cart ",-1)),c(i,{to:"/products",class:"btn-primary text-lg px-8 py-3"},{default:u(()=>s[0]||(s[0]=[p(" Start Shopping ")])),_:1,__:[0]})])):(r(),a("div",B,[t("div",V,[t("div",N,[t("div",T,[t("h2",A," Cart Items ("+o(g.value)+") ",1),t("div",E,[(r(!0),a(C,null,S(_.value,e=>(r(),a("div",{key:e.product.id,class:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg"},[t("div",I,[t("img",{src:e.product.image_url||"/placeholder-product.jpg",alt:e.product.name,class:"w-20 h-20 object-cover rounded-lg"},null,8,D)]),t("div",H,[t("h3",M,o(e.product.name),1),t("p",Q," $"+o(e.product.price.toFixed(2))+" each ",1),t("p",L,o(e.product.stock_quantity)+" in stock ",1)]),t("div",O,[t("button",{onClick:y=>m(e.product.id,e.quantity-1),disabled:e.quantity<=1,class:"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"},s[4]||(s[4]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 12H4"})],-1)]),8,P),t("span",R,o(e.quantity),1),t("button",{onClick:y=>m(e.product.id,e.quantity+1),disabled:e.quantity>=e.product.stock_quantity,class:"w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"},s[5]||(s[5]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)]),8,Y)]),t("div",z,[t("p",G," $"+o((e.product.price*e.quantity).toFixed(2)),1),t("button",{onClick:y=>v(e.product.id),class:"text-sm text-red-600 hover:text-red-800 mt-1"}," Remove ",8,J)])]))),128))])])])]),t("div",K,[t("div",U,[s[11]||(s[11]=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Order Summary",-1)),t("div",W,[t("div",X,[s[6]||(s[6]=t("span",{class:"text-gray-600"},"Subtotal",-1)),t("span",Z,"$"+o(x.value.toFixed(2)),1)]),s[8]||(s[8]=j('<div class="flex justify-between"><span class="text-gray-600">Shipping</span><span class="font-medium">Free</span></div><div class="flex justify-between"><span class="text-gray-600">Tax</span><span class="font-medium">Calculated at checkout</span></div>',2)),t("div",tt,[t("div",st,[s[7]||(s[7]=t("span",{class:"text-lg font-semibold text-gray-900"},"Total",-1)),t("span",et," $"+o(x.value.toFixed(2)),1)])])]),t("div",ot,[c(i,{to:"/checkout",class:"w-full btn-primary text-center block py-3"},{default:u(()=>s[9]||(s[9]=[p(" Proceed to Checkout ")])),_:1,__:[9]}),c(i,{to:"/products",class:"w-full btn-outline text-center block py-3"},{default:u(()=>s[10]||(s[10]=[p(" Continue Shopping ")])),_:1,__:[10]})]),t("button",{onClick:f,class:"w-full mt-4 text-sm text-red-600 hover:text-red-800"}," Clear Cart ")])])]))])])}}});export{rt as default};
