var ie=Object.defineProperty;var de=(y,s,t)=>s in y?ie(y,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):y[s]=t;var W=(y,s,t)=>de(y,typeof s!="symbol"?s+"":s,t);import{d as J,r as w,C as Z,l as ue,a as i,D as O,b as e,m as _,n as C,v as P,q as ce,k as G,E as me,F as j,g as D,h as L,t as b,G as Y,i as d,H as ee,c as F,o as te,e as R,p as K,I as pe,B as fe,x as ve,J as ye,K as ge,u as be,L as M,w as xe,f as he}from"./index-COfeaTnR.js";var se="basil",_e=function(s){return s===3?"v3":s},re="https://js.stripe.com",we="".concat(re,"/").concat(se,"/stripe.js"),ke=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,Se=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/;var Ce=function(s){return ke.test(s)||Se.test(s)},$e=function(){for(var s=document.querySelectorAll('script[src^="'.concat(re,'"]')),t=0;t<s.length;t++){var a=s[t];if(Ce(a.src))return a}return null},X=function(s){var t="",a=document.createElement("script");a.src="".concat(we).concat(t);var n=document.head||document.body;if(!n)throw new Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return n.appendChild(a),a},Ae=function(s,t){!s||!s._registerWrapper||s._registerWrapper({name:"stripe-js",version:"7.3.1",startTime:t})},T=null,z=null,H=null,Pe=function(s){return function(t){s(new Error("Failed to load Stripe.js",{cause:t}))}},Ee=function(s,t){return function(){window.Stripe?s(window.Stripe):t(new Error("Stripe.js not available"))}},Ie=function(s){return T!==null?T:(T=new Promise(function(t,a){if(typeof window>"u"||typeof document>"u"){t(null);return}if(window.Stripe){t(window.Stripe);return}try{var n=$e();if(!(n&&s)){if(!n)n=X(s);else if(n&&H!==null&&z!==null){var c;n.removeEventListener("load",H),n.removeEventListener("error",z),(c=n.parentNode)===null||c===void 0||c.removeChild(n),n=X(s)}}H=Ee(t,a),z=Pe(a),n.addEventListener("load",H),n.addEventListener("error",z)}catch(l){a(l);return}}),T.catch(function(t){return T=null,Promise.reject(t)}))},Ve=function(s,t,a){if(s===null)return null;var n=t[0],c=n.match(/^pk_test/),l=_e(s.version),v=se;c&&l!==v&&console.warn("Stripe.js@".concat(l," was loaded on the page, but @stripe/stripe-js@").concat("7.3.1"," expected Stripe.js@").concat(v,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var u=s.apply(void 0,t);return Ae(u,a),u},q,oe=!1,ne=function(){return q||(q=Ie(null).catch(function(s){return q=null,Promise.reject(s)}),q)};Promise.resolve().then(function(){return ne()}).catch(function(y){oe||console.warn(y)});var Me=function(){for(var s=arguments.length,t=new Array(s),a=0;a<s;a++)t[a]=arguments[a];oe=!0;var n=Date.now();return ne().then(function(c){return Ve(c,t,n)})};const je="pk_test_demo_key";class ${static getStripe(){return this.stripePromise||(this.stripePromise=Me(je)),this.stripePromise}static async createPaymentIntent(s,t="usd",a){try{const n={id:`pi_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,client_secret:`pi_${Date.now()}_secret_${Math.random().toString(36).substr(2,9)}`,amount:Math.round(s*100),currency:t,status:"requires_payment_method"};return await new Promise(c=>setTimeout(c,500)),n}catch(n){throw console.error("Error creating payment intent:",n),new Error("Failed to create payment intent")}}static async confirmPayment(s,t){try{return await new Promise(n=>setTimeout(n,2e3)),Math.random()>.1?{success:!0,paymentIntent:{id:s.split("_secret_")[0],client_secret:s,amount:0,currency:"usd",status:"succeeded"}}:{success:!1,error:"Your card was declined. Please try a different payment method."}}catch(a){return console.error("Error confirming payment:",a),{success:!1,error:"An error occurred while processing your payment. Please try again."}}}static formatAmount(s,t="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:t.toUpperCase()}).format(s)}static validatePaymentData(s){var a,n,c,l;const t=[];return(a=s.cardNumber)!=null&&a.trim()?/^\d{13,19}$/.test(s.cardNumber.replace(/\s/g,""))||t.push("Invalid card number"):t.push("Card number is required"),(n=s.expiryDate)!=null&&n.trim()?/^(0[1-9]|1[0-2])\/\d{2}$/.test(s.expiryDate)||t.push("Invalid expiry date (MM/YY)"):t.push("Expiry date is required"),(c=s.cvc)!=null&&c.trim()?/^\d{3,4}$/.test(s.cvc)||t.push("Invalid CVC"):t.push("CVC is required"),(l=s.name)!=null&&l.trim()||t.push("Cardholder name is required"),t}static formatCardNumber(s){return(s.replace(/\s/g,"").match(/.{1,4}/g)||[]).join(" ")}static formatExpiryDate(s){const t=s.replace(/\D/g,"");return t.length>=2?t.substring(0,2)+(t.length>2?"/"+t.substring(2,4):""):t}static getCardType(s){const t=s.replace(/\s/g,"");return/^4/.test(t)?"visa":/^5[1-5]/.test(t)?"mastercard":/^3[47]/.test(t)?"amex":/^6/.test(t)?"discover":"unknown"}static calculateProcessingFee(s){return Math.round((s*.029+.3)*100)/100}}W($,"stripePromise",null);const De={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Le={class:"md:col-span-2"},Fe={class:"md:col-span-2"},Ue={class:"md:col-span-2"},Be={class:"md:col-span-2"},Ne={class:"flex items-center"},Te={key:0,class:"bg-red-50 border border-red-200 rounded-md p-3"},qe={class:"text-sm text-red-600 space-y-1"},Re={class:"flex justify-end space-x-3 pt-4"},ze=["disabled"],He={key:0,class:"flex items-center"},Oe={key:1},Q=J({__name:"AddressForm",props:{address:{default:null},userId:{},editMode:{type:Boolean,default:!1}},emits:["submit","cancel"],setup(y,{emit:s}){const t=y,a=s,n=w(!1),c=w([]),l=Z({user_id:t.userId,address_line_1:"",address_line_2:"",city:"",postal_code:"",country:"",is_default:!1});ue(()=>t.address,u=>{u&&(l.address_line_1=u.address_line_1,l.address_line_2=u.address_line_2||"",l.city=u.city,l.postal_code=u.postal_code,l.country=u.country,l.is_default=u.is_default)},{immediate:!0});const v=async()=>{try{n.value=!0,c.value=[];const u=Y.validateAddress(l);if(u.length>0){c.value=u;return}let o=null;t.editMode&&t.address?o=await Y.updateAddress(t.address.id,l):o=await Y.createAddress(l),o&&a("submit",o)}catch(u){console.error("Error saving address:",u),c.value=["Failed to save address. Please try again."]}finally{n.value=!1}};return(u,o)=>(d(),i("form",{onSubmit:O(v,["prevent"]),class:"space-y-4"},[e("div",De,[e("div",Le,[o[7]||(o[7]=e("label",{for:"address_line_1",class:"block text-sm font-medium text-gray-700 mb-1"}," Address Line 1 * ",-1)),C(e("input",{id:"address_line_1","onUpdate:modelValue":o[0]||(o[0]=f=>l.address_line_1=f),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent",placeholder:"Street address, P.O. box, company name"},null,512),[[P,l.address_line_1]])]),e("div",Fe,[o[8]||(o[8]=e("label",{for:"address_line_2",class:"block text-sm font-medium text-gray-700 mb-1"}," Address Line 2 ",-1)),C(e("input",{id:"address_line_2","onUpdate:modelValue":o[1]||(o[1]=f=>l.address_line_2=f),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent",placeholder:"Apartment, suite, unit, building, floor, etc."},null,512),[[P,l.address_line_2]])]),e("div",null,[o[9]||(o[9]=e("label",{for:"city",class:"block text-sm font-medium text-gray-700 mb-1"}," City * ",-1)),C(e("input",{id:"city","onUpdate:modelValue":o[2]||(o[2]=f=>l.city=f),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent",placeholder:"City"},null,512),[[P,l.city]])]),e("div",null,[o[10]||(o[10]=e("label",{for:"postal_code",class:"block text-sm font-medium text-gray-700 mb-1"}," Postal Code * ",-1)),C(e("input",{id:"postal_code","onUpdate:modelValue":o[3]||(o[3]=f=>l.postal_code=f),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent",placeholder:"Postal code"},null,512),[[P,l.postal_code]])]),e("div",Ue,[o[12]||(o[12]=e("label",{for:"country",class:"block text-sm font-medium text-gray-700 mb-1"}," Country * ",-1)),C(e("select",{id:"country","onUpdate:modelValue":o[4]||(o[4]=f=>l.country=f),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"},o[11]||(o[11]=[G('<option value="">Select a country</option><option value="US">United States</option><option value="CA">Canada</option><option value="TN">Tunisia</option><option value="FR">France</option><option value="DE">Germany</option><option value="GB">United Kingdom</option><option value="IT">Italy</option><option value="ES">Spain</option><option value="AU">Australia</option><option value="JP">Japan</option>',11)]),512),[[ce,l.country]])]),e("div",Be,[e("label",Ne,[C(e("input",{"onUpdate:modelValue":o[5]||(o[5]=f=>l.is_default=f),type:"checkbox",class:"rounded border-gray-300 text-amber-600 focus:ring-amber-500"},null,512),[[me,l.is_default]]),o[13]||(o[13]=e("span",{class:"ml-2 text-sm text-gray-700"},"Set as default address",-1))])])]),c.value.length>0?(d(),i("div",Te,[e("ul",qe,[(d(!0),i(j,null,D(c.value,f=>(d(),i("li",{key:f},b(f),1))),128))])])):_("",!0),e("div",Re,[e("button",{type:"button",onClick:o[6]||(o[6]=f=>u.$emit("cancel")),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-amber-500"}," Cancel "),e("button",{type:"submit",disabled:n.value,class:"px-4 py-2 text-sm font-medium text-white bg-amber-600 border border-transparent rounded-md hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed"},[n.value?(d(),i("span",He,o[14]||(o[14]=[e("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),L(" Saving... ")]))):(d(),i("span",Oe,b(u.editMode?"Update Address":"Add Address"),1))],8,ze)])],32))}}),Ge={class:"space-y-4"},Je={key:0,class:"space-y-3"},Ye={class:"space-y-3"},Ke=["onClick"],We={class:"flex items-start justify-between"},Xe={class:"flex items-start space-x-3"},Qe=["value","checked","onChange"],Ze={class:"flex-1"},et={class:"flex items-center space-x-2"},tt={class:"text-sm font-medium text-gray-900"},st={key:0,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800"},rt={class:"flex items-center space-x-2"},ot=["onClick"],nt=["onClick"],at={class:"border-t pt-4"},lt={key:0,class:"mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50"},it={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},dt={class:"bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-screen overflow-y-auto"},ut={key:2,class:"bg-red-50 border border-red-200 rounded-md p-3"},ct={class:"text-sm text-red-600"},mt=J({__name:"AddressSelector",props:{userId:{},modelValue:{}},emits:["update:modelValue","addressSelected"],setup(y,{emit:s}){const t=y,a=s,n=ee(),c=w(!1),l=w(null),v=w(""),u=F(()=>n.addresses);F(()=>n.loading);const o=F({get:()=>t.modelValue,set:r=>a("update:modelValue",r)});te(async()=>{try{await n.fetchAddresses(t.userId),!o.value&&n.defaultAddress&&f(n.defaultAddress.id)}catch{v.value="Failed to load addresses"}});const f=r=>{o.value=r;const m=u.value.find(x=>x.id===r);a("addressSelected",m||null)},A=r=>{c.value=!1,(u.value.length===1||r.is_default)&&f(r.id)},S=r=>{l.value=null,o.value===r.id&&a("addressSelected",r)},E=r=>{l.value=r},I=async r=>{confirm("Are you sure you want to delete this address?")&&await n.deleteAddress(r)&&o.value===r&&(o.value=null,a("addressSelected",null),n.defaultAddress&&f(n.defaultAddress.id))},h=r=>n.formatAddress(r);return(r,m)=>(d(),i("div",Ge,[u.value.length>0?(d(),i("div",Je,[m[3]||(m[3]=e("h3",{class:"text-lg font-medium text-gray-900"},"Select Shipping Address",-1)),e("div",Ye,[(d(!0),i(j,null,D(u.value,x=>(d(),i("div",{key:x.id,class:K(["relative border rounded-lg p-4 cursor-pointer transition-colors",[o.value===x.id?"border-amber-500 bg-amber-50":"border-gray-200 hover:border-gray-300"]]),onClick:U=>f(x.id)},[e("div",We,[e("div",Xe,[e("input",{type:"radio",value:x.id,checked:o.value===x.id,class:"mt-1 text-amber-600 focus:ring-amber-500",onChange:U=>f(x.id)},null,40,Qe),e("div",Ze,[e("div",et,[e("p",tt,b(h(x)),1),x.is_default?(d(),i("span",st," Default ")):_("",!0)])])]),e("div",rt,[e("button",{type:"button",onClick:O(U=>E(x),["stop"]),class:"text-sm text-amber-600 hover:text-amber-700"}," Edit ",8,ot),x.is_default?_("",!0):(d(),i("button",{key:0,type:"button",onClick:O(U=>I(x.id),["stop"]),class:"text-sm text-red-600 hover:text-red-700"}," Delete ",8,nt))])])],10,Ke))),128))])])):_("",!0),e("div",at,[e("button",{type:"button",onClick:m[0]||(m[0]=x=>c.value=!c.value),class:"flex items-center space-x-2 text-amber-600 hover:text-amber-700"},[m[4]||(m[4]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)),e("span",null,b(u.value.length>0?"Add New Address":"Add Shipping Address"),1)]),c.value?(d(),i("div",lt,[m[5]||(m[5]=e("h4",{class:"text-md font-medium text-gray-900 mb-4"},"Add New Address",-1)),R(Q,{"user-id":r.userId,onSubmit:A,onCancel:m[1]||(m[1]=x=>c.value=!1)},null,8,["user-id"])])):_("",!0)]),l.value?(d(),i("div",it,[e("div",dt,[m[6]||(m[6]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Edit Address",-1)),R(Q,{address:l.value,"user-id":r.userId,"edit-mode":!0,onSubmit:S,onCancel:m[2]||(m[2]=x=>l.value=null)},null,8,["address","user-id"])])])):_("",!0),v.value?(d(),i("div",ut,[e("p",ct,b(v.value),1)])):_("",!0)]))}}),pt={class:"space-y-6"},ft={class:"space-y-3"},vt={class:"flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50"},yt={key:0,class:"space-y-4"},gt={class:"bg-gray-50 p-4 rounded-lg"},bt={class:"relative"},xt={class:"absolute inset-y-0 right-0 flex items-center pr-3"},ht={key:0,class:"h-6 w-8",viewBox:"0 0 40 24",fill:"none"},_t={key:1,class:"h-6 w-8",viewBox:"0 0 40 24",fill:"none"},wt={key:2,class:"h-6 w-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},kt={class:"grid grid-cols-2 gap-4"},St={key:0,class:"bg-red-50 border border-red-200 rounded-md p-3"},Ct={class:"text-sm text-red-600 space-y-1"},$t={class:"bg-blue-50 border border-blue-200 rounded-md p-3"},At={class:"text-sm text-blue-700"},Pt={key:0,class:"text-xs text-blue-600"},Et=J({__name:"PaymentForm",props:{amount:{},loading:{type:Boolean,default:!1}},emits:["submit"],setup(y,{expose:s,emit:t}){const a=y,n=t,c=w("card"),l=w([]),v=Z({name:"",cardNumber:"",expiryDate:"",cvc:""}),u=F(()=>$.getCardType(v.cardNumber)),o=F(()=>$.calculateProcessingFee(a.amount)),f=h=>$.formatAmount(h),A=h=>{const r=h.target;v.cardNumber=$.formatCardNumber(r.value)},S=h=>{const r=h.target;v.expiryDate=$.formatExpiryDate(r.value)},E=h=>{const m=h.target.value.replace(/\D/g,"");v.cvc=m},I=()=>{l.value=[];const h=$.validatePaymentData(v);if(h.length>0){l.value=h;return}n("submit",{method:c.value,cardData:{...v}})};return s({submit:I}),(h,r)=>(d(),i("div",pt,[e("div",null,[r[7]||(r[7]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Payment Method",-1)),e("div",ft,[e("label",vt,[C(e("input",{"onUpdate:modelValue":r[0]||(r[0]=m=>c.value=m),type:"radio",value:"card",class:"text-amber-600 focus:ring-amber-500"},null,512),[[pe,c.value]]),r[5]||(r[5]=e("div",{class:"ml-3 flex items-center space-x-2"},[e("svg",{class:"h-6 w-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})]),e("span",{class:"text-sm font-medium text-gray-900"},"Credit or Debit Card")],-1))]),r[6]||(r[6]=G('<label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 opacity-50"><input type="radio" value="paypal" disabled class="text-amber-600 focus:ring-amber-500"><div class="ml-3 flex items-center space-x-2"><svg class="h-6 w-6 text-blue-600" viewBox="0 0 24 24" fill="currentColor"><path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.26-.93 4.778-4.005 6.473-7.955 6.473h-2.05c-.262 0-.48.19-.523.45L8.91 20.395a.384.384 0 0 0 .38.442h2.776c.262 0 .48-.19.523-.45l.548-3.467c.043-.26.261-.45.523-.45h1.31c3.5 0 6.24-1.42 7.04-5.53.33-1.71.16-3.14-.82-4.18z"></path></svg><span class="text-sm font-medium text-gray-900">PayPal</span><span class="text-xs text-gray-500">(Coming Soon)</span></div></label>',1))])]),c.value==="card"?(d(),i("div",yt,[e("div",gt,[r[16]||(r[16]=e("p",{class:"text-sm text-gray-600 mb-4"},[e("strong",null,"Demo Mode:"),L(" Use test card number 4242 4242 4242 4242 with any future expiry date and any 3-digit CVC. ")],-1)),e("form",{onSubmit:O(I,["prevent"]),class:"space-y-4"},[e("div",null,[r[8]||(r[8]=e("label",{for:"cardName",class:"block text-sm font-medium text-gray-700 mb-1"}," Cardholder Name * ",-1)),C(e("input",{id:"cardName","onUpdate:modelValue":r[1]||(r[1]=m=>v.name=m),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent",placeholder:"John Doe"},null,512),[[P,v.name]])]),e("div",null,[r[12]||(r[12]=e("label",{for:"cardNumber",class:"block text-sm font-medium text-gray-700 mb-1"}," Card Number * ",-1)),e("div",bt,[C(e("input",{id:"cardNumber","onUpdate:modelValue":r[2]||(r[2]=m=>v.cardNumber=m),type:"text",required:"",maxlength:"19",class:"w-full px-3 py-2 pr-12 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent",placeholder:"1234 5678 9012 3456",onInput:A},null,544),[[P,v.cardNumber]]),e("div",xt,[u.value==="visa"?(d(),i("svg",ht,r[9]||(r[9]=[e("rect",{width:"40",height:"24",rx:"4",fill:"#1A1F71"},null,-1),e("path",{d:"M16.5 8.5L14.2 15.5H12.1L10.9 10.4C10.8 10 10.6 9.7 10.3 9.5C9.7 9.1 8.9 8.8 8.1 8.6L8.3 8.5H11.8C12.3 8.5 12.7 8.8 12.8 9.3L13.6 13.1L15.7 8.5H16.5ZM21.2 15.5H19.1L20.7 8.5H22.8L21.2 15.5Z",fill:"white"},null,-1)]))):u.value==="mastercard"?(d(),i("svg",_t,r[10]||(r[10]=[e("rect",{width:"40",height:"24",rx:"4",fill:"#EB001B"},null,-1),e("circle",{cx:"15",cy:"12",r:"7",fill:"#FF5F00"},null,-1),e("circle",{cx:"25",cy:"12",r:"7",fill:"#F79E1B"},null,-1)]))):(d(),i("svg",wt,r[11]||(r[11]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"},null,-1)])))])])]),e("div",kt,[e("div",null,[r[13]||(r[13]=e("label",{for:"expiryDate",class:"block text-sm font-medium text-gray-700 mb-1"}," Expiry Date * ",-1)),C(e("input",{id:"expiryDate","onUpdate:modelValue":r[3]||(r[3]=m=>v.expiryDate=m),type:"text",required:"",maxlength:"5",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent",placeholder:"MM/YY",onInput:S},null,544),[[P,v.expiryDate]])]),e("div",null,[r[14]||(r[14]=e("label",{for:"cvc",class:"block text-sm font-medium text-gray-700 mb-1"}," CVC * ",-1)),C(e("input",{id:"cvc","onUpdate:modelValue":r[4]||(r[4]=m=>v.cvc=m),type:"text",required:"",maxlength:"4",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent",placeholder:"123",onInput:E},null,544),[[P,v.cvc]])])]),l.value.length>0?(d(),i("div",St,[e("ul",Ct,[(d(!0),i(j,null,D(l.value,m=>(d(),i("li",{key:m},b(m),1))),128))])])):_("",!0),e("div",$t,[e("p",At,[r[15]||(r[15]=e("strong",null,"Total:",-1)),L(" "+b(f(h.amount))+" ",1),o.value>0?(d(),i("span",Pt," (includes "+b(f(o.value))+" processing fee) ",1)):_("",!0)])])],32)])])):_("",!0)]))}}),It={class:"min-h-screen bg-gray-50"},Vt={class:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},Mt={class:"mb-8"},jt={"aria-label":"Progress"},Dt={class:"flex items-center"},Lt={key:0,class:"absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-300","aria-hidden":"true"},Ft={class:"relative flex items-start group"},Ut={class:"h-9 flex items-center"},Bt={key:0,class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},Nt={key:1},Tt={class:"ml-4 min-w-0 flex flex-col"},qt={class:"text-sm text-gray-500"},Rt={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},zt={class:"lg:col-span-2"},Ht={class:"bg-white rounded-lg shadow-sm p-6"},Ot={key:0,class:"space-y-6"},Gt={class:"flex justify-between pt-6"},Jt=["disabled"],Yt={key:1,class:"space-y-6"},Kt={class:"flex justify-between pt-6"},Wt=["disabled"],Xt={key:0,class:"flex items-center"},Qt={key:1},Zt={key:2,class:"space-y-6"},es={class:"space-y-4"},ts=["src","alt"],ss={class:"flex-1"},rs={class:"font-medium text-gray-900"},os={class:"text-sm text-gray-500"},ns={class:"text-right"},as={class:"font-medium text-gray-900"},ls={class:"text-sm text-gray-500"},is={key:0,class:"p-4 border border-gray-200 rounded-lg"},ds={class:"text-sm text-gray-600"},us={class:"flex justify-between pt-6"},cs=["disabled"],ms={key:0,class:"flex items-center"},ps={key:1},fs={class:"lg:col-span-1"},vs={class:"bg-white rounded-lg shadow-sm p-6 sticky top-8"},ys={class:"space-y-3 mb-4"},gs=["src","alt"],bs={class:"flex-1 min-w-0"},xs={class:"text-sm font-medium text-gray-900 truncate"},hs={class:"text-sm text-gray-500"},_s={class:"text-sm font-medium text-gray-900"},ws={class:"border-t border-gray-200 pt-4 space-y-2"},ks={class:"flex justify-between text-sm"},Ss={class:"text-gray-900"},Cs={class:"border-t border-gray-200 pt-2"},$s={class:"flex justify-between"},As={class:"text-base font-semibold text-gray-900"},Is=J({__name:"CheckoutView",setup(y){const s=be(),t=fe(),a=ve(),n=ye(),c=ee(),l=ge(),v=w(null),u=w(1),o=w(null),f=w(null),A=w(null),S=w(!1),E=[{id:1,name:"Shipping",description:"Delivery address"},{id:2,name:"Payment",description:"Payment method"},{id:3,name:"Review",description:"Confirm order"}],I=F(()=>{switch(u.value){case 1:return!!o.value;case 2:return!!A.value;case 3:return!0;default:return!1}});te(async()=>{var k;if(!t.isAuthenticated){s.push("/auth/login");return}if(a.isEmpty){l.error("Your cart is empty"),s.push("/cart");return}(k=t.user)!=null&&k.id&&(await c.fetchAddresses(t.user.id),c.defaultAddress&&(o.value=c.defaultAddress.id,f.value=c.defaultAddress))});const h=()=>{I.value&&u.value<E.length&&u.value++},r=()=>{u.value>1&&u.value--},m=k=>{f.value=k},x=k=>{A.value=k},U=()=>{v.value&&v.value.submit()},ae=async()=>{var k,p;if(!((k=t.user)!=null&&k.id)||!o.value||!A.value){l.error("Missing required information");return}try{S.value=!0;const B=await $.createPaymentIntent(a.totalAmount,"usd",a.items),V=await $.confirmPayment(B.client_secret,A.value.cardData);if(!V.success){l.error(V.error||"Payment failed");return}const g=await n.createOrder({userId:t.user.id,cartItems:a.items,shippingAddressId:o.value,paymentIntentId:(p=V.paymentIntent)==null?void 0:p.id});g&&(a.clearCart(),s.push(`/orders/${g.id}`))}catch(B){console.error("Error confirming order:",B),l.error("Failed to place order. Please try again.")}finally{S.value=!1}},le=k=>c.formatAddress(k);return(k,p)=>{var V;const B=he("router-link");return d(),i("div",It,[e("div",Vt,[p[14]||(p[14]=e("div",{class:"mb-8"},[e("h1",{class:"text-3xl font-serif font-bold text-gray-900"},"Checkout"),e("p",{class:"text-gray-600 mt-2"}," Complete your order for authentic Tunisian products ")],-1)),e("div",Mt,[e("nav",jt,[e("ol",Dt,[(d(),i(j,null,D(E,(g,N)=>e("li",{key:g.id,class:"relative"},[N!==E.length-1?(d(),i("div",Lt)):_("",!0),e("div",Ft,[e("span",Ut,[e("span",{class:K(["relative z-10 w-8 h-8 flex items-center justify-center rounded-full text-sm font-semibold",[u.value>=N+1?"bg-amber-600 text-white":"bg-white border-2 border-gray-300 text-gray-500"]])},[u.value>N+1?(d(),i("svg",Bt,p[1]||(p[1]=[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"},null,-1)]))):(d(),i("span",Nt,b(N+1),1))],2)]),e("span",Tt,[e("span",{class:K(["text-sm font-medium",u.value>=N+1?"text-amber-600":"text-gray-500"])},b(g.name),3),e("span",qt,b(g.description),1)])])])),64))])])]),e("div",Rt,[e("div",zt,[e("div",Ht,[u.value===1?(d(),i("div",Ot,[p[3]||(p[3]=e("h2",{class:"text-xl font-semibold text-gray-900"}," Shipping Address ",-1)),R(mt,{modelValue:o.value,"onUpdate:modelValue":p[0]||(p[0]=g=>o.value=g),"user-id":((V=M(t).user)==null?void 0:V.id)||"",onAddressSelected:m},null,8,["modelValue","user-id"]),e("div",Gt,[R(B,{to:"/cart",class:"btn-outline"},{default:xe(()=>p[2]||(p[2]=[L(" Back to Cart ")])),_:1,__:[2]}),e("button",{onClick:h,disabled:!o.value,class:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed"}," Continue to Payment ",8,Jt)])])):_("",!0),u.value===2?(d(),i("div",Yt,[p[5]||(p[5]=e("h2",{class:"text-xl font-semibold text-gray-900"}," Payment Information ",-1)),R(Et,{ref_key:"paymentFormRef",ref:v,amount:M(a).totalAmount,loading:S.value,onSubmit:x},null,8,["amount","loading"]),e("div",Kt,[e("button",{onClick:r,class:"btn-outline"}," Back to Shipping "),e("button",{onClick:U,disabled:S.value,class:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed"},[S.value?(d(),i("span",Xt,p[4]||(p[4]=[e("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),L(" Processing... ")]))):(d(),i("span",Qt,"Place Order"))],8,Wt)])])):_("",!0),u.value===3?(d(),i("div",Zt,[p[8]||(p[8]=e("h2",{class:"text-xl font-semibold text-gray-900"}," Review Your Order ",-1)),e("div",es,[(d(!0),i(j,null,D(M(a).items,g=>(d(),i("div",{key:g.product.id,class:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg"},[e("img",{src:g.product.image_url||"https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=100",alt:g.product.name,class:"w-16 h-16 object-cover rounded-md"},null,8,ts),e("div",ss,[e("h3",rs,b(g.product.name),1),e("p",os," Quantity: "+b(g.quantity),1)]),e("div",ns,[e("p",as," $"+b((g.product.price*g.quantity).toFixed(2)),1),e("p",ls," $"+b(g.product.price.toFixed(2))+" each ",1)])]))),128))]),f.value?(d(),i("div",is,[p[6]||(p[6]=e("h3",{class:"font-medium text-gray-900 mb-2"},"Shipping Address",-1)),e("p",ds,b(le(f.value)),1)])):_("",!0),e("div",us,[e("button",{onClick:r,class:"btn-outline"}," Back to Payment "),e("button",{onClick:ae,disabled:S.value,class:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed"},[S.value?(d(),i("span",ms,p[7]||(p[7]=[e("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),L(" Placing Order... ")]))):(d(),i("span",ps,"Confirm Order"))],8,cs)])])):_("",!0)])]),e("div",fs,[e("div",vs,[p[12]||(p[12]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"}," Order Summary ",-1)),e("div",ys,[(d(!0),i(j,null,D(M(a).items,g=>(d(),i("div",{key:g.product.id,class:"flex items-center space-x-3"},[e("img",{src:g.product.image_url||"https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=60",alt:g.product.name,class:"w-12 h-12 object-cover rounded-md"},null,8,gs),e("div",bs,[e("p",xs,b(g.product.name),1),e("p",hs,"Qty: "+b(g.quantity),1)]),e("p",_s," $"+b((g.product.price*g.quantity).toFixed(2)),1)]))),128))]),e("div",ws,[e("div",ks,[p[9]||(p[9]=e("span",{class:"text-gray-600"},"Subtotal",-1)),e("span",Ss,"$"+b(M(a).totalAmount.toFixed(2)),1)]),p[11]||(p[11]=G('<div class="flex justify-between text-sm"><span class="text-gray-600">Shipping</span><span class="text-gray-900">Free</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Tax</span><span class="text-gray-900">Calculated at checkout</span></div>',2)),e("div",Cs,[e("div",$s,[p[10]||(p[10]=e("span",{class:"text-base font-semibold text-gray-900"},"Total",-1)),e("span",As,"$"+b(M(a).totalAmount.toFixed(2)),1)])])]),p[13]||(p[13]=G('<div class="mt-6 p-3 bg-green-50 border border-green-200 rounded-md"><div class="flex items-center"><svg class="h-5 w-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg><span class="text-sm text-green-700">Secure checkout</span></div></div>',1))])])])])])}}});export{Is as default};
