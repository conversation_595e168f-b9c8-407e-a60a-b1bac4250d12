import{d as w,r as g,c as x,o as k,a as r,b as s,e as m,w as p,f as C,F as l,g as d,h as v,u as P,i as o,t as y,j as S,k as T}from"./index-COfeaTnR.js";import{u as V,P as B}from"./ProductCard-CVdyVacO.js";const N={class:"min-h-screen bg-gray-50"},F={class:"bg-gradient-to-r from-primary-600 to-primary-800 text-white"},L={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24"},E={class:"text-center"},j={class:"space-x-4"},D={class:"py-16"},H={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},M={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},q=["onClick"],A={class:"p-6"},O={class:"text-xl font-semibold text-gray-900 mb-2"},R={class:"text-gray-600"},$={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},z={class:"py-16 bg-white"},G={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},I={key:0,class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"},J={key:1,class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"},K={class:"text-center mt-12"},Y=w({__name:"HomeView",setup(Q){const f=P(),i=V(),n=g(!0),c=g(!0),b=x(()=>i.categories),h=x(()=>i.products.slice(0,4).map(a=>({...a,images:a.images?[...a.images]:null}))),_=a=>{f.push({name:"products",query:{category:a}})};return k(async()=>{try{await i.fetchCategories(),n.value=!1,await i.fetchProducts(),c.value=!1}catch(a){console.error("Error loading home page data:",a),n.value=!1,c.value=!1}}),(a,t)=>{const u=C("router-link");return o(),r("div",N,[s("section",F,[s("div",L,[s("div",E,[t[2]||(t[2]=s("h1",{class:"text-4xl md:text-6xl font-serif font-bold mb-6"}," Savory Tunisian Bounty ",-1)),t[3]||(t[3]=s("p",{class:"text-xl md:text-2xl mb-8 max-w-3xl mx-auto"}," Discover the authentic flavors of Tunisia with our premium selection of traditional spices, olive oils, and artisanal products. ",-1)),s("div",j,[m(u,{to:"/products",class:"btn-secondary inline-block text-lg px-8 py-3"},{default:p(()=>t[0]||(t[0]=[v(" Shop Now ")])),_:1,__:[0]}),t[1]||(t[1]=s("button",{class:"btn-outline inline-block text-lg px-8 py-3 border-white text-white hover:bg-white hover:text-primary-600"}," Learn More ",-1))])])])]),s("section",D,[s("div",H,[t[6]||(t[6]=s("div",{class:"text-center mb-12"},[s("h2",{class:"text-3xl font-serif font-bold text-gray-900 mb-4"}," Explore Our Categories "),s("p",{class:"text-lg text-gray-600 max-w-2xl mx-auto"}," From aromatic spices to golden olive oils, discover the treasures of Tunisian cuisine ")],-1)),n.value?(o(),r("div",$,[(o(),r(l,null,d(6,e=>s("div",{key:e,class:"card animate-pulse"},t[5]||(t[5]=[s("div",{class:"h-48 bg-gray-200"},null,-1),s("div",{class:"p-6"},[s("div",{class:"h-6 bg-gray-200 rounded mb-2"}),s("div",{class:"h-4 bg-gray-200 rounded"})],-1)]))),64))])):(o(),r("div",M,[(o(!0),r(l,null,d(b.value,e=>(o(),r("div",{key:e.id,class:"card hover:shadow-lg transition-shadow duration-300 cursor-pointer",onClick:U=>_(e.id)},[t[4]||(t[4]=s("div",{class:"h-48 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center"},[s("div",{class:"text-6xl"},"🌿")],-1)),s("div",A,[s("h3",O,y(e.name),1),s("p",R,y(e.description),1)])],8,q))),128))]))])]),s("section",z,[s("div",G,[t[9]||(t[9]=s("div",{class:"text-center mb-12"},[s("h2",{class:"text-3xl font-serif font-bold text-gray-900 mb-4"}," Featured Products "),s("p",{class:"text-lg text-gray-600 max-w-2xl mx-auto"}," Hand-picked selections from our finest Tunisian producers ")],-1)),c.value?(o(),r("div",J,[(o(),r(l,null,d(4,e=>s("div",{key:e,class:"card animate-pulse"},t[7]||(t[7]=[T('<div class="h-48 bg-gray-200"></div><div class="p-4"><div class="h-6 bg-gray-200 rounded mb-2"></div><div class="h-4 bg-gray-200 rounded mb-2"></div><div class="h-6 bg-gray-200 rounded w-20"></div></div>',2)]))),64))])):(o(),r("div",I,[(o(!0),r(l,null,d(h.value,e=>(o(),S(B,{key:e.id,product:e},null,8,["product"]))),128))])),s("div",K,[m(u,{to:"/products",class:"btn-primary text-lg px-8 py-3"},{default:p(()=>t[8]||(t[8]=[v(" View All Products ")])),_:1,__:[8]})])])])])}}});export{Y as default};
