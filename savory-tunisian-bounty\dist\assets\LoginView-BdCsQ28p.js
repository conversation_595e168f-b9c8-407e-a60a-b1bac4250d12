import{d as q,B,r as n,C as g,a as r,b as e,m,D as _,n as y,v as x,p as h,t as b,E,k as F,e as L,w as N,f as R,h as D,s as T,u as U,i as l}from"./index-COfeaTnR.js";const j={class:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8"},z={class:"mt-8 sm:mx-auto sm:w-full sm:max-w-md"},A={class:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10"},$={class:"mt-1"},W={key:0,class:"mt-1 text-sm text-red-600"},G={class:"mt-1"},H={key:0,class:"mt-1 text-sm text-red-600"},I={class:"flex items-center justify-between"},J={class:"flex items-center"},K={class:"text-sm"},O=["disabled"],Q={key:0},X={key:1},Y={key:0,class:"rounded-md bg-red-50 p-4"},Z={class:"text-sm text-red-700"},ee={class:"mt-6"},se={class:"mt-6"},te={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},oe={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},ae={class:"mt-3"},re={class:"mb-4"},le={class:"flex space-x-3"},ie=["disabled"],de={key:0},ne={key:1},ue={key:0,class:"mt-4 text-sm text-green-600"},pe=q({__name:"LoginView",setup(me){const k=U(),P=T(),w=B(),c=n(!1),d=n(""),p=n(!1),v=n(""),f=n(!1),u=n(""),o=g({email:"",password:"",rememberMe:!1}),a=g({email:"",password:""}),S=()=>(a.email="",a.password="",o.email?o.password?o.password.length<6?(a.password="Password must be at least 6 characters",!1):!0:(a.password="Password is required",!1):(a.email="Email is required",!1)),C=async()=>{if(S())try{c.value=!0,d.value="";const{error:t}=await w.signIn(o.email,o.password);if(t){d.value=(t==null?void 0:t.message)||"Login failed";return}const s=P.query.redirect||"/";k.push(s)}catch(t){d.value="An unexpected error occurred",console.error("Login error:",t)}finally{c.value=!1}},V=async()=>{try{f.value=!0,u.value="";const{error:t}=await w.resetPassword(v.value);if(t){d.value=(t==null?void 0:t.message)||"Password reset failed";return}u.value="Password reset link sent to your email",setTimeout(()=>{p.value=!1,v.value="",u.value=""},3e3)}catch(t){d.value="Failed to send reset email",console.error("Password reset error:",t)}finally{f.value=!1}};return(t,s)=>{const M=R("router-link");return l(),r("div",j,[s[13]||(s[13]=e("div",{class:"sm:mx-auto sm:w-full sm:max-w-md"},[e("div",{class:"text-center"},[e("h2",{class:"text-3xl font-serif font-bold text-gray-900"}," Welcome Back "),e("p",{class:"mt-2 text-sm text-gray-600"}," Sign in to your account to continue shopping ")])],-1)),e("div",z,[e("div",A,[e("form",{onSubmit:_(C,["prevent"]),class:"space-y-6"},[e("div",null,[s[6]||(s[6]=e("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Email address ",-1)),e("div",$,[y(e("input",{id:"email","onUpdate:modelValue":s[0]||(s[0]=i=>o.email=i),type:"email",autocomplete:"email",required:"",class:h(["input-field",{"border-red-500":a.email}])},null,2),[[x,o.email]]),a.email?(l(),r("p",W,b(a.email),1)):m("",!0)])]),e("div",null,[s[7]||(s[7]=e("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Password ",-1)),e("div",G,[y(e("input",{id:"password","onUpdate:modelValue":s[1]||(s[1]=i=>o.password=i),type:"password",autocomplete:"current-password",required:"",class:h(["input-field",{"border-red-500":a.password}])},null,2),[[x,o.password]]),a.password?(l(),r("p",H,b(a.password),1)):m("",!0)])]),e("div",I,[e("div",J,[y(e("input",{id:"remember-me","onUpdate:modelValue":s[2]||(s[2]=i=>o.rememberMe=i),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[E,o.rememberMe]]),s[8]||(s[8]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Remember me ",-1))]),e("div",K,[e("button",{type:"button",onClick:s[3]||(s[3]=i=>p.value=!0),class:"font-medium text-primary-600 hover:text-primary-500"}," Forgot your password? ")])]),e("div",null,[e("button",{type:"submit",disabled:c.value,class:"w-full btn-primary py-3 disabled:opacity-50 disabled:cursor-not-allowed"},[c.value?(l(),r("span",Q,"Signing in...")):(l(),r("span",X,"Sign in"))],8,O)]),d.value?(l(),r("div",Y,[e("div",Z,b(d.value),1)])):m("",!0)],32),e("div",ee,[s[10]||(s[10]=F('<div class="relative"><div class="absolute inset-0 flex items-center"><div class="w-full border-t border-gray-300"></div></div><div class="relative flex justify-center text-sm"><span class="px-2 bg-white text-gray-500">Don&#39;t have an account?</span></div></div>',1)),e("div",se,[L(M,{to:"/register",class:"w-full btn-outline text-center block py-3"},{default:N(()=>s[9]||(s[9]=[D(" Create new account ")])),_:1,__:[9]})])])])]),p.value?(l(),r("div",te,[e("div",oe,[e("div",ae,[s[12]||(s[12]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Reset Password",-1)),e("form",{onSubmit:_(V,["prevent"])},[e("div",re,[s[11]||(s[11]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Email address ",-1)),y(e("input",{"onUpdate:modelValue":s[4]||(s[4]=i=>v.value=i),type:"email",required:"",class:"input-field",placeholder:"Enter your email"},null,512),[[x,v.value]])]),e("div",le,[e("button",{type:"submit",disabled:f.value,class:"flex-1 btn-primary py-2 disabled:opacity-50"},[f.value?(l(),r("span",de,"Sending...")):(l(),r("span",ne,"Send Reset Link"))],8,ie),e("button",{type:"button",onClick:s[5]||(s[5]=i=>p.value=!1),class:"flex-1 btn-outline py-2"}," Cancel ")])],32),u.value?(l(),r("div",ue,b(u.value),1)):m("",!0)])])])):m("",!0)])}}});export{pe as default};
