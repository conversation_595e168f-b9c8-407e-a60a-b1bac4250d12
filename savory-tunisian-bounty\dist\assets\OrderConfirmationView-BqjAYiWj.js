import{d as V,B as A,J as N,K as B,r as v,c as P,s as D,o as I,u as L,a,b as t,e as u,w as c,f as T,t as r,p as b,F as $,g as E,m as h,k as M,L as U,h as m,i as n}from"./index-COfeaTnR.js";const W={class:"min-h-screen bg-gray-50"},z={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},R={key:0,class:"text-center py-12"},q={key:1,class:"text-center py-12"},H={key:2,class:"space-y-8"},J={class:"bg-white rounded-lg shadow-sm overflow-hidden"},K={class:"bg-amber-50 px-6 py-4 border-b border-amber-200"},Q={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},G={class:"text-xl font-semibold text-gray-900"},X={class:"text-sm text-gray-600 mt-1"},Y={class:"mt-4 sm:mt-0"},Z={class:"p-6"},tt={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},et={class:"space-y-4"},st=["src","alt"],ot={class:"flex-1"},rt={class:"font-medium text-gray-900"},at={class:"text-sm text-gray-500"},nt={class:"text-sm text-gray-500"},lt={class:"text-right"},dt={class:"font-medium text-gray-900"},it={class:"space-y-6"},ut={key:0},ct={class:"p-4 border border-gray-200 rounded-lg bg-gray-50"},mt={class:"text-sm text-gray-900"},xt={class:"p-4 border border-gray-200 rounded-lg bg-gray-50"},pt={class:"flex items-center justify-between"},yt={key:0,class:"mt-2"},_t={class:"text-sm text-gray-600"},gt={class:"p-4 border border-gray-200 rounded-lg bg-gray-50 space-y-2"},ft={class:"flex justify-between text-sm"},vt={class:"text-gray-900"},bt={class:"border-t border-gray-300 pt-2"},ht={class:"flex justify-between"},wt={class:"text-base font-semibold text-gray-900"},St={class:"flex flex-col sm:flex-row gap-4 justify-center"},kt={class:"bg-blue-50 border border-blue-200 rounded-md p-4"},Ot={class:"flex items-start"},Ct={class:"text-sm text-blue-700 mt-1"},Vt=V({__name:"OrderConfirmationView",setup(jt){const w=D(),x=L(),p=A(),d=N(),y=B(),_=v(!0),o=v(null),g=P(()=>w.params.id);I(async()=>{try{if(!p.isAuthenticated){x.push("/auth/login");return}if(!g.value){x.push("/orders");return}const s=await d.fetchOrderById(g.value);s?o.value=s:y.error("Order not found")}catch(s){console.error("Error loading order:",s),y.error("Failed to load order details")}finally{_.value=!1}});const S=s=>d.formatOrderNumber(s),k=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),O=s=>d.getOrderStatusColor(s),C=s=>d.getPaymentStatusColor(s),j=s=>[s.address_line_1,s.address_line_2,s.city,s.postal_code,s.country].filter(Boolean).join(", "),F=()=>o.value?o.value.order_items.reduce((s,e)=>s+e.total_price,0):0;return(s,e)=>{var f;const i=T("router-link");return n(),a("div",W,[t("div",z,[_.value?(n(),a("div",R,e[0]||(e[0]=[t("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto"},null,-1),t("p",{class:"mt-4 text-gray-600"},"Loading order details...",-1)]))):o.value?(n(),a("div",H,[e[17]||(e[17]=t("div",{class:"text-center"},[t("div",{class:"text-6xl mb-4"},"✅"),t("h1",{class:"text-3xl font-serif font-bold text-gray-900 mb-2"},"Order Confirmed!"),t("p",{class:"text-lg text-gray-600"}," Thank you for your order. We'll send you a confirmation email shortly. ")],-1)),t("div",J,[t("div",K,[t("div",Q,[t("div",null,[t("h2",G," Order "+r(S(o.value)),1),t("p",X," Placed on "+r(k(o.value.created_at)),1)]),t("div",Y,[t("span",{class:b(["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",O(o.value.status)])},r(o.value.status.charAt(0).toUpperCase()+o.value.status.slice(1)),3)])])]),t("div",Z,[t("div",tt,[t("div",null,[e[5]||(e[5]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Order Items",-1)),t("div",et,[(n(!0),a($,null,E(o.value.order_items,l=>(n(),a("div",{key:l.id,class:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg"},[t("img",{src:l.products.image_url||"https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=80",alt:l.products.name,class:"w-16 h-16 object-cover rounded-md"},null,8,st),t("div",ot,[t("h4",rt,r(l.products.name),1),t("p",at,"Quantity: "+r(l.quantity),1),t("p",nt,"$"+r(l.unit_price.toFixed(2))+" each",1)]),t("div",lt,[t("p",dt,"$"+r(l.total_price.toFixed(2)),1)])]))),128))])]),t("div",it,[o.value.user_addresses?(n(),a("div",ut,[e[6]||(e[6]=t("h3",{class:"text-lg font-medium text-gray-900 mb-3"},"Shipping Address",-1)),t("div",ct,[t("p",mt,r(j(o.value.user_addresses)),1)])])):h("",!0),t("div",null,[e[8]||(e[8]=t("h3",{class:"text-lg font-medium text-gray-900 mb-3"},"Payment",-1)),t("div",xt,[t("div",pt,[e[7]||(e[7]=t("span",{class:"text-sm text-gray-600"},"Payment Status",-1)),t("span",{class:b(["inline-flex items-center px-2 py-1 rounded text-xs font-medium",C(o.value.payment_status)])},r(o.value.payment_status.charAt(0).toUpperCase()+o.value.payment_status.slice(1)),3)]),o.value.payment_intent_id?(n(),a("div",yt,[t("span",_t,"Payment ID: "+r(o.value.payment_intent_id),1)])):h("",!0)])]),t("div",null,[e[12]||(e[12]=t("h3",{class:"text-lg font-medium text-gray-900 mb-3"},"Order Summary",-1)),t("div",gt,[t("div",ft,[e[9]||(e[9]=t("span",{class:"text-gray-600"},"Subtotal",-1)),t("span",vt,"$"+r(F().toFixed(2)),1)]),e[11]||(e[11]=M('<div class="flex justify-between text-sm"><span class="text-gray-600">Shipping</span><span class="text-gray-900">Free</span></div><div class="flex justify-between text-sm"><span class="text-gray-600">Tax</span><span class="text-gray-900">$0.00</span></div>',2)),t("div",bt,[t("div",ht,[e[10]||(e[10]=t("span",{class:"text-base font-semibold text-gray-900"},"Total",-1)),t("span",wt,"$"+r(o.value.total_amount.toFixed(2)),1)])])])])])])])]),t("div",St,[u(i,{to:"/orders",class:"btn-outline text-center"},{default:c(()=>e[13]||(e[13]=[m(" View All Orders ")])),_:1,__:[13]}),u(i,{to:"/products",class:"btn-primary text-center"},{default:c(()=>e[14]||(e[14]=[m(" Continue Shopping ")])),_:1,__:[14]})]),t("div",kt,[t("div",Ot,[e[16]||(e[16]=t("svg",{class:"h-5 w-5 text-blue-600 mt-0.5 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),t("div",null,[e[15]||(e[15]=t("h3",{class:"text-sm font-medium text-blue-800"},"Order Confirmation Email",-1)),t("p",Ct," We've sent a confirmation email to "+r((f=U(p).user)==null?void 0:f.email)+". If you don't see it in your inbox, please check your spam folder. ",1)])])])])):(n(),a("div",q,[e[2]||(e[2]=t("div",{class:"text-6xl mb-4"},"❌",-1)),e[3]||(e[3]=t("h2",{class:"text-2xl font-semibold text-gray-900 mb-4"},"Order Not Found",-1)),e[4]||(e[4]=t("p",{class:"text-gray-600 mb-8"}," We couldn't find the order you're looking for. ",-1)),u(i,{to:"/orders",class:"btn-primary"},{default:c(()=>e[1]||(e[1]=[m(" View All Orders ")])),_:1,__:[1]})]))])])}}});export{Vt as default};
