import{d as V,B as I,J as A,x as B,K as F,c as p,o as L,u as $,a as o,b as e,e as g,w as y,f as j,m as l,F as f,g as h,h as b,t as n,p as q,i as a}from"./index-COfeaTnR.js";const U={class:"min-h-screen bg-gray-50"},E={class:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},M={key:0,class:"text-center py-12"},R={key:1,class:"text-center py-12"},T={key:2,class:"space-y-6"},Y={class:"bg-gray-50 px-6 py-4 border-b border-gray-200"},z={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},H={class:"text-lg font-semibold text-gray-900"},J={class:"text-sm text-gray-600 mt-1"},K={class:"mt-4 sm:mt-0 flex items-center space-x-4"},P={class:"text-lg font-semibold text-gray-900"},Q={class:"p-6"},G={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6"},W=["src","alt"],X={class:"flex-1 min-w-0"},Z={class:"text-sm font-medium text-gray-900 truncate"},tt={class:"text-sm text-gray-500"},et={key:0,class:"flex items-center justify-center text-sm text-gray-500"},st={class:"flex flex-col sm:flex-row gap-3"},rt=["onClick"],ot=["onClick"],at={key:0,class:"text-center"},it=V({__name:"OrdersView",setup(nt){const _=$(),u=I(),d=A(),v=B(),m=F(),w=p(()=>d.loading),x=p(()=>d.orders),S=p(()=>d.hasOrders);L(async()=>{var r;if(!u.isAuthenticated){_.push("/auth/login");return}(r=u.user)!=null&&r.id&&await d.fetchUserOrders(u.user.id)});const O=r=>d.formatOrderNumber(r),k=r=>new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),C=r=>d.getOrderStatusColor(r),D=async r=>{try{for(const t of r.order_items){const c={id:t.products.id,name:t.products.name,description:null,price:t.products.price,stock_quantity:100,category_id:null,image_url:t.products.image_url,images:null,is_active:!0,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};v.addItem(c,t.quantity)}m.success("Items added to cart!"),_.push("/cart")}catch(t){console.error("Error reordering items:",t),m.error("Failed to add items to cart")}},N=async r=>{confirm("Are you sure you want to cancel this order?")&&await d.updateOrderStatus(r,"cancelled")&&m.success("Order cancelled successfully")};return(r,t)=>{const c=j("router-link");return a(),o("div",U,[e("div",E,[t[7]||(t[7]=e("h1",{class:"text-3xl font-serif font-bold text-gray-900 mb-8"}," Order History ",-1)),w.value?(a(),o("div",M,t[0]||(t[0]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto"},null,-1),e("p",{class:"mt-4 text-gray-600"},"Loading your orders...",-1)]))):S.value?(a(),o("div",T,[(a(!0),o(f,null,h(x.value,s=>(a(),o("div",{key:s.id,class:"bg-white rounded-lg shadow-sm overflow-hidden"},[e("div",Y,[e("div",z,[e("div",null,[e("h3",H," Order "+n(O(s)),1),e("p",J," Placed on "+n(k(s.created_at)),1)]),e("div",K,[e("span",{class:q(["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",C(s.status)])},n(s.status.charAt(0).toUpperCase()+s.status.slice(1)),3),e("span",P," $"+n(s.total_amount.toFixed(2)),1)])])]),e("div",Q,[e("div",G,[(a(!0),o(f,null,h(s.order_items.slice(0,3),i=>(a(),o("div",{key:i.id,class:"flex items-center space-x-3"},[e("img",{src:i.products.image_url||"https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=60",alt:i.products.name,class:"w-12 h-12 object-cover rounded-md"},null,8,W),e("div",X,[e("p",Z,n(i.products.name),1),e("p",tt,"Qty: "+n(i.quantity),1)])]))),128)),s.order_items.length>3?(a(),o("div",et," +"+n(s.order_items.length-3)+" more item"+n(s.order_items.length-3>1?"s":""),1)):l("",!0)]),e("div",st,[g(c,{to:`/orders/${s.id}`,class:"btn-outline text-center"},{default:y(()=>t[5]||(t[5]=[b(" View Details ")])),_:2,__:[5]},1032,["to"]),s.status==="delivered"?(a(),o("button",{key:0,onClick:i=>D(s),class:"btn-primary"}," Reorder ",8,rt)):l("",!0),s.status==="pending"||s.status==="confirmed"?(a(),o("button",{key:1,onClick:i=>N(s.id),class:"text-sm text-red-600 hover:text-red-700 px-4 py-2"}," Cancel Order ",8,ot)):l("",!0)])])]))),128)),x.value.length>=10?(a(),o("div",at,t[6]||(t[6]=[e("button",{class:"btn-outline"},"Load More Orders",-1)]))):l("",!0)])):(a(),o("div",R,[t[2]||(t[2]=e("div",{class:"text-6xl mb-4"},"📦",-1)),t[3]||(t[3]=e("h2",{class:"text-2xl font-semibold text-gray-900 mb-4"},"No Orders Yet",-1)),t[4]||(t[4]=e("p",{class:"text-gray-600 mb-8"}," You haven't placed any orders yet. Start shopping to see your order history here. ",-1)),g(c,{to:"/products",class:"btn-primary"},{default:y(()=>t[1]||(t[1]=[b(" Start Shopping ")])),_:1,__:[1]})]))])])}}});export{it as default};
