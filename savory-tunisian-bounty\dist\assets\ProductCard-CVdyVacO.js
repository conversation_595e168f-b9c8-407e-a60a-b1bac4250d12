import{y as E,r as p,c as w,z as m,A as _,d as I,x as q,a as y,b as r,m as B,t as g,u as S,i as v,_ as L}from"./index-COfeaTnR.js";const U=E("products",()=>{const i=p([]),a=p([]),n=p(!1),c=p(null),d=p(""),b=w(()=>{let t=i.value;if(c.value&&(t=t.filter(e=>e.category_id===c.value)),d.value){const e=d.value.toLowerCase();t=t.filter(s=>{var l;return s.name.toLowerCase().includes(e)||((l=s.description)==null?void 0:l.toLowerCase().includes(e))})}return t}),C=w(()=>{const t={};return a.value.forEach(e=>{t[e.id]=i.value.filter(s=>s.category_id===e.id)}),t}),u=async()=>{try{n.value=!0;const{data:t,error:e}=await _.from("products").select(`
          *,
          categories (
            id,
            name,
            description
          )
        `).eq("is_active",!0).order("name");if(e)throw e;i.value=(t||[]).map(s=>({...s,images:s.images?[...s.images]:null}))}catch(t){console.error("Error fetching products:",t)}finally{n.value=!1}},h=async()=>{try{const{data:t,error:e}=await _.from("categories").select("*").order("name");if(e)throw e;a.value=t||[]}catch(t){console.error("Error fetching categories:",t)}},o=async t=>{try{const{data:e,error:s}=await _.from("products").select(`
          *,
          categories (
            id,
            name,
            description
          )
        `).eq("id",t).eq("is_active",!0).single();if(s)throw s;return e?{...e,images:e.images?[...e.images]:null}:null}catch(e){return console.error("Error fetching product:",e),null}},f=async t=>{try{if(n.value=!0,d.value=t,!t.trim()){await u();return}const{data:e,error:s}=await _.from("products").select(`
          *,
          categories (
            id,
            name,
            description
          )
        `).eq("is_active",!0).or(`name.ilike.%${t}%,description.ilike.%${t}%`).order("name");if(s)throw s;i.value=(e||[]).map(l=>({...l,images:l.images?[...l.images]:null}))}catch(e){console.error("Error searching products:",e)}finally{n.value=!1}},x=t=>{c.value=t},k=()=>{c.value=null,d.value=""},P=async()=>{await Promise.all([u(),h()])};return{products:m(i),categories:m(a),loading:m(n),selectedCategory:m(c),searchQuery:m(d),filteredProducts:b,productsByCategory:C,fetchProducts:u,fetchCategories:h,getProductById:o,searchProducts:f,filterByCategory:x,clearFilters:k,initialize:P}}),Q={class:"card hover:shadow-lg transition-shadow duration-300"},j={class:"relative"},V=["src","alt"],$={class:"absolute top-2 right-2"},z={key:0,class:"bg-red-500 text-white text-xs px-2 py-1 rounded"},A={class:"p-4"},D={class:"text-lg font-semibold text-gray-900 mb-2 line-clamp-2"},F={class:"text-gray-600 text-sm mb-3 line-clamp-2"},N={class:"flex items-center justify-between mb-4"},R={class:"text-2xl font-bold text-primary-600"},T={class:"text-sm text-gray-500"},G={class:"flex space-x-2"},H=["disabled"],J={key:0},K={key:1},M=I({__name:"ProductCard",props:{product:{}},setup(i){const a=i,n=S(),c=q(),d=w(()=>c.isInCart(a.product.id)),b=w(()=>c.getItemQuantity(a.product.id)),C=()=>{n.push({name:"product-detail",params:{id:a.product.id}})},u=()=>{a.product.stock_quantity>0&&c.addItem(a.product,1)},h=o=>{const f=o.target;f.src="https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400"};return(o,f)=>(v(),y("div",Q,[r("div",j,[r("img",{src:o.product.image_url||"/placeholder-product.jpg",alt:o.product.name,class:"w-full h-48 object-cover",onError:h},null,40,V),r("div",$,[o.product.stock_quantity<=5?(v(),y("span",z," Low Stock ")):B("",!0)])]),r("div",A,[r("h3",D,g(o.product.name),1),r("p",F,g(o.product.description),1),r("div",N,[r("span",R," $"+g(o.product.price.toFixed(2)),1),r("span",T,g(o.product.stock_quantity)+" in stock ",1)]),r("div",G,[r("button",{onClick:C,class:"flex-1 btn-outline text-sm py-2"}," View Details "),r("button",{onClick:u,disabled:o.product.stock_quantity===0,class:"flex-1 btn-primary text-sm py-2 disabled:opacity-50 disabled:cursor-not-allowed"},[d.value?(v(),y("span",J,"In Cart ("+g(b.value)+")",1)):(v(),y("span",K,"Add to Cart"))],8,H)])])]))}}),W=L(M,[["__scopeId","data-v-e4dd1850"]]);export{W as P,U as u};
