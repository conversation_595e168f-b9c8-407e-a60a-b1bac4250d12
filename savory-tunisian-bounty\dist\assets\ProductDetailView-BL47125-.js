import{d as T,x as $,r as p,c as v,o as H,s as O,a,k as z,b as t,m as g,e as f,w as _,f as Q,t as r,F as I,g as j,h as b,p as U,j as D,i as l}from"./index-COfeaTnR.js";import{u as K,P as R}from"./ProductCard-CVdyVacO.js";const W={class:"min-h-screen bg-gray-50"},Y={key:0,class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},G={key:1,class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},J={class:"mb-8"},X={class:"flex items-center space-x-2 text-sm text-gray-500"},Z={class:"text-gray-900"},tt={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},et={class:"aspect-square bg-gray-100 rounded-lg overflow-hidden mb-4 relative group"},st=["src","alt"],ot={key:0,class:"absolute inset-0 flex items-center justify-between p-4 opacity-0 group-hover:opacity-100 transition-opacity"},lt={key:1,class:"absolute bottom-4 right-4 bg-black/50 text-white text-sm px-2 py-1 rounded"},at={key:0,class:"grid grid-cols-4 gap-2"},rt=["onClick"],it=["src","alt"],nt={key:0,class:"aspect-square bg-gray-100 rounded-lg overflow-hidden flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors"},dt={class:"text-center"},ut={class:"text-xs text-gray-500"},ct={class:"text-3xl font-serif font-bold text-gray-900 mb-4"},vt={class:"flex items-center space-x-4 mb-6"},gt={class:"text-3xl font-bold text-primary-600"},mt={key:0,class:"bg-red-100 text-red-800 text-sm px-2 py-1 rounded"},pt={key:1,class:"bg-green-100 text-green-800 text-sm px-2 py-1 rounded"},xt={key:2,class:"bg-gray-100 text-gray-800 text-sm px-2 py-1 rounded"},yt={class:"prose prose-gray max-w-none mb-8"},ht={class:"space-y-6"},ft={class:"flex items-center space-x-3"},_t={class:"w-16 text-center font-medium text-lg"},bt=["disabled"],kt={class:"flex space-x-4"},wt=["disabled"],Ct={key:0},Bt={key:1},Mt={key:2},zt={key:0,class:"bg-green-50 border border-green-200 rounded-lg p-4"},It={class:"flex items-center"},jt={class:"text-green-800"},qt={class:"mt-8 border-t border-gray-200 pt-8"},Lt={class:"space-y-3"},St={class:"flex"},Pt={class:"text-sm text-gray-900"},Nt={class:"flex"},At={class:"text-sm text-gray-900"},Vt={class:"flex"},Et={class:"text-sm text-gray-900"},Ft={class:"flex"},Tt={class:"text-sm text-gray-900"},$t={class:"flex"},Ht={class:"text-sm text-gray-900"},Ot={key:0,class:"mt-16 border-t border-gray-200 pt-16"},Qt={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"},Ut={key:2,class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center"},Wt=T({__name:"ProductDetailView",props:{id:{}},setup(q){const L=q,S=O(),m=K(),h=$(),s=p(null),k=p(!0),u=p(1),x=p(!1),n=p(0),w=v(()=>s.value?h.isInCart(s.value.id):!1),C=v(()=>s.value?h.getItemQuantity(s.value.id):0),P=v(()=>{var e;if(!((e=s.value)!=null&&e.category_id))return"Uncategorized";const o=m.categories.find(c=>{var d;return c.id===((d=s.value)==null?void 0:d.category_id)});return(o==null?void 0:o.name)||"Uncategorized"}),i=v(()=>{if(!s.value)return[];const o=[];return s.value.image_url&&o.push(s.value.image_url),s.value.images&&Array.isArray(s.value.images)&&o.push(...s.value.images),o.length===0&&o.push("https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400"),o}),N=v(()=>i.value[n.value]||i.value[0]),B=v(()=>!s.value||!s.value.category_id?[]:m.products.filter(o=>{var e,c;return o.category_id===((e=s.value)==null?void 0:e.category_id)&&o.id!==((c=s.value)==null?void 0:c.id)&&o.is_active}).slice(0,4).map(o=>({...o,images:o.images?[...o.images]:null}))),A=async()=>{if(!(!s.value||s.value.stock_quantity===0))try{x.value=!0,h.addItem(s.value,u.value),u.value=1}catch(o){console.error("Error adding to cart:",o)}finally{x.value=!1}},V=o=>{n.value=o},E=()=>{n.value=(n.value+1)%i.value.length},F=()=>{n.value=n.value===0?i.value.length-1:n.value-1},M=o=>{const e=o.target;e.src="https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400"};return H(async()=>{try{const o=L.id||S.params.id;m.categories.length===0&&await m.fetchCategories(),s.value=await m.getProductById(o)}catch(o){console.error("Error loading product:",o)}finally{k.value=!1}}),(o,e)=>{const c=Q("router-link");return l(),a("div",W,[k.value?(l(),a("div",Y,e[2]||(e[2]=[z('<div class="animate-pulse"><div class="grid grid-cols-1 lg:grid-cols-2 gap-8"><div class="h-96 bg-gray-200 rounded-lg"></div><div class="space-y-4"><div class="h-8 bg-gray-200 rounded"></div><div class="h-4 bg-gray-200 rounded w-3/4"></div><div class="h-6 bg-gray-200 rounded w-1/4"></div></div></div></div>',1)]))):s.value?(l(),a("div",G,[t("nav",J,[t("ol",X,[t("li",null,[f(c,{to:"/",class:"hover:text-primary-600"},{default:_(()=>e[3]||(e[3]=[b("Home")])),_:1,__:[3]})]),e[5]||(e[5]=t("li",null,"/",-1)),t("li",null,[f(c,{to:"/products",class:"hover:text-primary-600"},{default:_(()=>e[4]||(e[4]=[b("Products")])),_:1,__:[4]})]),e[6]||(e[6]=t("li",null,"/",-1)),t("li",Z,r(s.value.name),1)])]),t("div",tt,[t("div",null,[t("div",et,[t("img",{src:N.value,alt:s.value.name,class:"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105",onError:M},null,40,st),i.value.length>1?(l(),a("div",ot,[t("button",{onClick:F,class:"bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-colors"},e[7]||(e[7]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),t("button",{onClick:E,class:"bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-colors"},e[8]||(e[8]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))])):g("",!0),i.value.length>1?(l(),a("div",lt,r(n.value+1)+" / "+r(i.value.length),1)):g("",!0)]),i.value.length>1?(l(),a("div",at,[(l(!0),a(I,null,j(i.value.slice(0,4),(d,y)=>(l(),a("div",{key:y,onClick:Dt=>V(y),class:U(["aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer transition-all duration-200",n.value===y?"ring-2 ring-primary-500 ring-offset-2":"hover:opacity-75 hover:ring-1 hover:ring-gray-300"])},[t("img",{src:d,alt:`${s.value.name} ${y+1}`,class:"w-full h-full object-cover",onError:M},null,40,it)],10,rt))),128)),i.value.length>4?(l(),a("div",nt,[t("div",dt,[e[9]||(e[9]=t("svg",{class:"w-6 h-6 mx-auto mb-1 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),t("span",ut,"+"+r(i.value.length-4),1)])])):g("",!0)])):g("",!0)]),t("div",null,[t("h1",ct,r(s.value.name),1),t("div",vt,[t("span",gt," $"+r(s.value.price.toFixed(2)),1),s.value.stock_quantity<=5?(l(),a("span",mt," Only "+r(s.value.stock_quantity)+" left! ",1)):s.value.stock_quantity>0?(l(),a("span",pt," In Stock ")):(l(),a("span",xt," Out of Stock "))]),t("div",yt,[t("p",null,r(s.value.description),1)]),t("div",ht,[t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Quantity ",-1)),t("div",ft,[t("button",{onClick:e[0]||(e[0]=d=>u.value=Math.max(1,u.value-1)),class:"w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"},e[10]||(e[10]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 12H4"})],-1)])),t("span",_t,r(u.value),1),t("button",{onClick:e[1]||(e[1]=d=>u.value=Math.min(s.value.stock_quantity,u.value+1)),disabled:u.value>=s.value.stock_quantity,class:"w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"},e[11]||(e[11]=[t("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)]),8,bt)])]),t("div",kt,[t("button",{onClick:A,disabled:s.value.stock_quantity===0||x.value,class:"flex-1 btn-primary py-3 text-lg disabled:opacity-50 disabled:cursor-not-allowed"},[x.value?(l(),a("span",Ct,"Adding...")):w.value?(l(),a("span",Bt,"Add More to Cart")):(l(),a("span",Mt,"Add to Cart"))],8,wt),e[13]||(e[13]=t("button",{class:"btn-outline px-6 py-3"},[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])],-1))]),w.value?(l(),a("div",zt,[t("div",It,[e[14]||(e[14]=t("svg",{class:"w-5 h-5 text-green-400 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})],-1)),t("span",jt,r(C.value)+" item"+r(C.value!==1?"s":"")+" in cart ",1)])])):g("",!0)]),t("div",qt,[e[20]||(e[20]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"}," Product Details ",-1)),t("dl",Lt,[t("div",St,[e[15]||(e[15]=t("dt",{class:"w-1/3 text-sm font-medium text-gray-500"},"SKU:",-1)),t("dd",Pt,r(s.value.id.slice(0,8).toUpperCase()),1)]),t("div",Nt,[e[16]||(e[16]=t("dt",{class:"w-1/3 text-sm font-medium text-gray-500"},"Stock:",-1)),t("dd",At,r(s.value.stock_quantity)+" units ",1)]),t("div",Vt,[e[17]||(e[17]=t("dt",{class:"w-1/3 text-sm font-medium text-gray-500"}," Category: ",-1)),t("dd",Et,r(P.value),1)]),t("div",Ft,[e[18]||(e[18]=t("dt",{class:"w-1/3 text-sm font-medium text-gray-500"},"Weight:",-1)),t("dd",Tt,r(s.value.weight||"N/A"),1)]),t("div",$t,[e[19]||(e[19]=t("dt",{class:"w-1/3 text-sm font-medium text-gray-500"},"Origin:",-1)),t("dd",Ht,r(s.value.origin||"Tunisia"),1)])])]),e[21]||(e[21]=z('<div class="mt-8 border-t border-gray-200 pt-8"><h3 class="text-lg font-medium text-gray-900 mb-4"> Features &amp; Benefits </h3><div class="grid grid-cols-1 sm:grid-cols-2 gap-4"><div class="flex items-center space-x-3"><div class="flex-shrink-0"><svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg></div><span class="text-sm text-gray-700">100% Authentic Tunisian</span></div><div class="flex items-center space-x-3"><div class="flex-shrink-0"><svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg></div><span class="text-sm text-gray-700">Premium Quality</span></div><div class="flex items-center space-x-3"><div class="flex-shrink-0"><svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg></div><span class="text-sm text-gray-700">Natural &amp; Organic</span></div><div class="flex items-center space-x-3"><div class="flex-shrink-0"><svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg></div><span class="text-sm text-gray-700">Fast Shipping</span></div></div></div>',1))])]),B.value.length>0?(l(),a("div",Ot,[e[22]||(e[22]=t("h2",{class:"text-2xl font-serif font-bold text-gray-900 mb-8"}," You Might Also Like ",-1)),t("div",Qt,[(l(!0),a(I,null,j(B.value.slice(0,4),d=>(l(),D(R,{key:d.id,product:d},null,8,["product"]))),128))])])):g("",!0)])):(l(),a("div",Ut,[e[24]||(e[24]=t("div",{class:"text-6xl mb-4"},"🔍",-1)),e[25]||(e[25]=t("h1",{class:"text-2xl font-bold text-gray-900 mb-4"},"Product Not Found",-1)),e[26]||(e[26]=t("p",{class:"text-gray-600 mb-8"}," The product you're looking for doesn't exist or has been removed. ",-1)),f(c,{to:"/products",class:"btn-primary"},{default:_(()=>e[23]||(e[23]=[b(" Browse Products ")])),_:1,__:[23]})]))])}}});export{Wt as default};
