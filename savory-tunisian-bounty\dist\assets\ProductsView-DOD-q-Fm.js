import{d as D,r as y,c as F,l as R,o as E,a as i,b as e,m as f,n as b,v as q,F as _,g as V,t as x,u as I,h as H,p as J,i as a,e as K,q as j,k as z,s as Q,j as Z}from"./index-COfeaTnR.js";import{u as O,P as G}from"./ProductCard-CVdyVacO.js";const W={class:"relative"},X={class:"relative"},Y=["placeholder"],ee={class:"absolute inset-y-0 right-0 flex items-center"},te={key:0,class:"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto"},le={key:0,class:"p-3 border-b border-gray-100"},se={class:"space-y-1"},oe=["onClick"],re={key:1,class:"p-3"},ae={key:0,class:"text-xs font-medium text-gray-500 uppercase tracking-wide mb-2"},ne={class:"space-y-1"},ie=["onClick"],ue=["src","alt"],ce={class:"flex-1 min-w-0"},de=["innerHTML"],ve={class:"text-gray-500 text-xs truncate"},pe={class:"text-primary-600 font-medium"},me={key:2,class:"p-4 text-center text-gray-500"},ge={class:"text-sm"},ye=D({__name:"SearchBar",props:{placeholder:{default:"Search products..."},modelValue:{default:""}},emits:["update:modelValue","search"],setup(T,{emit:h}){const C=T,v=h,m=I(),g=O(),r=y(C.modelValue),p=y(!1),o=y(-1),c=y([]),d=F(()=>{if(!r.value||r.value.length<2)return[];const t=r.value.toLowerCase();return g.products.filter(s=>{var n;return s.is_active&&(s.name.toLowerCase().includes(t)||((n=s.description)==null?void 0:n.toLowerCase().includes(t)))}).slice(0,8)});R(()=>C.modelValue,t=>{r.value=t}),R(r,t=>{v("update:modelValue",t),o.value=-1});const P=()=>{p.value=!0},k=()=>{setTimeout(()=>{p.value=!1},200)},N=t=>{if(p.value)switch(t.key){case"ArrowDown":t.preventDefault(),o.value=Math.min(o.value+1,d.value.length-1);break;case"ArrowUp":t.preventDefault(),o.value=Math.max(o.value-1,-1);break;case"Enter":t.preventDefault(),o.value>=0&&d.value[o.value]?B(d.value[o.value]):S();break;case"Escape":p.value=!1,o.value=-1;break}},S=()=>{r.value.trim()&&(w(r.value.trim()),v("search",r.value.trim()),p.value=!1)},A=()=>{r.value="",v("update:modelValue",""),v("search","")},$=t=>{r.value=t,S()},B=t=>{p.value=!1,m.push({name:"product-detail",params:{id:t.id}})},L=t=>{if(!r.value)return t;const s=new RegExp(`(${r.value})`,"gi");return t.replace(s,'<mark class="bg-yellow-200">$1</mark>')},w=t=>{const s=c.value.filter(n=>n!==t);s.unshift(t),c.value=s.slice(0,10),u()},M=()=>{try{const t=localStorage.getItem("recentSearches");t&&(c.value=JSON.parse(t))}catch(t){console.error("Error loading recent searches:",t)}},u=()=>{try{localStorage.setItem("recentSearches",JSON.stringify(c.value))}catch(t){console.error("Error saving recent searches:",t)}},l=t=>{const s=t.target;s.src="https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400"};return E(()=>{M()}),(t,s)=>(a(),i("div",W,[e("div",X,[s[4]||(s[4]=e("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[e("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1)),b(e("input",{"onUpdate:modelValue":s[0]||(s[0]=n=>r.value=n),onInput:P,onFocus:s[1]||(s[1]=n=>p.value=!0),onBlur:k,onKeydown:N,type:"text",placeholder:t.placeholder,class:"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"},null,40,Y),[[q,r.value]]),e("div",ee,[r.value?(a(),i("button",{key:0,onClick:A,class:"p-2 text-gray-400 hover:text-gray-600"},s[2]||(s[2]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):f("",!0),e("button",{onClick:S,class:"p-2 text-primary-600 hover:text-primary-700"},s[3]||(s[3]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)]))])]),p.value&&(d.value.length>0||c.value.length>0)?(a(),i("div",te,[c.value.length>0&&!r.value?(a(),i("div",le,[s[6]||(s[6]=e("h4",{class:"text-xs font-medium text-gray-500 uppercase tracking-wide mb-2"}," Recent Searches ",-1)),e("div",se,[(a(!0),i(_,null,V(c.value.slice(0,5),n=>(a(),i("button",{key:n,onClick:U=>$(n),class:"flex items-center w-full text-left px-2 py-1 text-sm text-gray-700 hover:bg-gray-50 rounded"},[s[5]||(s[5]=e("svg",{class:"h-4 w-4 text-gray-400 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),H(" "+x(n),1)],8,oe))),128))])])):f("",!0),d.value.length>0?(a(),i("div",re,[c.value.length>0&&!r.value?(a(),i("h4",ae," Suggestions ")):f("",!0),e("div",ne,[(a(!0),i(_,null,V(d.value.slice(0,8),(n,U)=>(a(),i("button",{key:n.id,onClick:Ne=>B(n),class:J(["flex items-center w-full text-left px-2 py-2 text-sm hover:bg-gray-50 rounded",o.value===U?"bg-primary-50":""])},[e("img",{src:n.image_url||"/placeholder-product.jpg",alt:n.name,class:"h-8 w-8 object-cover rounded mr-3",onError:l},null,40,ue),e("div",ce,[e("p",{class:"font-medium text-gray-900 truncate",innerHTML:L(n.name)},null,8,de),e("p",ve,x(n.description),1)]),e("span",pe,"$"+x(n.price.toFixed(2)),1)],10,ie))),128))])])):f("",!0),r.value&&d.value.length===0?(a(),i("div",me,[s[7]||(s[7]=e("svg",{class:"h-8 w-8 mx-auto mb-2 text-gray-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),e("p",ge,'No products found for "'+x(r.value)+'"',1)])):f("",!0)])):f("",!0)]))}}),fe={class:"min-h-screen bg-gray-50"},he={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},xe={class:"bg-white rounded-lg shadow-sm p-6 mb-8"},ke={class:"flex flex-col lg:flex-row gap-4"},we={class:"flex-1"},be={class:"lg:w-64"},Ce=["value"],Se={key:0,class:"mt-6 pt-6 border-t border-gray-200"},_e={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ve={class:"flex items-center space-x-2"},$e={class:"flex justify-between items-center mb-6"},Be={class:"text-gray-600"},Me={key:0,class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},Fe={key:1,class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},Pe={key:2,class:"text-center py-12"},je=D({__name:"ProductsView",setup(T){const h=Q(),C=I(),v=O(),m=y(""),g=y(""),r=y(!0),p=y(!1),o=y({min:null,max:null}),c=y("name"),d=y(""),P=F(()=>v.categories),k=F(()=>{let u=v.filteredProducts;return(o.value.min!==null||o.value.max!==null)&&(u=u.filter(l=>{const t=l.price,s=o.value.min===null||t>=o.value.min,n=o.value.max===null||t<=o.value.max;return s&&n})),d.value&&(u=u.filter(l=>{switch(d.value){case"in-stock":return l.stock_quantity>0;case"low-stock":return l.stock_quantity>0&&l.stock_quantity<=5;default:return!0}})),u=[...u].sort((l,t)=>{switch(c.value){case"name":return l.name.localeCompare(t.name);case"name-desc":return t.name.localeCompare(l.name);case"price":return l.price-t.price;case"price-desc":return t.price-l.price;case"newest":return new Date(t.created_at).getTime()-new Date(l.created_at).getTime();default:return 0}}),u}),N=F(()=>o.value.min!==null||o.value.max!==null||d.value!==""||c.value!=="name"),S=()=>{v.searchProducts(m.value),w()},A=()=>{v.filterByCategory(g.value||null),w()},$=()=>{m.value="",g.value="",o.value={min:null,max:null},c.value="name",d.value="",p.value=!1,v.clearFilters(),C.push({name:"products"})},B=()=>{w()},L=()=>{w()},w=()=>{const u={};m.value&&(u.search=m.value),g.value&&(u.category=g.value),C.push({name:"products",query:u})},M=()=>{h.query.search&&(m.value=h.query.search),h.query.category&&(g.value=h.query.category)};return E(async()=>{try{await v.initialize(),M(),m.value&&v.searchProducts(m.value),g.value&&v.filterByCategory(g.value)}catch(u){console.error("Error loading products:",u)}finally{r.value=!1}}),R(()=>h.query,()=>{M()},{deep:!0}),(u,l)=>(a(),i("div",fe,[e("div",he,[l[20]||(l[20]=e("div",{class:"mb-8"},[e("h1",{class:"text-3xl font-serif font-bold text-gray-900 mb-4"}," Our Products "),e("p",{class:"text-lg text-gray-600"}," Discover authentic Tunisian flavors and traditional specialties ")],-1)),e("div",xe,[e("div",ke,[e("div",we,[K(ye,{modelValue:m.value,"onUpdate:modelValue":l[0]||(l[0]=t=>m.value=t),onSearch:S,placeholder:"Search products, categories, or keywords..."},null,8,["modelValue"])]),e("div",be,[b(e("select",{"onUpdate:modelValue":l[1]||(l[1]=t=>g.value=t),class:"input-field",onChange:A},[l[7]||(l[7]=e("option",{value:""},"All Categories",-1)),(a(!0),i(_,null,V(P.value,t=>(a(),i("option",{key:t.id,value:t.id},x(t.name),9,Ce))),128))],544),[[j,g.value]])]),e("button",{onClick:l[2]||(l[2]=t=>p.value=!p.value),class:"btn-outline whitespace-nowrap flex items-center space-x-2"},l[8]||(l[8]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"})],-1),e("span",null,"Filters",-1)])),m.value||g.value||N.value?(a(),i("button",{key:0,onClick:$,class:"btn-outline whitespace-nowrap"}," Clear All ")):f("",!0)]),p.value?(a(),i("div",Se,[e("div",_e,[e("div",null,[l[10]||(l[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Price Range",-1)),e("div",Ve,[b(e("input",{"onUpdate:modelValue":l[3]||(l[3]=t=>o.value.min=t),type:"number",placeholder:"Min",class:"input-field text-sm",min:"0"},null,512),[[q,o.value.min,void 0,{number:!0}]]),l[9]||(l[9]=e("span",{class:"text-gray-500"},"-",-1)),b(e("input",{"onUpdate:modelValue":l[4]||(l[4]=t=>o.value.max=t),type:"number",placeholder:"Max",class:"input-field text-sm",min:"0"},null,512),[[q,o.value.max,void 0,{number:!0}]])])]),e("div",null,[l[12]||(l[12]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Sort By",-1)),b(e("select",{"onUpdate:modelValue":l[5]||(l[5]=t=>c.value=t),onChange:B,class:"input-field text-sm"},l[11]||(l[11]=[z('<option value="name">Name (A-Z)</option><option value="name-desc">Name (Z-A)</option><option value="price">Price (Low to High)</option><option value="price-desc">Price (High to Low)</option><option value="newest">Newest First</option>',5)]),544),[[j,c.value]])]),e("div",null,[l[14]||(l[14]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Availability",-1)),b(e("select",{"onUpdate:modelValue":l[6]||(l[6]=t=>d.value=t),class:"input-field text-sm"},l[13]||(l[13]=[e("option",{value:""},"All Products",-1),e("option",{value:"in-stock"},"In Stock Only",-1),e("option",{value:"low-stock"},"Low Stock",-1)]),512),[[j,d.value]])])]),e("div",{class:"mt-4 flex justify-end"},[e("button",{onClick:L,class:"btn-primary"}," Apply Filters ")])])):f("",!0)]),e("div",$e,[e("p",Be,x(k.value.length)+" product"+x(k.value.length!==1?"s":"")+" found ",1),l[15]||(l[15]=e("div",{class:"flex items-center space-x-4"},[e("select",{class:"input-field w-auto"},[e("option",null,"Sort by Name"),e("option",null,"Sort by Price"),e("option",null,"Sort by Newest")])],-1))]),!r.value&&k.value.length>0?(a(),i("div",Me,[(a(!0),i(_,null,V(k.value,t=>(a(),Z(G,{key:t.id,product:t},null,8,["product"]))),128))])):r.value?(a(),i("div",Fe,[(a(),i(_,null,V(8,t=>e("div",{key:t,class:"card animate-pulse"},l[16]||(l[16]=[z('<div class="h-48 bg-gray-200"></div><div class="p-4"><div class="h-6 bg-gray-200 rounded mb-2"></div><div class="h-4 bg-gray-200 rounded mb-2"></div><div class="h-6 bg-gray-200 rounded w-20"></div></div>',2)]))),64))])):(a(),i("div",Pe,[l[17]||(l[17]=e("div",{class:"text-6xl mb-4"},"🔍",-1)),l[18]||(l[18]=e("h3",{class:"text-xl font-semibold text-gray-900 mb-2"}," No products found ",-1)),l[19]||(l[19]=e("p",{class:"text-gray-600 mb-6"}," Try adjusting your search criteria or browse our categories ",-1)),e("button",{onClick:$,class:"btn-primary"},"Clear Filters")]))])]))}});export{je as default};
