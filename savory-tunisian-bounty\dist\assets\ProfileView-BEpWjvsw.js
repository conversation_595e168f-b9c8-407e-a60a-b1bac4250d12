import{d as u,B as f,r as b,C as x,o as y,a as i,b as e,D as c,n,v as d,i as r}from"./index-COfeaTnR.js";const g={class:"min-h-screen bg-gray-50"},v={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},h={class:"bg-white rounded-lg shadow-sm p-6"},_={class:"space-y-6"},N={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},k={class:"flex justify-end"},w=["disabled"],P={key:0},U={key:1},M=u({__name:"ProfileView",setup(V){const s=f(),a=b(!1),t=x({fullName:"",email:"",phone:""}),p=async()=>{try{a.value=!0,await s.updateProfile({full_name:t.fullName,phone:t.phone})}catch(m){console.error("Error updating profile:",m)}finally{a.value=!1}};return y(()=>{s.profile&&(t.fullName=s.profile.full_name||"",t.email=s.profile.email,t.phone=s.profile.phone||"")}),(m,l)=>(r(),i("div",g,[e("div",v,[l[7]||(l[7]=e("h1",{class:"text-3xl font-serif font-bold text-gray-900 mb-8"},"My Profile",-1)),e("div",h,[e("div",_,[e("div",null,[l[6]||(l[6]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Profile Information",-1)),e("form",{onSubmit:c(p,["prevent"]),class:"space-y-4"},[e("div",N,[e("div",null,[l[3]||(l[3]=e("label",{for:"fullName",class:"block text-sm font-medium text-gray-700 mb-1"}," Full Name ",-1)),n(e("input",{id:"fullName","onUpdate:modelValue":l[0]||(l[0]=o=>t.fullName=o),type:"text",class:"input-field"},null,512),[[d,t.fullName]])]),e("div",null,[l[4]||(l[4]=e("label",{for:"email",class:"block text-sm font-medium text-gray-700 mb-1"}," Email ",-1)),n(e("input",{id:"email","onUpdate:modelValue":l[1]||(l[1]=o=>t.email=o),type:"email",disabled:"",class:"input-field bg-gray-50"},null,512),[[d,t.email]])]),e("div",null,[l[5]||(l[5]=e("label",{for:"phone",class:"block text-sm font-medium text-gray-700 mb-1"}," Phone Number ",-1)),n(e("input",{id:"phone","onUpdate:modelValue":l[2]||(l[2]=o=>t.phone=o),type:"tel",class:"input-field"},null,512),[[d,t.phone]])])]),e("div",k,[e("button",{type:"submit",disabled:a.value,class:"btn-primary disabled:opacity-50"},[a.value?(r(),i("span",P,"Updating...")):(r(),i("span",U,"Update Profile"))],8,w)])],32)])])])])]))}});export{M as default};
