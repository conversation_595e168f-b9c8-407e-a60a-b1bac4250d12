import{d as k,B as N,r as x,C as b,a as l,b as e,D as V,m as n,n as c,v as w,p as v,t as m,E as C,h as y,k as q,e as S,w as T,f as U,u as A,i}from"./index-COfeaTnR.js";const B={class:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8"},E={class:"mt-8 sm:mx-auto sm:w-full sm:max-w-md"},R={class:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10"},j={class:"mt-1"},D={key:0,class:"mt-1 text-sm text-red-600"},F={class:"mt-1"},M={key:0,class:"mt-1 text-sm text-red-600"},z={class:"mt-1"},I={key:0,class:"mt-1 text-sm text-red-600"},J={class:"mt-1"},O={key:0,class:"mt-1 text-sm text-red-600"},G={class:"flex items-center"},H=["disabled"],K={key:0},L={key:1},Q={key:0,class:"rounded-md bg-red-50 p-4"},W={class:"text-sm text-red-700"},X={key:1,class:"rounded-md bg-green-50 p-4"},Y={class:"text-sm text-green-700"},Z={class:"mt-6"},$={class:"mt-6"},te=k({__name:"RegisterView",setup(ee){const g=A(),_=N(),f=x(!1),u=x(""),p=x(""),t=b({fullName:"",email:"",password:"",confirmPassword:"",acceptTerms:!1}),a=b({fullName:"",email:"",password:"",confirmPassword:""}),h=()=>{a.fullName="",a.email="",a.password="",a.confirmPassword="";let r=!0;return t.fullName.trim()||(a.fullName="Full name is required",r=!1),t.email||(a.email="Email is required",r=!1),t.password?t.password.length<6&&(a.password="Password must be at least 6 characters",r=!1):(a.password="Password is required",r=!1),t.confirmPassword?t.password!==t.confirmPassword&&(a.confirmPassword="Passwords do not match",r=!1):(a.confirmPassword="Please confirm your password",r=!1),r},P=async()=>{if(h())try{f.value=!0,u.value="",p.value="";const{data:r,error:s}=await _.signUp(t.email,t.password,t.fullName);if(s){u.value=(s==null?void 0:s.message)||"Registration failed";return}r!=null&&r.user&&!r.session?(p.value="Please check your email to confirm your account before signing in.",Object.keys(t).forEach(d=>{typeof t[d]=="string"?t[d]="":typeof t[d]=="boolean"&&(t[d]=!1)})):g.push("/")}catch(r){u.value="An unexpected error occurred",console.error("Registration error:",r)}finally{f.value=!1}};return(r,s)=>{const d=U("router-link");return i(),l("div",B,[s[12]||(s[12]=e("div",{class:"sm:mx-auto sm:w-full sm:max-w-md"},[e("div",{class:"text-center"},[e("h2",{class:"text-3xl font-serif font-bold text-gray-900"}," Create Account "),e("p",{class:"mt-2 text-sm text-gray-600"}," Join us to discover authentic Tunisian flavors ")])],-1)),e("div",E,[e("div",R,[e("form",{onSubmit:V(P,["prevent"]),class:"space-y-6"},[e("div",null,[s[5]||(s[5]=e("label",{for:"fullName",class:"block text-sm font-medium text-gray-700"}," Full Name ",-1)),e("div",j,[c(e("input",{id:"fullName","onUpdate:modelValue":s[0]||(s[0]=o=>t.fullName=o),type:"text",autocomplete:"name",required:"",class:v(["input-field",{"border-red-500":a.fullName}])},null,2),[[w,t.fullName]]),a.fullName?(i(),l("p",D,m(a.fullName),1)):n("",!0)])]),e("div",null,[s[6]||(s[6]=e("label",{for:"email",class:"block text-sm font-medium text-gray-700"}," Email address ",-1)),e("div",F,[c(e("input",{id:"email","onUpdate:modelValue":s[1]||(s[1]=o=>t.email=o),type:"email",autocomplete:"email",required:"",class:v(["input-field",{"border-red-500":a.email}])},null,2),[[w,t.email]]),a.email?(i(),l("p",M,m(a.email),1)):n("",!0)])]),e("div",null,[s[7]||(s[7]=e("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Password ",-1)),e("div",z,[c(e("input",{id:"password","onUpdate:modelValue":s[2]||(s[2]=o=>t.password=o),type:"password",autocomplete:"new-password",required:"",class:v(["input-field",{"border-red-500":a.password}])},null,2),[[w,t.password]]),a.password?(i(),l("p",I,m(a.password),1)):n("",!0)])]),e("div",null,[s[8]||(s[8]=e("label",{for:"confirmPassword",class:"block text-sm font-medium text-gray-700"}," Confirm Password ",-1)),e("div",J,[c(e("input",{id:"confirmPassword","onUpdate:modelValue":s[3]||(s[3]=o=>t.confirmPassword=o),type:"password",autocomplete:"new-password",required:"",class:v(["input-field",{"border-red-500":a.confirmPassword}])},null,2),[[w,t.confirmPassword]]),a.confirmPassword?(i(),l("p",O,m(a.confirmPassword),1)):n("",!0)])]),e("div",G,[c(e("input",{id:"terms","onUpdate:modelValue":s[4]||(s[4]=o=>t.acceptTerms=o),type:"checkbox",required:"",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[C,t.acceptTerms]]),s[9]||(s[9]=e("label",{for:"terms",class:"ml-2 block text-sm text-gray-900"},[y(" I agree to the "),e("a",{href:"#",class:"text-primary-600 hover:text-primary-500"},"Terms of Service"),y(" and "),e("a",{href:"#",class:"text-primary-600 hover:text-primary-500"},"Privacy Policy")],-1))]),e("div",null,[e("button",{type:"submit",disabled:f.value,class:"w-full btn-primary py-3 disabled:opacity-50 disabled:cursor-not-allowed"},[f.value?(i(),l("span",K,"Creating account...")):(i(),l("span",L,"Create account"))],8,H)]),u.value?(i(),l("div",Q,[e("div",W,m(u.value),1)])):n("",!0),p.value?(i(),l("div",X,[e("div",Y,m(p.value),1)])):n("",!0)],32),e("div",Z,[s[11]||(s[11]=q('<div class="relative"><div class="absolute inset-0 flex items-center"><div class="w-full border-t border-gray-300"></div></div><div class="relative flex justify-center text-sm"><span class="px-2 bg-white text-gray-500">Already have an account?</span></div></div>',1)),e("div",$,[S(d,{to:"/login",class:"w-full btn-outline text-center block py-3"},{default:T(()=>s[10]||(s[10]=[y(" Sign in instead ")])),_:1,__:[10]})])])])])])}}});export{te as default};
