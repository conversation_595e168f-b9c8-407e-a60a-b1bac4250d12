const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/HomeView-BeqJDZnt.js","assets/ProductCard-CVdyVacO.js","assets/ProductCard-BNSKu3im.css","assets/ProductsView-DOD-q-Fm.js","assets/ProductDetailView-BL47125-.js"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&s(o)}).observe(document,{childList:!0,subtree:!0});function r(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(n){if(n.ep)return;n.ep=!0;const i=r(n);fetch(n.href,i)}})();/**
* @vue/shared v3.5.15
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ri(e){const t=Object.create(null);for(const r of e.split(","))t[r]=1;return r=>r in t}const ce={},wr=[],dt=()=>{},ac=()=>!1,zs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),si=e=>e.startsWith("onUpdate:"),we=Object.assign,ni=(e,t)=>{const r=e.indexOf(t);r>-1&&e.splice(r,1)},lc=Object.prototype.hasOwnProperty,ie=(e,t)=>lc.call(e,t),F=Array.isArray,br=e=>fs(e)==="[object Map]",Or=e=>fs(e)==="[object Set]",ji=e=>fs(e)==="[object Date]",z=e=>typeof e=="function",ve=e=>typeof e=="string",ht=e=>typeof e=="symbol",fe=e=>e!==null&&typeof e=="object",ca=e=>(fe(e)||z(e))&&z(e.then)&&z(e.catch),ua=Object.prototype.toString,fs=e=>ua.call(e),cc=e=>fs(e).slice(8,-1),fa=e=>fs(e)==="[object Object]",ii=e=>ve(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,qr=ri(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Ws=e=>{const t=Object.create(null);return r=>t[r]||(t[r]=e(r))},uc=/-(\w)/g,Qe=Ws(e=>e.replace(uc,(t,r)=>r?r.toUpperCase():"")),fc=/\B([A-Z])/g,qt=Ws(e=>e.replace(fc,"-$1").toLowerCase()),Gs=Ws(e=>e.charAt(0).toUpperCase()+e.slice(1)),un=Ws(e=>e?`on${Gs(e)}`:""),Ut=(e,t)=>!Object.is(e,t),As=(e,...t)=>{for(let r=0;r<e.length;r++)e[r](...t)},da=(e,t,r,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:r})},Is=e=>{const t=parseFloat(e);return isNaN(t)?e:t},dc=e=>{const t=ve(e)?Number(e):NaN;return isNaN(t)?e:t};let Ii;const Js=()=>Ii||(Ii=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function oi(e){if(F(e)){const t={};for(let r=0;r<e.length;r++){const s=e[r],n=ve(s)?mc(s):oi(s);if(n)for(const i in n)t[i]=n[i]}return t}else if(ve(e)||fe(e))return e}const hc=/;(?![^(]*\))/g,pc=/:([^]+)/,gc=/\/\*[^]*?\*\//g;function mc(e){const t={};return e.replace(gc,"").split(hc).forEach(r=>{if(r){const s=r.split(pc);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Qs(e){let t="";if(ve(e))t=e;else if(F(e))for(let r=0;r<e.length;r++){const s=Qs(e[r]);s&&(t+=s+" ")}else if(fe(e))for(const r in e)e[r]&&(t+=r+" ");return t.trim()}const vc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",_c=ri(vc);function ha(e){return!!e||e===""}function yc(e,t){if(e.length!==t.length)return!1;let r=!0;for(let s=0;r&&s<e.length;s++)r=er(e[s],t[s]);return r}function er(e,t){if(e===t)return!0;let r=ji(e),s=ji(t);if(r||s)return r&&s?e.getTime()===t.getTime():!1;if(r=ht(e),s=ht(t),r||s)return e===t;if(r=F(e),s=F(t),r||s)return r&&s?yc(e,t):!1;if(r=fe(e),s=fe(t),r||s){if(!r||!s)return!1;const n=Object.keys(e).length,i=Object.keys(t).length;if(n!==i)return!1;for(const o in e){const a=e.hasOwnProperty(o),l=t.hasOwnProperty(o);if(a&&!l||!a&&l||!er(e[o],t[o]))return!1}}return String(e)===String(t)}function ai(e,t){return e.findIndex(r=>er(r,t))}const pa=e=>!!(e&&e.__v_isRef===!0),ts=e=>ve(e)?e:e==null?"":F(e)||fe(e)&&(e.toString===ua||!z(e.toString))?pa(e)?ts(e.value):JSON.stringify(e,ga,2):String(e),ga=(e,t)=>pa(t)?ga(e,t.value):br(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((r,[s,n],i)=>(r[fn(s,i)+" =>"]=n,r),{})}:Or(t)?{[`Set(${t.size})`]:[...t.values()].map(r=>fn(r))}:ht(t)?fn(t):fe(t)&&!F(t)&&!fa(t)?String(t):t,fn=(e,t="")=>{var r;return ht(e)?`Symbol(${(r=e.description)!=null?r:t})`:e};/**
* @vue/reactivity v3.5.15
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ke;class ma{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ke,!t&&ke&&(this.index=(ke.scopes||(ke.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].pause();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].resume();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].resume()}}run(t){if(this._active){const r=ke;try{return ke=this,t()}finally{ke=r}}}on(){++this._on===1&&(this.prevScope=ke,ke=this)}off(){this._on>0&&--this._on===0&&(ke=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let r,s;for(r=0,s=this.effects.length;r<s;r++)this.effects[r].stop();for(this.effects.length=0,r=0,s=this.cleanups.length;r<s;r++)this.cleanups[r]();if(this.cleanups.length=0,this.scopes){for(r=0,s=this.scopes.length;r<s;r++)this.scopes[r].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function va(e){return new ma(e)}function _a(){return ke}function wc(e,t=!1){ke&&ke.cleanups.push(e)}let pe;const dn=new WeakSet;class ya{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ke&&ke.active&&ke.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,dn.has(this)&&(dn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ba(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Li(this),Sa(this);const t=pe,r=Ze;pe=this,Ze=!0;try{return this.fn()}finally{xa(this),pe=t,Ze=r,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)ui(t);this.deps=this.depsTail=void 0,Li(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?dn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){On(this)&&this.run()}get dirty(){return On(this)}}let wa=0,Hr,Vr;function ba(e,t=!1){if(e.flags|=8,t){e.next=Vr,Vr=e;return}e.next=Hr,Hr=e}function li(){wa++}function ci(){if(--wa>0)return;if(Vr){let t=Vr;for(Vr=void 0;t;){const r=t.next;t.next=void 0,t.flags&=-9,t=r}}let e;for(;Hr;){let t=Hr;for(Hr=void 0;t;){const r=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=r}}if(e)throw e}function Sa(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function xa(e){let t,r=e.depsTail,s=r;for(;s;){const n=s.prevDep;s.version===-1?(s===r&&(r=n),ui(s),bc(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=n}e.deps=t,e.depsTail=r}function On(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Ea(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Ea(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===rs)||(e.globalVersion=rs,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!On(e))))return;e.flags|=2;const t=e.dep,r=pe,s=Ze;pe=e,Ze=!0;try{Sa(e);const n=e.fn(e._value);(t.version===0||Ut(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(n){throw t.version++,n}finally{pe=r,Ze=s,xa(e),e.flags&=-3}}function ui(e,t=!1){const{dep:r,prevSub:s,nextSub:n}=e;if(s&&(s.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=s,e.nextSub=void 0),r.subs===e&&(r.subs=s,!s&&r.computed)){r.computed.flags&=-5;for(let i=r.computed.deps;i;i=i.nextDep)ui(i,!0)}!t&&!--r.sc&&r.map&&r.map.delete(r.key)}function bc(e){const{prevDep:t,nextDep:r}=e;t&&(t.nextDep=r,e.prevDep=void 0),r&&(r.prevDep=t,e.nextDep=void 0)}let Ze=!0;const ka=[];function St(){ka.push(Ze),Ze=!1}function xt(){const e=ka.pop();Ze=e===void 0?!0:e}function Li(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const r=pe;pe=void 0;try{t()}finally{pe=r}}}let rs=0;class Sc{constructor(t,r){this.sub=t,this.dep=r,this.version=r.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class fi{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!pe||!Ze||pe===this.computed)return;let r=this.activeLink;if(r===void 0||r.sub!==pe)r=this.activeLink=new Sc(pe,this),pe.deps?(r.prevDep=pe.depsTail,pe.depsTail.nextDep=r,pe.depsTail=r):pe.deps=pe.depsTail=r,Ta(r);else if(r.version===-1&&(r.version=this.version,r.nextDep)){const s=r.nextDep;s.prevDep=r.prevDep,r.prevDep&&(r.prevDep.nextDep=s),r.prevDep=pe.depsTail,r.nextDep=void 0,pe.depsTail.nextDep=r,pe.depsTail=r,pe.deps===r&&(pe.deps=s)}return r}trigger(t){this.version++,rs++,this.notify(t)}notify(t){li();try{for(let r=this.subs;r;r=r.prevSub)r.sub.notify()&&r.sub.dep.notify()}finally{ci()}}}function Ta(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Ta(s)}const r=e.dep.subs;r!==e&&(e.prevSub=r,r&&(r.nextSub=e)),e.dep.subs=e}}const Ls=new WeakMap,Xt=Symbol(""),Rn=Symbol(""),ss=Symbol("");function Ce(e,t,r){if(Ze&&pe){let s=Ls.get(e);s||Ls.set(e,s=new Map);let n=s.get(r);n||(s.set(r,n=new fi),n.map=s,n.key=r),n.track()}}function yt(e,t,r,s,n,i){const o=Ls.get(e);if(!o){rs++;return}const a=l=>{l&&l.trigger()};if(li(),t==="clear")o.forEach(a);else{const l=F(e),u=l&&ii(r);if(l&&r==="length"){const c=Number(s);o.forEach((f,d)=>{(d==="length"||d===ss||!ht(d)&&d>=c)&&a(f)})}else switch((r!==void 0||o.has(void 0))&&a(o.get(r)),u&&a(o.get(ss)),t){case"add":l?u&&a(o.get("length")):(a(o.get(Xt)),br(e)&&a(o.get(Rn)));break;case"delete":l||(a(o.get(Xt)),br(e)&&a(o.get(Rn)));break;case"set":br(e)&&a(o.get(Xt));break}}ci()}function xc(e,t){const r=Ls.get(e);return r&&r.get(t)}function ir(e){const t=te(e);return t===e?t:(Ce(t,"iterate",ss),We(e)?t:t.map(Ee))}function Ys(e){return Ce(e=te(e),"iterate",ss),e}const Ec={__proto__:null,[Symbol.iterator](){return hn(this,Symbol.iterator,Ee)},concat(...e){return ir(this).concat(...e.map(t=>F(t)?ir(t):t))},entries(){return hn(this,"entries",e=>(e[1]=Ee(e[1]),e))},every(e,t){return gt(this,"every",e,t,void 0,arguments)},filter(e,t){return gt(this,"filter",e,t,r=>r.map(Ee),arguments)},find(e,t){return gt(this,"find",e,t,Ee,arguments)},findIndex(e,t){return gt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return gt(this,"findLast",e,t,Ee,arguments)},findLastIndex(e,t){return gt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return gt(this,"forEach",e,t,void 0,arguments)},includes(...e){return pn(this,"includes",e)},indexOf(...e){return pn(this,"indexOf",e)},join(e){return ir(this).join(e)},lastIndexOf(...e){return pn(this,"lastIndexOf",e)},map(e,t){return gt(this,"map",e,t,void 0,arguments)},pop(){return Ir(this,"pop")},push(...e){return Ir(this,"push",e)},reduce(e,...t){return Di(this,"reduce",e,t)},reduceRight(e,...t){return Di(this,"reduceRight",e,t)},shift(){return Ir(this,"shift")},some(e,t){return gt(this,"some",e,t,void 0,arguments)},splice(...e){return Ir(this,"splice",e)},toReversed(){return ir(this).toReversed()},toSorted(e){return ir(this).toSorted(e)},toSpliced(...e){return ir(this).toSpliced(...e)},unshift(...e){return Ir(this,"unshift",e)},values(){return hn(this,"values",Ee)}};function hn(e,t,r){const s=Ys(e),n=s[t]();return s!==e&&!We(e)&&(n._next=n.next,n.next=()=>{const i=n._next();return i.value&&(i.value=r(i.value)),i}),n}const kc=Array.prototype;function gt(e,t,r,s,n,i){const o=Ys(e),a=o!==e&&!We(e),l=o[t];if(l!==kc[t]){const f=l.apply(e,i);return a?Ee(f):f}let u=r;o!==e&&(a?u=function(f,d){return r.call(this,Ee(f),d,e)}:r.length>2&&(u=function(f,d){return r.call(this,f,d,e)}));const c=l.call(o,u,s);return a&&n?n(c):c}function Di(e,t,r,s){const n=Ys(e);let i=r;return n!==e&&(We(e)?r.length>3&&(i=function(o,a,l){return r.call(this,o,a,l,e)}):i=function(o,a,l){return r.call(this,o,Ee(a),l,e)}),n[t](i,...s)}function pn(e,t,r){const s=te(e);Ce(s,"iterate",ss);const n=s[t](...r);return(n===-1||n===!1)&&pi(r[0])?(r[0]=te(r[0]),s[t](...r)):n}function Ir(e,t,r=[]){St(),li();const s=te(e)[t].apply(e,r);return ci(),xt(),s}const Tc=ri("__proto__,__v_isRef,__isVue"),Ca=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ht));function Cc(e){ht(e)||(e=String(e));const t=te(this);return Ce(t,"has",e),t.hasOwnProperty(e)}class Aa{constructor(t=!1,r=!1){this._isReadonly=t,this._isShallow=r}get(t,r,s){if(r==="__v_skip")return t.__v_skip;const n=this._isReadonly,i=this._isShallow;if(r==="__v_isReactive")return!n;if(r==="__v_isReadonly")return n;if(r==="__v_isShallow")return i;if(r==="__v_raw")return s===(n?i?Mc:$a:i?Ra:Oa).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=F(t);if(!n){let l;if(o&&(l=Ec[r]))return l;if(r==="hasOwnProperty")return Cc}const a=Reflect.get(t,r,_e(t)?t:s);return(ht(r)?Ca.has(r):Tc(r))||(n||Ce(t,"get",r),i)?a:_e(a)?o&&ii(r)?a:a.value:fe(a)?n?ze(a):ds(a):a}}class Pa extends Aa{constructor(t=!1){super(!1,t)}set(t,r,s,n){let i=t[r];if(!this._isShallow){const l=Ft(i);if(!We(s)&&!Ft(s)&&(i=te(i),s=te(s)),!F(t)&&_e(i)&&!_e(s))return l?!1:(i.value=s,!0)}const o=F(t)&&ii(r)?Number(r)<t.length:ie(t,r),a=Reflect.set(t,r,s,_e(t)?t:n);return t===te(n)&&(o?Ut(s,i)&&yt(t,"set",r,s):yt(t,"add",r,s)),a}deleteProperty(t,r){const s=ie(t,r);t[r];const n=Reflect.deleteProperty(t,r);return n&&s&&yt(t,"delete",r,void 0),n}has(t,r){const s=Reflect.has(t,r);return(!ht(r)||!Ca.has(r))&&Ce(t,"has",r),s}ownKeys(t){return Ce(t,"iterate",F(t)?"length":Xt),Reflect.ownKeys(t)}}class Ac extends Aa{constructor(t=!1){super(!0,t)}set(t,r){return!0}deleteProperty(t,r){return!0}}const Pc=new Pa,Oc=new Ac,Rc=new Pa(!0);const $n=e=>e,ys=e=>Reflect.getPrototypeOf(e);function $c(e,t,r){return function(...s){const n=this.__v_raw,i=te(n),o=br(i),a=e==="entries"||e===Symbol.iterator&&o,l=e==="keys"&&o,u=n[e](...s),c=r?$n:t?Ds:Ee;return!t&&Ce(i,"iterate",l?Rn:Xt),{next(){const{value:f,done:d}=u.next();return d?{value:f,done:d}:{value:a?[c(f[0]),c(f[1])]:c(f),done:d}},[Symbol.iterator](){return this}}}}function ws(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function jc(e,t){const r={get(n){const i=this.__v_raw,o=te(i),a=te(n);e||(Ut(n,a)&&Ce(o,"get",n),Ce(o,"get",a));const{has:l}=ys(o),u=t?$n:e?Ds:Ee;if(l.call(o,n))return u(i.get(n));if(l.call(o,a))return u(i.get(a));i!==o&&i.get(n)},get size(){const n=this.__v_raw;return!e&&Ce(te(n),"iterate",Xt),Reflect.get(n,"size",n)},has(n){const i=this.__v_raw,o=te(i),a=te(n);return e||(Ut(n,a)&&Ce(o,"has",n),Ce(o,"has",a)),n===a?i.has(n):i.has(n)||i.has(a)},forEach(n,i){const o=this,a=o.__v_raw,l=te(a),u=t?$n:e?Ds:Ee;return!e&&Ce(l,"iterate",Xt),a.forEach((c,f)=>n.call(i,u(c),u(f),o))}};return we(r,e?{add:ws("add"),set:ws("set"),delete:ws("delete"),clear:ws("clear")}:{add(n){!t&&!We(n)&&!Ft(n)&&(n=te(n));const i=te(this);return ys(i).has.call(i,n)||(i.add(n),yt(i,"add",n,n)),this},set(n,i){!t&&!We(i)&&!Ft(i)&&(i=te(i));const o=te(this),{has:a,get:l}=ys(o);let u=a.call(o,n);u||(n=te(n),u=a.call(o,n));const c=l.call(o,n);return o.set(n,i),u?Ut(i,c)&&yt(o,"set",n,i):yt(o,"add",n,i),this},delete(n){const i=te(this),{has:o,get:a}=ys(i);let l=o.call(i,n);l||(n=te(n),l=o.call(i,n)),a&&a.call(i,n);const u=i.delete(n);return l&&yt(i,"delete",n,void 0),u},clear(){const n=te(this),i=n.size!==0,o=n.clear();return i&&yt(n,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(n=>{r[n]=$c(n,e,t)}),r}function di(e,t){const r=jc(e,t);return(s,n,i)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?s:Reflect.get(ie(r,n)&&n in s?r:s,n,i)}const Ic={get:di(!1,!1)},Lc={get:di(!1,!0)},Dc={get:di(!0,!1)};const Oa=new WeakMap,Ra=new WeakMap,$a=new WeakMap,Mc=new WeakMap;function Uc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Nc(e){return e.__v_skip||!Object.isExtensible(e)?0:Uc(cc(e))}function ds(e){return Ft(e)?e:hi(e,!1,Pc,Ic,Oa)}function ja(e){return hi(e,!1,Rc,Lc,Ra)}function ze(e){return hi(e,!0,Oc,Dc,$a)}function hi(e,t,r,s,n){if(!fe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=Nc(e);if(i===0)return e;const o=n.get(e);if(o)return o;const a=new Proxy(e,i===2?s:r);return n.set(e,a),a}function Nt(e){return Ft(e)?Nt(e.__v_raw):!!(e&&e.__v_isReactive)}function Ft(e){return!!(e&&e.__v_isReadonly)}function We(e){return!!(e&&e.__v_isShallow)}function pi(e){return e?!!e.__v_raw:!1}function te(e){const t=e&&e.__v_raw;return t?te(t):e}function gi(e){return!ie(e,"__v_skip")&&Object.isExtensible(e)&&da(e,"__v_skip",!0),e}const Ee=e=>fe(e)?ds(e):e,Ds=e=>fe(e)?ze(e):e;function _e(e){return e?e.__v_isRef===!0:!1}function me(e){return Ia(e,!1)}function Fc(e){return Ia(e,!0)}function Ia(e,t){return _e(e)?e:new Bc(e,t)}class Bc{constructor(t,r){this.dep=new fi,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=r?t:te(t),this._value=r?t:Ee(t),this.__v_isShallow=r}get value(){return this.dep.track(),this._value}set value(t){const r=this._rawValue,s=this.__v_isShallow||We(t)||Ft(t);t=s?t:te(t),Ut(t,r)&&(this._rawValue=t,this._value=s?t:Ee(t),this.dep.trigger())}}function Sr(e){return _e(e)?e.value:e}const qc={get:(e,t,r)=>t==="__v_raw"?e:Sr(Reflect.get(e,t,r)),set:(e,t,r,s)=>{const n=e[t];return _e(n)&&!_e(r)?(n.value=r,!0):Reflect.set(e,t,r,s)}};function La(e){return Nt(e)?e:new Proxy(e,qc)}function Hc(e){const t=F(e)?new Array(e.length):{};for(const r in e)t[r]=Kc(e,r);return t}class Vc{constructor(t,r,s){this._object=t,this._key=r,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return xc(te(this._object),this._key)}}function Kc(e,t,r){const s=e[t];return _e(s)?s:new Vc(e,t,r)}class zc{constructor(t,r,s){this.fn=t,this.setter=r,this._value=void 0,this.dep=new fi(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=rs-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!r,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&pe!==this)return ba(this,!0),!0}get value(){const t=this.dep.track();return Ea(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Wc(e,t,r=!1){let s,n;return z(e)?s=e:(s=e.get,n=e.set),new zc(s,n,r)}const bs={},Ms=new WeakMap;let Jt;function Gc(e,t=!1,r=Jt){if(r){let s=Ms.get(r);s||Ms.set(r,s=[]),s.push(e)}}function Jc(e,t,r=ce){const{immediate:s,deep:n,once:i,scheduler:o,augmentJob:a,call:l}=r,u=T=>n?T:We(T)||n===!1||n===0?wt(T,1):wt(T);let c,f,d,h,m=!1,v=!1;if(_e(e)?(f=()=>e.value,m=We(e)):Nt(e)?(f=()=>u(e),m=!0):F(e)?(v=!0,m=e.some(T=>Nt(T)||We(T)),f=()=>e.map(T=>{if(_e(T))return T.value;if(Nt(T))return u(T);if(z(T))return l?l(T,2):T()})):z(e)?t?f=l?()=>l(e,2):e:f=()=>{if(d){St();try{d()}finally{xt()}}const T=Jt;Jt=c;try{return l?l(e,3,[h]):e(h)}finally{Jt=T}}:f=dt,t&&n){const T=f,P=n===!0?1/0:n;f=()=>wt(T(),P)}const y=_a(),x=()=>{c.stop(),y&&y.active&&ni(y.effects,c)};if(i&&t){const T=t;t=(...P)=>{T(...P),x()}}let A=v?new Array(e.length).fill(bs):bs;const k=T=>{if(!(!(c.flags&1)||!c.dirty&&!T))if(t){const P=c.run();if(n||m||(v?P.some((W,V)=>Ut(W,A[V])):Ut(P,A))){d&&d();const W=Jt;Jt=c;try{const V=[P,A===bs?void 0:v&&A[0]===bs?[]:A,h];A=P,l?l(t,3,V):t(...V)}finally{Jt=W}}}else c.run()};return a&&a(k),c=new ya(f),c.scheduler=o?()=>o(k,!1):k,h=T=>Gc(T,!1,c),d=c.onStop=()=>{const T=Ms.get(c);if(T){if(l)l(T,4);else for(const P of T)P();Ms.delete(c)}},t?s?k(!0):A=c.run():o?o(k.bind(null,!0),!0):c.run(),x.pause=c.pause.bind(c),x.resume=c.resume.bind(c),x.stop=x,x}function wt(e,t=1/0,r){if(t<=0||!fe(e)||e.__v_skip||(r=r||new Set,r.has(e)))return e;if(r.add(e),t--,_e(e))wt(e.value,t,r);else if(F(e))for(let s=0;s<e.length;s++)wt(e[s],t,r);else if(Or(e)||br(e))e.forEach(s=>{wt(s,t,r)});else if(fa(e)){for(const s in e)wt(e[s],t,r);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&wt(e[s],t,r)}return e}/**
* @vue/runtime-core v3.5.15
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function hs(e,t,r,s){try{return s?e(...s):e()}catch(n){Xs(n,t,r)}}function et(e,t,r,s){if(z(e)){const n=hs(e,t,r,s);return n&&ca(n)&&n.catch(i=>{Xs(i,t,r)}),n}if(F(e)){const n=[];for(let i=0;i<e.length;i++)n.push(et(e[i],t,r,s));return n}}function Xs(e,t,r,s=!0){const n=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||ce;if(t){let a=t.parent;const l=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${r}`;for(;a;){const c=a.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,l,u)===!1)return}a=a.parent}if(i){St(),hs(i,null,10,[e,l,u]),xt();return}}Qc(e,r,n,s,o)}function Qc(e,t,r,s=!0,n=!1){if(n)throw e;console.error(e)}const Re=[];let ct=-1;const xr=[];let jt=null,mr=0;const Da=Promise.resolve();let Us=null;function Zs(e){const t=Us||Da;return e?t.then(this?e.bind(this):e):t}function Yc(e){let t=ct+1,r=Re.length;for(;t<r;){const s=t+r>>>1,n=Re[s],i=ns(n);i<e||i===e&&n.flags&2?t=s+1:r=s}return t}function mi(e){if(!(e.flags&1)){const t=ns(e),r=Re[Re.length-1];!r||!(e.flags&2)&&t>=ns(r)?Re.push(e):Re.splice(Yc(t),0,e),e.flags|=1,Ma()}}function Ma(){Us||(Us=Da.then(Na))}function Xc(e){F(e)?xr.push(...e):jt&&e.id===-1?jt.splice(mr+1,0,e):e.flags&1||(xr.push(e),e.flags|=1),Ma()}function Mi(e,t,r=ct+1){for(;r<Re.length;r++){const s=Re[r];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Re.splice(r,1),r--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Ua(e){if(xr.length){const t=[...new Set(xr)].sort((r,s)=>ns(r)-ns(s));if(xr.length=0,jt){jt.push(...t);return}for(jt=t,mr=0;mr<jt.length;mr++){const r=jt[mr];r.flags&4&&(r.flags&=-2),r.flags&8||r(),r.flags&=-2}jt=null,mr=0}}const ns=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Na(e){try{for(ct=0;ct<Re.length;ct++){const t=Re[ct];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),hs(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;ct<Re.length;ct++){const t=Re[ct];t&&(t.flags&=-2)}ct=-1,Re.length=0,Ua(),Us=null,(Re.length||xr.length)&&Na()}}let $e=null,Fa=null;function Ns(e){const t=$e;return $e=e,Fa=e&&e.type.__scopeId||null,t}function De(e,t=$e,r){if(!t||e._n)return e;const s=(...n)=>{s._d&&Wi(-1);const i=Ns(t);let o;try{o=e(...n)}finally{Ns(i),s._d&&Wi(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function jn(e,t){if($e===null)return e;const r=sn($e),s=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[i,o,a,l=ce]=t[n];i&&(z(i)&&(i={mounted:i,updated:i}),i.deep&&wt(o),s.push({dir:i,instance:r,value:o,oldValue:void 0,arg:a,modifiers:l}))}return e}function Vt(e,t,r,s){const n=e.dirs,i=t&&t.dirs;for(let o=0;o<n.length;o++){const a=n[o];i&&(a.oldValue=i[o].value);let l=a.dir[s];l&&(St(),et(l,r,8,[e.el,a,e,t]),xt())}}const Zc=Symbol("_vte"),eu=e=>e.__isTeleport,or=Symbol("_leaveCb"),Ss=Symbol("_enterCb");function tu(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return vi(()=>{e.isMounted=!0}),za(()=>{e.isUnmounting=!0}),e}const Ve=[Function,Array],ru={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Ve,onEnter:Ve,onAfterEnter:Ve,onEnterCancelled:Ve,onBeforeLeave:Ve,onLeave:Ve,onAfterLeave:Ve,onLeaveCancelled:Ve,onBeforeAppear:Ve,onAppear:Ve,onAfterAppear:Ve,onAppearCancelled:Ve};function su(e,t){const{leavingVNodes:r}=e;let s=r.get(t.type);return s||(s=Object.create(null),r.set(t.type,s)),s}function In(e,t,r,s,n){const{appear:i,mode:o,persisted:a=!1,onBeforeEnter:l,onEnter:u,onAfterEnter:c,onEnterCancelled:f,onBeforeLeave:d,onLeave:h,onAfterLeave:m,onLeaveCancelled:v,onBeforeAppear:y,onAppear:x,onAfterAppear:A,onAppearCancelled:k}=t,T=String(e.key),P=su(r,e),W=($,G)=>{$&&et($,s,9,G)},V=($,G)=>{const ee=G[1];W($,G),F($)?$.every(D=>D.length<=1)&&ee():$.length<=1&&ee()},q={mode:o,persisted:a,beforeEnter($){let G=l;if(!r.isMounted)if(i)G=y||l;else return;$[or]&&$[or](!0);const ee=P[T];ee&&vr(e,ee)&&ee.el[or]&&ee.el[or](),W(G,[$])},enter($){let G=u,ee=c,D=f;if(!r.isMounted)if(i)G=x||u,ee=A||c,D=k||f;else return;let X=!1;const ye=$[Ss]=Pe=>{X||(X=!0,Pe?W(D,[$]):W(ee,[$]),q.delayedLeave&&q.delayedLeave(),$[Ss]=void 0)};G?V(G,[$,ye]):ye()},leave($,G){const ee=String(e.key);if($[Ss]&&$[Ss](!0),r.isUnmounting)return G();W(d,[$]);let D=!1;const X=$[or]=ye=>{D||(D=!0,G(),ye?W(v,[$]):W(m,[$]),$[or]=void 0,P[ee]===e&&delete P[ee])};P[ee]=e,h?V(h,[$,X]):X()},clone($){return In($,t,r,s)}};return q}function is(e,t){e.shapeFlag&6&&e.component?(e.transition=t,is(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ba(e,t=!1,r){let s=[],n=0;for(let i=0;i<e.length;i++){let o=e[i];const a=r==null?o.key:String(r)+String(o.key!=null?o.key:i);o.type===Ke?(o.patchFlag&128&&n++,s=s.concat(Ba(o.children,t,a))):(t||o.type!==Et)&&s.push(a!=null?tr(o,{key:a}):o)}if(n>1)for(let i=0;i<s.length;i++)s[i].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Rr(e,t){return z(e)?we({name:e.name},t,{setup:e}):e}function qa(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Fs(e,t,r,s,n=!1){if(F(e)){e.forEach((m,v)=>Fs(m,t&&(F(t)?t[v]:t),r,s,n));return}if(Kr(s)&&!n){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Fs(e,t,r,s.component.subTree);return}const i=s.shapeFlag&4?sn(s.component):s.el,o=n?null:i,{i:a,r:l}=e,u=t&&t.r,c=a.refs===ce?a.refs={}:a.refs,f=a.setupState,d=te(f),h=f===ce?()=>!1:m=>ie(d,m);if(u!=null&&u!==l&&(ve(u)?(c[u]=null,h(u)&&(f[u]=null)):_e(u)&&(u.value=null)),z(l))hs(l,a,12,[o,c]);else{const m=ve(l),v=_e(l);if(m||v){const y=()=>{if(e.f){const x=m?h(l)?f[l]:c[l]:l.value;n?F(x)&&ni(x,i):F(x)?x.includes(i)||x.push(i):m?(c[l]=[i],h(l)&&(f[l]=c[l])):(l.value=[i],e.k&&(c[e.k]=l.value))}else m?(c[l]=o,h(l)&&(f[l]=o)):v&&(l.value=o,e.k&&(c[e.k]=o))};o?(y.id=-1,Fe(y,r)):y()}}}Js().requestIdleCallback;Js().cancelIdleCallback;const Kr=e=>!!e.type.__asyncLoader,Ha=e=>e.type.__isKeepAlive;function nu(e,t){Va(e,"a",t)}function iu(e,t){Va(e,"da",t)}function Va(e,t,r=xe){const s=e.__wdc||(e.__wdc=()=>{let n=r;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(en(t,s,r),r){let n=r.parent;for(;n&&n.parent;)Ha(n.parent.vnode)&&ou(s,t,r,n),n=n.parent}}function ou(e,t,r,s){const n=en(t,e,s,!0);_i(()=>{ni(s[t],n)},r)}function en(e,t,r=xe,s=!1){if(r){const n=r[e]||(r[e]=[]),i=t.__weh||(t.__weh=(...o)=>{St();const a=ps(r),l=et(t,r,e,o);return a(),xt(),l});return s?n.unshift(i):n.push(i),i}}const kt=e=>(t,r=xe)=>{(!as||e==="sp")&&en(e,(...s)=>t(...s),r)},au=kt("bm"),vi=kt("m"),lu=kt("bu"),Ka=kt("u"),za=kt("bum"),_i=kt("um"),cu=kt("sp"),uu=kt("rtg"),fu=kt("rtc");function du(e,t=xe){en("ec",e,t)}const Wa="components";function yi(e,t){return Ja(Wa,e,!0,t)||e}const Ga=Symbol.for("v-ndc");function hu(e){return ve(e)?Ja(Wa,e,!1)||e:e||Ga}function Ja(e,t,r=!0,s=!1){const n=$e||xe;if(n){const i=n.type;{const a=rf(i,!1);if(a&&(a===t||a===Qe(t)||a===Gs(Qe(t))))return i}const o=Ui(n[e]||i[e],t)||Ui(n.appContext[e],t);return!o&&s?i:o}}function Ui(e,t){return e&&(e[t]||e[Qe(t)]||e[Gs(Qe(t))])}function pu(e,t,r,s){let n;const i=r,o=F(e);if(o||ve(e)){const a=o&&Nt(e);let l=!1,u=!1;a&&(l=!We(e),u=Ft(e),e=Ys(e)),n=new Array(e.length);for(let c=0,f=e.length;c<f;c++)n[c]=t(l?u?Ds(Ee(e[c])):Ee(e[c]):e[c],c,void 0,i)}else if(typeof e=="number"){n=new Array(e);for(let a=0;a<e;a++)n[a]=t(a+1,a,void 0,i)}else if(fe(e))if(e[Symbol.iterator])n=Array.from(e,(a,l)=>t(a,l,void 0,i));else{const a=Object.keys(e);n=new Array(a.length);for(let l=0,u=a.length;l<u;l++){const c=a[l];n[l]=t(e[c],c,l,i)}}else n=[];return n}const Ln=e=>e?ml(e)?sn(e):Ln(e.parent):null,zr=we(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ln(e.parent),$root:e=>Ln(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ya(e),$forceUpdate:e=>e.f||(e.f=()=>{mi(e.update)}),$nextTick:e=>e.n||(e.n=Zs.bind(e.proxy)),$watch:e=>Du.bind(e)}),gn=(e,t)=>e!==ce&&!e.__isScriptSetup&&ie(e,t),gu={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:r,setupState:s,data:n,props:i,accessCache:o,type:a,appContext:l}=e;let u;if(t[0]!=="$"){const h=o[t];if(h!==void 0)switch(h){case 1:return s[t];case 2:return n[t];case 4:return r[t];case 3:return i[t]}else{if(gn(s,t))return o[t]=1,s[t];if(n!==ce&&ie(n,t))return o[t]=2,n[t];if((u=e.propsOptions[0])&&ie(u,t))return o[t]=3,i[t];if(r!==ce&&ie(r,t))return o[t]=4,r[t];Dn&&(o[t]=0)}}const c=zr[t];let f,d;if(c)return t==="$attrs"&&Ce(e.attrs,"get",""),c(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(r!==ce&&ie(r,t))return o[t]=4,r[t];if(d=l.config.globalProperties,ie(d,t))return d[t]},set({_:e},t,r){const{data:s,setupState:n,ctx:i}=e;return gn(n,t)?(n[t]=r,!0):s!==ce&&ie(s,t)?(s[t]=r,!0):ie(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=r,!0)},has({_:{data:e,setupState:t,accessCache:r,ctx:s,appContext:n,propsOptions:i}},o){let a;return!!r[o]||e!==ce&&ie(e,o)||gn(t,o)||(a=i[0])&&ie(a,o)||ie(s,o)||ie(zr,o)||ie(n.config.globalProperties,o)},defineProperty(e,t,r){return r.get!=null?e._.accessCache[t]=0:ie(r,"value")&&this.set(e,t,r.value,null),Reflect.defineProperty(e,t,r)}};function Ni(e){return F(e)?e.reduce((t,r)=>(t[r]=null,t),{}):e}let Dn=!0;function mu(e){const t=Ya(e),r=e.proxy,s=e.ctx;Dn=!1,t.beforeCreate&&Fi(t.beforeCreate,e,"bc");const{data:n,computed:i,methods:o,watch:a,provide:l,inject:u,created:c,beforeMount:f,mounted:d,beforeUpdate:h,updated:m,activated:v,deactivated:y,beforeDestroy:x,beforeUnmount:A,destroyed:k,unmounted:T,render:P,renderTracked:W,renderTriggered:V,errorCaptured:q,serverPrefetch:$,expose:G,inheritAttrs:ee,components:D,directives:X,filters:ye}=t;if(u&&vu(u,s,null),o)for(const J in o){const se=o[J];z(se)&&(s[J]=se.bind(r))}if(n){const J=n.call(r,r);fe(J)&&(e.data=ds(J))}if(Dn=!0,i)for(const J in i){const se=i[J],pt=z(se)?se.bind(r,r):z(se.get)?se.get.bind(r,r):dt,Tt=!z(se)&&z(se.set)?se.set.bind(r):dt,rt=ue({get:pt,set:Tt});Object.defineProperty(s,J,{enumerable:!0,configurable:!0,get:()=>rt.value,set:je=>rt.value=je})}if(a)for(const J in a)Qa(a[J],s,r,J);if(l){const J=z(l)?l.call(r):l;Reflect.ownKeys(J).forEach(se=>{Ps(se,J[se])})}c&&Fi(c,e,"c");function ae(J,se){F(se)?se.forEach(pt=>J(pt.bind(r))):se&&J(se.bind(r))}if(ae(au,f),ae(vi,d),ae(lu,h),ae(Ka,m),ae(nu,v),ae(iu,y),ae(du,q),ae(fu,W),ae(uu,V),ae(za,A),ae(_i,T),ae(cu,$),F(G))if(G.length){const J=e.exposed||(e.exposed={});G.forEach(se=>{Object.defineProperty(J,se,{get:()=>r[se],set:pt=>r[se]=pt})})}else e.exposed||(e.exposed={});P&&e.render===dt&&(e.render=P),ee!=null&&(e.inheritAttrs=ee),D&&(e.components=D),X&&(e.directives=X),$&&qa(e)}function vu(e,t,r=dt){F(e)&&(e=Mn(e));for(const s in e){const n=e[s];let i;fe(n)?"default"in n?i=Ge(n.from||s,n.default,!0):i=Ge(n.from||s):i=Ge(n),_e(i)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[s]=i}}function Fi(e,t,r){et(F(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,r)}function Qa(e,t,r,s){let n=s.includes(".")?ul(r,s):()=>r[s];if(ve(e)){const i=t[e];z(i)&&Wr(n,i)}else if(z(e))Wr(n,e.bind(r));else if(fe(e))if(F(e))e.forEach(i=>Qa(i,t,r,s));else{const i=z(e.handler)?e.handler.bind(r):t[e.handler];z(i)&&Wr(n,i,e)}}function Ya(e){const t=e.type,{mixins:r,extends:s}=t,{mixins:n,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,a=i.get(t);let l;return a?l=a:!n.length&&!r&&!s?l=t:(l={},n.length&&n.forEach(u=>Bs(l,u,o,!0)),Bs(l,t,o)),fe(t)&&i.set(t,l),l}function Bs(e,t,r,s=!1){const{mixins:n,extends:i}=t;i&&Bs(e,i,r,!0),n&&n.forEach(o=>Bs(e,o,r,!0));for(const o in t)if(!(s&&o==="expose")){const a=_u[o]||r&&r[o];e[o]=a?a(e[o],t[o]):t[o]}return e}const _u={data:Bi,props:qi,emits:qi,methods:Fr,computed:Fr,beforeCreate:Oe,created:Oe,beforeMount:Oe,mounted:Oe,beforeUpdate:Oe,updated:Oe,beforeDestroy:Oe,beforeUnmount:Oe,destroyed:Oe,unmounted:Oe,activated:Oe,deactivated:Oe,errorCaptured:Oe,serverPrefetch:Oe,components:Fr,directives:Fr,watch:wu,provide:Bi,inject:yu};function Bi(e,t){return t?e?function(){return we(z(e)?e.call(this,this):e,z(t)?t.call(this,this):t)}:t:e}function yu(e,t){return Fr(Mn(e),Mn(t))}function Mn(e){if(F(e)){const t={};for(let r=0;r<e.length;r++)t[e[r]]=e[r];return t}return e}function Oe(e,t){return e?[...new Set([].concat(e,t))]:t}function Fr(e,t){return e?we(Object.create(null),e,t):t}function qi(e,t){return e?F(e)&&F(t)?[...new Set([...e,...t])]:we(Object.create(null),Ni(e),Ni(t??{})):t}function wu(e,t){if(!e)return t;if(!t)return e;const r=we(Object.create(null),e);for(const s in t)r[s]=Oe(e[s],t[s]);return r}function Xa(){return{app:null,config:{isNativeTag:ac,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let bu=0;function Su(e,t){return function(s,n=null){z(s)||(s=we({},s)),n!=null&&!fe(n)&&(n=null);const i=Xa(),o=new WeakSet,a=[];let l=!1;const u=i.app={_uid:bu++,_component:s,_props:n,_container:null,_context:i,_instance:null,version:nf,get config(){return i.config},set config(c){},use(c,...f){return o.has(c)||(c&&z(c.install)?(o.add(c),c.install(u,...f)):z(c)&&(o.add(c),c(u,...f))),u},mixin(c){return i.mixins.includes(c)||i.mixins.push(c),u},component(c,f){return f?(i.components[c]=f,u):i.components[c]},directive(c,f){return f?(i.directives[c]=f,u):i.directives[c]},mount(c,f,d){if(!l){const h=u._ceVNode||re(s,n);return h.appContext=i,d===!0?d="svg":d===!1&&(d=void 0),e(h,c,d),l=!0,u._container=c,c.__vue_app__=u,sn(h.component)}},onUnmount(c){a.push(c)},unmount(){l&&(et(a,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return i.provides[c]=f,u},runWithContext(c){const f=Zt;Zt=u;try{return c()}finally{Zt=f}}};return u}}let Zt=null;function Ps(e,t){if(xe){let r=xe.provides;const s=xe.parent&&xe.parent.provides;s===r&&(r=xe.provides=Object.create(s)),r[e]=t}}function Ge(e,t,r=!1){const s=xe||$e;if(s||Zt){let n=Zt?Zt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return r&&z(t)?t.call(s&&s.proxy):t}}function xu(){return!!(xe||$e||Zt)}const Za={},el=()=>Object.create(Za),tl=e=>Object.getPrototypeOf(e)===Za;function Eu(e,t,r,s=!1){const n={},i=el();e.propsDefaults=Object.create(null),rl(e,t,n,i);for(const o in e.propsOptions[0])o in n||(n[o]=void 0);r?e.props=s?n:ja(n):e.type.props?e.props=n:e.props=i,e.attrs=i}function ku(e,t,r,s){const{props:n,attrs:i,vnode:{patchFlag:o}}=e,a=te(n),[l]=e.propsOptions;let u=!1;if((s||o>0)&&!(o&16)){if(o&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let d=c[f];if(tn(e.emitsOptions,d))continue;const h=t[d];if(l)if(ie(i,d))h!==i[d]&&(i[d]=h,u=!0);else{const m=Qe(d);n[m]=Un(l,a,m,h,e,!1)}else h!==i[d]&&(i[d]=h,u=!0)}}}else{rl(e,t,n,i)&&(u=!0);let c;for(const f in a)(!t||!ie(t,f)&&((c=qt(f))===f||!ie(t,c)))&&(l?r&&(r[f]!==void 0||r[c]!==void 0)&&(n[f]=Un(l,a,f,void 0,e,!0)):delete n[f]);if(i!==a)for(const f in i)(!t||!ie(t,f))&&(delete i[f],u=!0)}u&&yt(e.attrs,"set","")}function rl(e,t,r,s){const[n,i]=e.propsOptions;let o=!1,a;if(t)for(let l in t){if(qr(l))continue;const u=t[l];let c;n&&ie(n,c=Qe(l))?!i||!i.includes(c)?r[c]=u:(a||(a={}))[c]=u:tn(e.emitsOptions,l)||(!(l in s)||u!==s[l])&&(s[l]=u,o=!0)}if(i){const l=te(r),u=a||ce;for(let c=0;c<i.length;c++){const f=i[c];r[f]=Un(n,l,f,u[f],e,!ie(u,f))}}return o}function Un(e,t,r,s,n,i){const o=e[r];if(o!=null){const a=ie(o,"default");if(a&&s===void 0){const l=o.default;if(o.type!==Function&&!o.skipFactory&&z(l)){const{propsDefaults:u}=n;if(r in u)s=u[r];else{const c=ps(n);s=u[r]=l.call(null,t),c()}}else s=l;n.ce&&n.ce._setProp(r,s)}o[0]&&(i&&!a?s=!1:o[1]&&(s===""||s===qt(r))&&(s=!0))}return s}const Tu=new WeakMap;function sl(e,t,r=!1){const s=r?Tu:t.propsCache,n=s.get(e);if(n)return n;const i=e.props,o={},a=[];let l=!1;if(!z(e)){const c=f=>{l=!0;const[d,h]=sl(f,t,!0);we(o,d),h&&a.push(...h)};!r&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!i&&!l)return fe(e)&&s.set(e,wr),wr;if(F(i))for(let c=0;c<i.length;c++){const f=Qe(i[c]);Hi(f)&&(o[f]=ce)}else if(i)for(const c in i){const f=Qe(c);if(Hi(f)){const d=i[c],h=o[f]=F(d)||z(d)?{type:d}:we({},d),m=h.type;let v=!1,y=!0;if(F(m))for(let x=0;x<m.length;++x){const A=m[x],k=z(A)&&A.name;if(k==="Boolean"){v=!0;break}else k==="String"&&(y=!1)}else v=z(m)&&m.name==="Boolean";h[0]=v,h[1]=y,(v||ie(h,"default"))&&a.push(f)}}const u=[o,a];return fe(e)&&s.set(e,u),u}function Hi(e){return e[0]!=="$"&&!qr(e)}const wi=e=>e[0]==="_"||e==="$stable",bi=e=>F(e)?e.map(ft):[ft(e)],Cu=(e,t,r)=>{if(t._n)return t;const s=De((...n)=>bi(t(...n)),r);return s._c=!1,s},nl=(e,t,r)=>{const s=e._ctx;for(const n in e){if(wi(n))continue;const i=e[n];if(z(i))t[n]=Cu(n,i,s);else if(i!=null){const o=bi(i);t[n]=()=>o}}},il=(e,t)=>{const r=bi(t);e.slots.default=()=>r},ol=(e,t,r)=>{for(const s in t)(r||!wi(s))&&(e[s]=t[s])},Au=(e,t,r)=>{const s=e.slots=el();if(e.vnode.shapeFlag&32){const n=t._;n?(ol(s,t,r),r&&da(s,"_",n,!0)):nl(t,s)}else t&&il(e,t)},Pu=(e,t,r)=>{const{vnode:s,slots:n}=e;let i=!0,o=ce;if(s.shapeFlag&32){const a=t._;a?r&&a===1?i=!1:ol(n,t,r):(i=!t.$stable,nl(t,n)),o=t}else t&&(il(e,t),o={default:1});if(i)for(const a in n)!wi(a)&&o[a]==null&&delete n[a]},Fe=Hu;function Ou(e){return Ru(e)}function Ru(e,t){const r=Js();r.__VUE__=!0;const{insert:s,remove:n,patchProp:i,createElement:o,createText:a,createComment:l,setText:u,setElementText:c,parentNode:f,nextSibling:d,setScopeId:h=dt,insertStaticContent:m}=e,v=(p,g,_,w=null,E=null,S=null,j=void 0,R=null,O=!!g.dynamicChildren)=>{if(p===g)return;p&&!vr(p,g)&&(w=b(p),je(p,E,S,!0),p=null),g.patchFlag===-2&&(O=!1,g.dynamicChildren=null);const{type:C,ref:H,shapeFlag:L}=g;switch(C){case rn:y(p,g,_,w);break;case Et:x(p,g,_,w);break;case Os:p==null&&A(g,_,w,j);break;case Ke:D(p,g,_,w,E,S,j,R,O);break;default:L&1?P(p,g,_,w,E,S,j,R,O):L&6?X(p,g,_,w,E,S,j,R,O):(L&64||L&128)&&C.process(p,g,_,w,E,S,j,R,O,N)}H!=null&&E&&Fs(H,p&&p.ref,S,g||p,!g)},y=(p,g,_,w)=>{if(p==null)s(g.el=a(g.children),_,w);else{const E=g.el=p.el;g.children!==p.children&&u(E,g.children)}},x=(p,g,_,w)=>{p==null?s(g.el=l(g.children||""),_,w):g.el=p.el},A=(p,g,_,w)=>{[p.el,p.anchor]=m(p.children,g,_,w,p.el,p.anchor)},k=({el:p,anchor:g},_,w)=>{let E;for(;p&&p!==g;)E=d(p),s(p,_,w),p=E;s(g,_,w)},T=({el:p,anchor:g})=>{let _;for(;p&&p!==g;)_=d(p),n(p),p=_;n(g)},P=(p,g,_,w,E,S,j,R,O)=>{g.type==="svg"?j="svg":g.type==="math"&&(j="mathml"),p==null?W(g,_,w,E,S,j,R,O):$(p,g,E,S,j,R,O)},W=(p,g,_,w,E,S,j,R)=>{let O,C;const{props:H,shapeFlag:L,transition:B,dirs:K}=p;if(O=p.el=o(p.type,S,H&&H.is,H),L&8?c(O,p.children):L&16&&q(p.children,O,null,w,E,mn(p,S),j,R),K&&Vt(p,null,w,"created"),V(O,p,p.scopeId,j,w),H){for(const de in H)de!=="value"&&!qr(de)&&i(O,de,null,H[de],S,w);"value"in H&&i(O,"value",null,H.value,S),(C=H.onVnodeBeforeMount)&&ot(C,w,p)}K&&Vt(p,null,w,"beforeMount");const Z=$u(E,B);Z&&B.beforeEnter(O),s(O,g,_),((C=H&&H.onVnodeMounted)||Z||K)&&Fe(()=>{C&&ot(C,w,p),Z&&B.enter(O),K&&Vt(p,null,w,"mounted")},E)},V=(p,g,_,w,E)=>{if(_&&h(p,_),w)for(let S=0;S<w.length;S++)h(p,w[S]);if(E){let S=E.subTree;if(g===S||dl(S.type)&&(S.ssContent===g||S.ssFallback===g)){const j=E.vnode;V(p,j,j.scopeId,j.slotScopeIds,E.parent)}}},q=(p,g,_,w,E,S,j,R,O=0)=>{for(let C=O;C<p.length;C++){const H=p[C]=R?It(p[C]):ft(p[C]);v(null,H,g,_,w,E,S,j,R)}},$=(p,g,_,w,E,S,j)=>{const R=g.el=p.el;let{patchFlag:O,dynamicChildren:C,dirs:H}=g;O|=p.patchFlag&16;const L=p.props||ce,B=g.props||ce;let K;if(_&&Kt(_,!1),(K=B.onVnodeBeforeUpdate)&&ot(K,_,g,p),H&&Vt(g,p,_,"beforeUpdate"),_&&Kt(_,!0),(L.innerHTML&&B.innerHTML==null||L.textContent&&B.textContent==null)&&c(R,""),C?G(p.dynamicChildren,C,R,_,w,mn(g,E),S):j||se(p,g,R,null,_,w,mn(g,E),S,!1),O>0){if(O&16)ee(R,L,B,_,E);else if(O&2&&L.class!==B.class&&i(R,"class",null,B.class,E),O&4&&i(R,"style",L.style,B.style,E),O&8){const Z=g.dynamicProps;for(let de=0;de<Z.length;de++){const oe=Z[de],Ue=L[oe],Ie=B[oe];(Ie!==Ue||oe==="value")&&i(R,oe,Ue,Ie,E,_)}}O&1&&p.children!==g.children&&c(R,g.children)}else!j&&C==null&&ee(R,L,B,_,E);((K=B.onVnodeUpdated)||H)&&Fe(()=>{K&&ot(K,_,g,p),H&&Vt(g,p,_,"updated")},w)},G=(p,g,_,w,E,S,j)=>{for(let R=0;R<g.length;R++){const O=p[R],C=g[R],H=O.el&&(O.type===Ke||!vr(O,C)||O.shapeFlag&198)?f(O.el):_;v(O,C,H,null,w,E,S,j,!0)}},ee=(p,g,_,w,E)=>{if(g!==_){if(g!==ce)for(const S in g)!qr(S)&&!(S in _)&&i(p,S,g[S],null,E,w);for(const S in _){if(qr(S))continue;const j=_[S],R=g[S];j!==R&&S!=="value"&&i(p,S,R,j,E,w)}"value"in _&&i(p,"value",g.value,_.value,E)}},D=(p,g,_,w,E,S,j,R,O)=>{const C=g.el=p?p.el:a(""),H=g.anchor=p?p.anchor:a("");let{patchFlag:L,dynamicChildren:B,slotScopeIds:K}=g;K&&(R=R?R.concat(K):K),p==null?(s(C,_,w),s(H,_,w),q(g.children||[],_,H,E,S,j,R,O)):L>0&&L&64&&B&&p.dynamicChildren?(G(p.dynamicChildren,B,_,E,S,j,R),(g.key!=null||E&&g===E.subTree)&&al(p,g,!0)):se(p,g,_,H,E,S,j,R,O)},X=(p,g,_,w,E,S,j,R,O)=>{g.slotScopeIds=R,p==null?g.shapeFlag&512?E.ctx.activate(g,_,w,j,O):ye(g,_,w,E,S,j,O):Pe(p,g,O)},ye=(p,g,_,w,E,S,j)=>{const R=p.component=Qu(p,w,E);if(Ha(p)&&(R.ctx.renderer=N),Xu(R,!1,j),R.asyncDep){if(E&&E.registerDep(R,ae,j),!p.el){const O=R.subTree=re(Et);x(null,O,g,_)}}else ae(R,p,g,_,E,S,j)},Pe=(p,g,_)=>{const w=g.component=p.component;if(Bu(p,g,_))if(w.asyncDep&&!w.asyncResolved){J(w,g,_);return}else w.next=g,w.update();else g.el=p.el,w.vnode=g},ae=(p,g,_,w,E,S,j)=>{const R=()=>{if(p.isMounted){let{next:L,bu:B,u:K,parent:Z,vnode:de}=p;{const nt=ll(p);if(nt){L&&(L.el=de.el,J(p,L,j)),nt.asyncDep.then(()=>{p.isUnmounted||R()});return}}let oe=L,Ue;Kt(p,!1),L?(L.el=de.el,J(p,L,j)):L=de,B&&As(B),(Ue=L.props&&L.props.onVnodeBeforeUpdate)&&ot(Ue,Z,L,de),Kt(p,!0);const Ie=Ki(p),st=p.subTree;p.subTree=Ie,v(st,Ie,f(st.el),b(st),p,E,S),L.el=Ie.el,oe===null&&qu(p,Ie.el),K&&Fe(K,E),(Ue=L.props&&L.props.onVnodeUpdated)&&Fe(()=>ot(Ue,Z,L,de),E)}else{let L;const{el:B,props:K}=g,{bm:Z,m:de,parent:oe,root:Ue,type:Ie}=p,st=Kr(g);Kt(p,!1),Z&&As(Z),!st&&(L=K&&K.onVnodeBeforeMount)&&ot(L,oe,g),Kt(p,!0);{Ue.ce&&Ue.ce._injectChildStyle(Ie);const nt=p.subTree=Ki(p);v(null,nt,_,w,p,E,S),g.el=nt.el}if(de&&Fe(de,E),!st&&(L=K&&K.onVnodeMounted)){const nt=g;Fe(()=>ot(L,oe,nt),E)}(g.shapeFlag&256||oe&&Kr(oe.vnode)&&oe.vnode.shapeFlag&256)&&p.a&&Fe(p.a,E),p.isMounted=!0,g=_=w=null}};p.scope.on();const O=p.effect=new ya(R);p.scope.off();const C=p.update=O.run.bind(O),H=p.job=O.runIfDirty.bind(O);H.i=p,H.id=p.uid,O.scheduler=()=>mi(H),Kt(p,!0),C()},J=(p,g,_)=>{g.component=p;const w=p.vnode.props;p.vnode=g,p.next=null,ku(p,g.props,w,_),Pu(p,g.children,_),St(),Mi(p),xt()},se=(p,g,_,w,E,S,j,R,O=!1)=>{const C=p&&p.children,H=p?p.shapeFlag:0,L=g.children,{patchFlag:B,shapeFlag:K}=g;if(B>0){if(B&128){Tt(C,L,_,w,E,S,j,R,O);return}else if(B&256){pt(C,L,_,w,E,S,j,R,O);return}}K&8?(H&16&&He(C,E,S),L!==C&&c(_,L)):H&16?K&16?Tt(C,L,_,w,E,S,j,R,O):He(C,E,S,!0):(H&8&&c(_,""),K&16&&q(L,_,w,E,S,j,R,O))},pt=(p,g,_,w,E,S,j,R,O)=>{p=p||wr,g=g||wr;const C=p.length,H=g.length,L=Math.min(C,H);let B;for(B=0;B<L;B++){const K=g[B]=O?It(g[B]):ft(g[B]);v(p[B],K,_,null,E,S,j,R,O)}C>H?He(p,E,S,!0,!1,L):q(g,_,w,E,S,j,R,O,L)},Tt=(p,g,_,w,E,S,j,R,O)=>{let C=0;const H=g.length;let L=p.length-1,B=H-1;for(;C<=L&&C<=B;){const K=p[C],Z=g[C]=O?It(g[C]):ft(g[C]);if(vr(K,Z))v(K,Z,_,null,E,S,j,R,O);else break;C++}for(;C<=L&&C<=B;){const K=p[L],Z=g[B]=O?It(g[B]):ft(g[B]);if(vr(K,Z))v(K,Z,_,null,E,S,j,R,O);else break;L--,B--}if(C>L){if(C<=B){const K=B+1,Z=K<H?g[K].el:w;for(;C<=B;)v(null,g[C]=O?It(g[C]):ft(g[C]),_,Z,E,S,j,R,O),C++}}else if(C>B)for(;C<=L;)je(p[C],E,S,!0),C++;else{const K=C,Z=C,de=new Map;for(C=Z;C<=B;C++){const Ne=g[C]=O?It(g[C]):ft(g[C]);Ne.key!=null&&de.set(Ne.key,C)}let oe,Ue=0;const Ie=B-Z+1;let st=!1,nt=0;const jr=new Array(Ie);for(C=0;C<Ie;C++)jr[C]=0;for(C=K;C<=L;C++){const Ne=p[C];if(Ue>=Ie){je(Ne,E,S,!0);continue}let it;if(Ne.key!=null)it=de.get(Ne.key);else for(oe=Z;oe<=B;oe++)if(jr[oe-Z]===0&&vr(Ne,g[oe])){it=oe;break}it===void 0?je(Ne,E,S,!0):(jr[it-Z]=C+1,it>=nt?nt=it:st=!0,v(Ne,g[it],_,null,E,S,j,R,O),Ue++)}const Ri=st?ju(jr):wr;for(oe=Ri.length-1,C=Ie-1;C>=0;C--){const Ne=Z+C,it=g[Ne],$i=Ne+1<H?g[Ne+1].el:w;jr[C]===0?v(null,it,_,$i,E,S,j,R,O):st&&(oe<0||C!==Ri[oe]?rt(it,_,$i,2):oe--)}}},rt=(p,g,_,w,E=null)=>{const{el:S,type:j,transition:R,children:O,shapeFlag:C}=p;if(C&6){rt(p.component.subTree,g,_,w);return}if(C&128){p.suspense.move(g,_,w);return}if(C&64){j.move(p,g,_,N);return}if(j===Ke){s(S,g,_);for(let L=0;L<O.length;L++)rt(O[L],g,_,w);s(p.anchor,g,_);return}if(j===Os){k(p,g,_);return}if(w!==2&&C&1&&R)if(w===0)R.beforeEnter(S),s(S,g,_),Fe(()=>R.enter(S),E);else{const{leave:L,delayLeave:B,afterLeave:K}=R,Z=()=>{p.ctx.isUnmounted?n(S):s(S,g,_)},de=()=>{L(S,()=>{Z(),K&&K()})};B?B(S,Z,de):de()}else s(S,g,_)},je=(p,g,_,w=!1,E=!1)=>{const{type:S,props:j,ref:R,children:O,dynamicChildren:C,shapeFlag:H,patchFlag:L,dirs:B,cacheIndex:K}=p;if(L===-2&&(E=!1),R!=null&&(St(),Fs(R,null,_,p,!0),xt()),K!=null&&(g.renderCache[K]=void 0),H&256){g.ctx.deactivate(p);return}const Z=H&1&&B,de=!Kr(p);let oe;if(de&&(oe=j&&j.onVnodeBeforeUnmount)&&ot(oe,g,p),H&6)_s(p.component,_,w);else{if(H&128){p.suspense.unmount(_,w);return}Z&&Vt(p,null,g,"beforeUnmount"),H&64?p.type.remove(p,g,_,N,w):C&&!C.hasOnce&&(S!==Ke||L>0&&L&64)?He(C,g,_,!1,!0):(S===Ke&&L&384||!E&&H&16)&&He(O,g,_),w&&sr(p)}(de&&(oe=j&&j.onVnodeUnmounted)||Z)&&Fe(()=>{oe&&ot(oe,g,p),Z&&Vt(p,null,g,"unmounted")},_)},sr=p=>{const{type:g,el:_,anchor:w,transition:E}=p;if(g===Ke){nr(_,w);return}if(g===Os){T(p);return}const S=()=>{n(_),E&&!E.persisted&&E.afterLeave&&E.afterLeave()};if(p.shapeFlag&1&&E&&!E.persisted){const{leave:j,delayLeave:R}=E,O=()=>j(_,S);R?R(p.el,S,O):O()}else S()},nr=(p,g)=>{let _;for(;p!==g;)_=d(p),n(p),p=_;n(g)},_s=(p,g,_)=>{const{bum:w,scope:E,job:S,subTree:j,um:R,m:O,a:C,parent:H,slots:{__:L}}=p;Vi(O),Vi(C),w&&As(w),H&&F(L)&&L.forEach(B=>{H.renderCache[B]=void 0}),E.stop(),S&&(S.flags|=8,je(j,p,g,_)),R&&Fe(R,g),Fe(()=>{p.isUnmounted=!0},g),g&&g.pendingBranch&&!g.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===g.pendingId&&(g.deps--,g.deps===0&&g.resolve())},He=(p,g,_,w=!1,E=!1,S=0)=>{for(let j=S;j<p.length;j++)je(p[j],g,_,w,E)},b=p=>{if(p.shapeFlag&6)return b(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const g=d(p.anchor||p.el),_=g&&g[Zc];return _?d(_):g};let M=!1;const I=(p,g,_)=>{p==null?g._vnode&&je(g._vnode,null,null,!0):v(g._vnode||null,p,g,null,null,null,_),g._vnode=p,M||(M=!0,Mi(),Ua(),M=!1)},N={p:v,um:je,m:rt,r:sr,mt:ye,mc:q,pc:se,pbc:G,n:b,o:e};return{render:I,hydrate:void 0,createApp:Su(I)}}function mn({type:e,props:t},r){return r==="svg"&&e==="foreignObject"||r==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:r}function Kt({effect:e,job:t},r){r?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function $u(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function al(e,t,r=!1){const s=e.children,n=t.children;if(F(s)&&F(n))for(let i=0;i<s.length;i++){const o=s[i];let a=n[i];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=n[i]=It(n[i]),a.el=o.el),!r&&a.patchFlag!==-2&&al(o,a)),a.type===rn&&(a.el=o.el),a.type===Et&&!a.el&&(a.el=o.el)}}function ju(e){const t=e.slice(),r=[0];let s,n,i,o,a;const l=e.length;for(s=0;s<l;s++){const u=e[s];if(u!==0){if(n=r[r.length-1],e[n]<u){t[s]=n,r.push(s);continue}for(i=0,o=r.length-1;i<o;)a=i+o>>1,e[r[a]]<u?i=a+1:o=a;u<e[r[i]]&&(i>0&&(t[s]=r[i-1]),r[i]=s)}}for(i=r.length,o=r[i-1];i-- >0;)r[i]=o,o=t[o];return r}function ll(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ll(t)}function Vi(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Iu=Symbol.for("v-scx"),Lu=()=>Ge(Iu);function Wr(e,t,r){return cl(e,t,r)}function cl(e,t,r=ce){const{immediate:s,deep:n,flush:i,once:o}=r,a=we({},r),l=t&&s||!t&&i!=="post";let u;if(as){if(i==="sync"){const h=Lu();u=h.__watcherHandles||(h.__watcherHandles=[])}else if(!l){const h=()=>{};return h.stop=dt,h.resume=dt,h.pause=dt,h}}const c=xe;a.call=(h,m,v)=>et(h,c,m,v);let f=!1;i==="post"?a.scheduler=h=>{Fe(h,c&&c.suspense)}:i!=="sync"&&(f=!0,a.scheduler=(h,m)=>{m?h():mi(h)}),a.augmentJob=h=>{t&&(h.flags|=4),f&&(h.flags|=2,c&&(h.id=c.uid,h.i=c))};const d=Jc(e,t,a);return as&&(u?u.push(d):l&&d()),d}function Du(e,t,r){const s=this.proxy,n=ve(e)?e.includes(".")?ul(s,e):()=>s[e]:e.bind(s,s);let i;z(t)?i=t:(i=t.handler,r=t);const o=ps(this),a=cl(n,i.bind(s),r);return o(),a}function ul(e,t){const r=t.split(".");return()=>{let s=e;for(let n=0;n<r.length&&s;n++)s=s[r[n]];return s}}const Mu=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Qe(t)}Modifiers`]||e[`${qt(t)}Modifiers`];function Uu(e,t,...r){if(e.isUnmounted)return;const s=e.vnode.props||ce;let n=r;const i=t.startsWith("update:"),o=i&&Mu(s,t.slice(7));o&&(o.trim&&(n=r.map(c=>ve(c)?c.trim():c)),o.number&&(n=r.map(Is)));let a,l=s[a=un(t)]||s[a=un(Qe(t))];!l&&i&&(l=s[a=un(qt(t))]),l&&et(l,e,6,n);const u=s[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,et(u,e,6,n)}}function fl(e,t,r=!1){const s=t.emitsCache,n=s.get(e);if(n!==void 0)return n;const i=e.emits;let o={},a=!1;if(!z(e)){const l=u=>{const c=fl(u,t,!0);c&&(a=!0,we(o,c))};!r&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!i&&!a?(fe(e)&&s.set(e,null),null):(F(i)?i.forEach(l=>o[l]=null):we(o,i),fe(e)&&s.set(e,o),o)}function tn(e,t){return!e||!zs(t)?!1:(t=t.slice(2).replace(/Once$/,""),ie(e,t[0].toLowerCase()+t.slice(1))||ie(e,qt(t))||ie(e,t))}function Ki(e){const{type:t,vnode:r,proxy:s,withProxy:n,propsOptions:[i],slots:o,attrs:a,emit:l,render:u,renderCache:c,props:f,data:d,setupState:h,ctx:m,inheritAttrs:v}=e,y=Ns(e);let x,A;try{if(r.shapeFlag&4){const T=n||s,P=T;x=ft(u.call(P,T,c,f,h,d,m)),A=a}else{const T=t;x=ft(T.length>1?T(f,{attrs:a,slots:o,emit:l}):T(f,null)),A=t.props?a:Nu(a)}}catch(T){Gr.length=0,Xs(T,e,1),x=re(Et)}let k=x;if(A&&v!==!1){const T=Object.keys(A),{shapeFlag:P}=k;T.length&&P&7&&(i&&T.some(si)&&(A=Fu(A,i)),k=tr(k,A,!1,!0))}return r.dirs&&(k=tr(k,null,!1,!0),k.dirs=k.dirs?k.dirs.concat(r.dirs):r.dirs),r.transition&&is(k,r.transition),x=k,Ns(y),x}const Nu=e=>{let t;for(const r in e)(r==="class"||r==="style"||zs(r))&&((t||(t={}))[r]=e[r]);return t},Fu=(e,t)=>{const r={};for(const s in e)(!si(s)||!(s.slice(9)in t))&&(r[s]=e[s]);return r};function Bu(e,t,r){const{props:s,children:n,component:i}=e,{props:o,children:a,patchFlag:l}=t,u=i.emitsOptions;if(t.dirs||t.transition)return!0;if(r&&l>=0){if(l&1024)return!0;if(l&16)return s?zi(s,o,u):!!o;if(l&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const d=c[f];if(o[d]!==s[d]&&!tn(u,d))return!0}}}else return(n||a)&&(!a||!a.$stable)?!0:s===o?!1:s?o?zi(s,o,u):!0:!!o;return!1}function zi(e,t,r){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let n=0;n<s.length;n++){const i=s[n];if(t[i]!==e[i]&&!tn(r,i))return!0}return!1}function qu({vnode:e,parent:t},r){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=r,t=t.parent;else break}}const dl=e=>e.__isSuspense;function Hu(e,t){t&&t.pendingBranch?F(e)?t.effects.push(...e):t.effects.push(e):Xc(e)}const Ke=Symbol.for("v-fgt"),rn=Symbol.for("v-txt"),Et=Symbol.for("v-cmt"),Os=Symbol.for("v-stc"),Gr=[];let qe=null;function Ae(e=!1){Gr.push(qe=e?null:[])}function Vu(){Gr.pop(),qe=Gr[Gr.length-1]||null}let os=1;function Wi(e,t=!1){os+=e,e<0&&qe&&t&&(qe.hasOnce=!0)}function hl(e){return e.dynamicChildren=os>0?qe||wr:null,Vu(),os>0&&qe&&qe.push(e),e}function Me(e,t,r,s,n,i){return hl(U(e,t,r,s,n,i,!0))}function pl(e,t,r,s,n){return hl(re(e,t,r,s,n,!0))}function qs(e){return e?e.__v_isVNode===!0:!1}function vr(e,t){return e.type===t.type&&e.key===t.key}const gl=({key:e})=>e??null,Rs=({ref:e,ref_key:t,ref_for:r})=>(typeof e=="number"&&(e=""+e),e!=null?ve(e)||_e(e)||z(e)?{i:$e,r:e,k:t,f:!!r}:e:null);function U(e,t=null,r=null,s=0,n=null,i=e===Ke?0:1,o=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&gl(t),ref:t&&Rs(t),scopeId:Fa,slotScopeIds:null,children:r,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:$e};return a?(Si(l,r),i&128&&e.normalize(l)):r&&(l.shapeFlag|=ve(r)?8:16),os>0&&!o&&qe&&(l.patchFlag>0||i&6)&&l.patchFlag!==32&&qe.push(l),l}const re=Ku;function Ku(e,t=null,r=null,s=0,n=null,i=!1){if((!e||e===Ga)&&(e=Et),qs(e)){const a=tr(e,t,!0);return r&&Si(a,r),os>0&&!i&&qe&&(a.shapeFlag&6?qe[qe.indexOf(e)]=a:qe.push(a)),a.patchFlag=-2,a}if(sf(e)&&(e=e.__vccOpts),t){t=zu(t);let{class:a,style:l}=t;a&&!ve(a)&&(t.class=Qs(a)),fe(l)&&(pi(l)&&!F(l)&&(l=we({},l)),t.style=oi(l))}const o=ve(e)?1:dl(e)?128:eu(e)?64:fe(e)?4:z(e)?2:0;return U(e,t,r,s,n,o,i,!0)}function zu(e){return e?pi(e)||tl(e)?we({},e):e:null}function tr(e,t,r=!1,s=!1){const{props:n,ref:i,patchFlag:o,children:a,transition:l}=e,u=t?Wu(n||{},t):n,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&gl(u),ref:t&&t.ref?r&&i?F(i)?i.concat(Rs(t)):[i,Rs(t)]:Rs(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ke?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&tr(e.ssContent),ssFallback:e.ssFallback&&tr(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&s&&is(c,l.clone(c)),c}function Ye(e=" ",t=0){return re(rn,null,e,t)}function Gi(e,t){const r=re(Os,null,e);return r.staticCount=t,r}function $s(e="",t=!1){return t?(Ae(),pl(Et,null,e)):re(Et,null,e)}function ft(e){return e==null||typeof e=="boolean"?re(Et):F(e)?re(Ke,null,e.slice()):qs(e)?It(e):re(rn,null,String(e))}function It(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:tr(e)}function Si(e,t){let r=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(F(t))r=16;else if(typeof t=="object")if(s&65){const n=t.default;n&&(n._c&&(n._d=!1),Si(e,n()),n._c&&(n._d=!0));return}else{r=32;const n=t._;!n&&!tl(t)?t._ctx=$e:n===3&&$e&&($e.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else z(t)?(t={default:t,_ctx:$e},r=32):(t=String(t),s&64?(r=16,t=[Ye(t)]):r=8);e.children=t,e.shapeFlag|=r}function Wu(...e){const t={};for(let r=0;r<e.length;r++){const s=e[r];for(const n in s)if(n==="class")t.class!==s.class&&(t.class=Qs([t.class,s.class]));else if(n==="style")t.style=oi([t.style,s.style]);else if(zs(n)){const i=t[n],o=s[n];o&&i!==o&&!(F(i)&&i.includes(o))&&(t[n]=i?[].concat(i,o):o)}else n!==""&&(t[n]=s[n])}return t}function ot(e,t,r,s=null){et(e,t,7,[r,s])}const Gu=Xa();let Ju=0;function Qu(e,t,r){const s=e.type,n=(t?t.appContext:e.appContext)||Gu,i={uid:Ju++,vnode:e,type:s,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ma(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:sl(s,n),emitsOptions:fl(s,n),emit:null,emitted:null,propsDefaults:ce,inheritAttrs:s.inheritAttrs,ctx:ce,data:ce,props:ce,attrs:ce,slots:ce,refs:ce,setupState:ce,setupContext:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Uu.bind(null,i),e.ce&&e.ce(i),i}let xe=null;const Yu=()=>xe||$e;let Hs,Nn;{const e=Js(),t=(r,s)=>{let n;return(n=e[r])||(n=e[r]=[]),n.push(s),i=>{n.length>1?n.forEach(o=>o(i)):n[0](i)}};Hs=t("__VUE_INSTANCE_SETTERS__",r=>xe=r),Nn=t("__VUE_SSR_SETTERS__",r=>as=r)}const ps=e=>{const t=xe;return Hs(e),e.scope.on(),()=>{e.scope.off(),Hs(t)}},Ji=()=>{xe&&xe.scope.off(),Hs(null)};function ml(e){return e.vnode.shapeFlag&4}let as=!1;function Xu(e,t=!1,r=!1){t&&Nn(t);const{props:s,children:n}=e.vnode,i=ml(e);Eu(e,s,i,t),Au(e,n,r||t);const o=i?Zu(e,t):void 0;return t&&Nn(!1),o}function Zu(e,t){const r=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,gu);const{setup:s}=r;if(s){St();const n=e.setupContext=s.length>1?tf(e):null,i=ps(e),o=hs(s,e,0,[e.props,n]),a=ca(o);if(xt(),i(),(a||e.sp)&&!Kr(e)&&qa(e),a){if(o.then(Ji,Ji),t)return o.then(l=>{Qi(e,l)}).catch(l=>{Xs(l,e,0)});e.asyncDep=o}else Qi(e,o)}else vl(e)}function Qi(e,t,r){z(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:fe(t)&&(e.setupState=La(t)),vl(e)}function vl(e,t,r){const s=e.type;e.render||(e.render=s.render||dt);{const n=ps(e);St();try{mu(e)}finally{xt(),n()}}}const ef={get(e,t){return Ce(e,"get",""),e[t]}};function tf(e){const t=r=>{e.exposed=r||{}};return{attrs:new Proxy(e.attrs,ef),slots:e.slots,emit:e.emit,expose:t}}function sn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(La(gi(e.exposed)),{get(t,r){if(r in t)return t[r];if(r in zr)return zr[r](e)},has(t,r){return r in t||r in zr}})):e.proxy}function rf(e,t=!0){return z(e)?e.displayName||e.name:e.name||t&&e.__name}function sf(e){return z(e)&&"__vccOpts"in e}const ue=(e,t)=>Wc(e,t,as);function ut(e,t,r){const s=arguments.length;return s===2?fe(t)&&!F(t)?qs(t)?re(e,null,[t]):re(e,t):re(e,null,t):(s>3?r=Array.prototype.slice.call(arguments,2):s===3&&qs(r)&&(r=[r]),re(e,t,r))}const nf="3.5.15";/**
* @vue/runtime-dom v3.5.15
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Fn;const Yi=typeof window<"u"&&window.trustedTypes;if(Yi)try{Fn=Yi.createPolicy("vue",{createHTML:e=>e})}catch{}const _l=Fn?e=>Fn.createHTML(e):e=>e,of="http://www.w3.org/2000/svg",af="http://www.w3.org/1998/Math/MathML",vt=typeof document<"u"?document:null,Xi=vt&&vt.createElement("template"),lf={insert:(e,t,r)=>{t.insertBefore(e,r||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,r,s)=>{const n=t==="svg"?vt.createElementNS(of,e):t==="mathml"?vt.createElementNS(af,e):r?vt.createElement(e,{is:r}):vt.createElement(e);return e==="select"&&s&&s.multiple!=null&&n.setAttribute("multiple",s.multiple),n},createText:e=>vt.createTextNode(e),createComment:e=>vt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>vt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,r,s,n,i){const o=r?r.previousSibling:t.lastChild;if(n&&(n===i||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),r),!(n===i||!(n=n.nextSibling)););else{Xi.innerHTML=_l(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const a=Xi.content;if(s==="svg"||s==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,r)}return[o?o.nextSibling:t.firstChild,r?r.previousSibling:t.lastChild]}},Ct="transition",Lr="animation",kr=Symbol("_vtc"),yl={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},cf=we({},ru,yl),zt=(e,t=[])=>{F(e)?e.forEach(r=>r(...t)):e&&e(...t)},Zi=e=>e?F(e)?e.some(t=>t.length>1):e.length>1:!1;function uf(e){const t={};for(const D in e)D in yl||(t[D]=e[D]);if(e.css===!1)return t;const{name:r="v",type:s,duration:n,enterFromClass:i=`${r}-enter-from`,enterActiveClass:o=`${r}-enter-active`,enterToClass:a=`${r}-enter-to`,appearFromClass:l=i,appearActiveClass:u=o,appearToClass:c=a,leaveFromClass:f=`${r}-leave-from`,leaveActiveClass:d=`${r}-leave-active`,leaveToClass:h=`${r}-leave-to`}=e,m=ff(n),v=m&&m[0],y=m&&m[1],{onBeforeEnter:x,onEnter:A,onEnterCancelled:k,onLeave:T,onLeaveCancelled:P,onBeforeAppear:W=x,onAppear:V=A,onAppearCancelled:q=k}=t,$=(D,X,ye,Pe)=>{D._enterCancelled=Pe,Pt(D,X?c:a),Pt(D,X?u:o),ye&&ye()},G=(D,X)=>{D._isLeaving=!1,Pt(D,f),Pt(D,h),Pt(D,d),X&&X()},ee=D=>(X,ye)=>{const Pe=D?V:A,ae=()=>$(X,D,ye);zt(Pe,[X,ae]),eo(()=>{Pt(X,D?l:i),at(X,D?c:a),Zi(Pe)||to(X,s,v,ae)})};return we(t,{onBeforeEnter(D){zt(x,[D]),at(D,i),at(D,o)},onBeforeAppear(D){zt(W,[D]),at(D,l),at(D,u)},onEnter:ee(!1),onAppear:ee(!0),onLeave(D,X){D._isLeaving=!0;const ye=()=>G(D,X);at(D,f),D._enterCancelled?(at(D,d),Bn()):(Bn(),at(D,d)),eo(()=>{D._isLeaving&&(Pt(D,f),at(D,h),Zi(T)||to(D,s,y,ye))}),zt(T,[D,ye])},onEnterCancelled(D){$(D,!1,void 0,!0),zt(k,[D])},onAppearCancelled(D){$(D,!0,void 0,!0),zt(q,[D])},onLeaveCancelled(D){G(D),zt(P,[D])}})}function ff(e){if(e==null)return null;if(fe(e))return[vn(e.enter),vn(e.leave)];{const t=vn(e);return[t,t]}}function vn(e){return dc(e)}function at(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.add(r)),(e[kr]||(e[kr]=new Set)).add(t)}function Pt(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const r=e[kr];r&&(r.delete(t),r.size||(e[kr]=void 0))}function eo(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let df=0;function to(e,t,r,s){const n=e._endId=++df,i=()=>{n===e._endId&&s()};if(r!=null)return setTimeout(i,r);const{type:o,timeout:a,propCount:l}=wl(e,t);if(!o)return s();const u=o+"end";let c=0;const f=()=>{e.removeEventListener(u,d),i()},d=h=>{h.target===e&&++c>=l&&f()};setTimeout(()=>{c<l&&f()},a+1),e.addEventListener(u,d)}function wl(e,t){const r=window.getComputedStyle(e),s=m=>(r[m]||"").split(", "),n=s(`${Ct}Delay`),i=s(`${Ct}Duration`),o=ro(n,i),a=s(`${Lr}Delay`),l=s(`${Lr}Duration`),u=ro(a,l);let c=null,f=0,d=0;t===Ct?o>0&&(c=Ct,f=o,d=i.length):t===Lr?u>0&&(c=Lr,f=u,d=l.length):(f=Math.max(o,u),c=f>0?o>u?Ct:Lr:null,d=c?c===Ct?i.length:l.length:0);const h=c===Ct&&/\b(transform|all)(,|$)/.test(s(`${Ct}Property`).toString());return{type:c,timeout:f,propCount:d,hasTransform:h}}function ro(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((r,s)=>so(r)+so(e[s])))}function so(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Bn(){return document.body.offsetHeight}function hf(e,t,r){const s=e[kr];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):r?e.setAttribute("class",t):e.className=t}const no=Symbol("_vod"),pf=Symbol("_vsh"),gf=Symbol(""),mf=/(^|;)\s*display\s*:/;function vf(e,t,r){const s=e.style,n=ve(r);let i=!1;if(r&&!n){if(t)if(ve(t))for(const o of t.split(";")){const a=o.slice(0,o.indexOf(":")).trim();r[a]==null&&js(s,a,"")}else for(const o in t)r[o]==null&&js(s,o,"");for(const o in r)o==="display"&&(i=!0),js(s,o,r[o])}else if(n){if(t!==r){const o=s[gf];o&&(r+=";"+o),s.cssText=r,i=mf.test(r)}}else t&&e.removeAttribute("style");no in e&&(e[no]=i?s.display:"",e[pf]&&(s.display="none"))}const io=/\s*!important$/;function js(e,t,r){if(F(r))r.forEach(s=>js(e,t,s));else if(r==null&&(r=""),t.startsWith("--"))e.setProperty(t,r);else{const s=_f(e,t);io.test(r)?e.setProperty(qt(s),r.replace(io,""),"important"):e[s]=r}}const oo=["Webkit","Moz","ms"],_n={};function _f(e,t){const r=_n[t];if(r)return r;let s=Qe(t);if(s!=="filter"&&s in e)return _n[t]=s;s=Gs(s);for(let n=0;n<oo.length;n++){const i=oo[n]+s;if(i in e)return _n[t]=i}return t}const ao="http://www.w3.org/1999/xlink";function lo(e,t,r,s,n,i=_c(t)){s&&t.startsWith("xlink:")?r==null?e.removeAttributeNS(ao,t.slice(6,t.length)):e.setAttributeNS(ao,t,r):r==null||i&&!ha(r)?e.removeAttribute(t):e.setAttribute(t,i?"":ht(r)?String(r):r)}function co(e,t,r,s,n){if(t==="innerHTML"||t==="textContent"){r!=null&&(e[t]=t==="innerHTML"?_l(r):r);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const a=i==="OPTION"?e.getAttribute("value")||"":e.value,l=r==null?e.type==="checkbox"?"on":"":String(r);(a!==l||!("_value"in e))&&(e.value=l),r==null&&e.removeAttribute(t),e._value=r;return}let o=!1;if(r===""||r==null){const a=typeof e[t];a==="boolean"?r=ha(r):r==null&&a==="string"?(r="",o=!0):a==="number"&&(r=0,o=!0)}try{e[t]=r}catch{}o&&e.removeAttribute(n||t)}function bt(e,t,r,s){e.addEventListener(t,r,s)}function yf(e,t,r,s){e.removeEventListener(t,r,s)}const uo=Symbol("_vei");function wf(e,t,r,s,n=null){const i=e[uo]||(e[uo]={}),o=i[t];if(s&&o)o.value=s;else{const[a,l]=bf(t);if(s){const u=i[t]=Ef(s,n);bt(e,a,u,l)}else o&&(yf(e,a,o,l),i[t]=void 0)}}const fo=/(?:Once|Passive|Capture)$/;function bf(e){let t;if(fo.test(e)){t={};let s;for(;s=e.match(fo);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):qt(e.slice(2)),t]}let yn=0;const Sf=Promise.resolve(),xf=()=>yn||(Sf.then(()=>yn=0),yn=Date.now());function Ef(e,t){const r=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=r.attached)return;et(kf(s,r.value),t,5,[s])};return r.value=e,r.attached=xf(),r}function kf(e,t){if(F(t)){const r=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{r.call(e),e._stopped=!0},t.map(s=>n=>!n._stopped&&s&&s(n))}else return t}const ho=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Tf=(e,t,r,s,n,i)=>{const o=n==="svg";t==="class"?hf(e,s,o):t==="style"?vf(e,r,s):zs(t)?si(t)||wf(e,t,r,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Cf(e,t,s,o))?(co(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&lo(e,t,s,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ve(s))?co(e,Qe(t),s,i,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),lo(e,t,s,o))};function Cf(e,t,r,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&ho(t)&&z(r));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return ho(t)&&ve(r)?!1:t in e}const bl=new WeakMap,Sl=new WeakMap,Vs=Symbol("_moveCb"),po=Symbol("_enterCb"),Af=e=>(delete e.props.mode,e),Pf=Af({name:"TransitionGroup",props:we({},cf,{tag:String,moveClass:String}),setup(e,{slots:t}){const r=Yu(),s=tu();let n,i;return Ka(()=>{if(!n.length)return;const o=e.moveClass||`${e.name||"v"}-move`;if(!If(n[0].el,r.vnode.el,o)){n=[];return}n.forEach(Rf),n.forEach($f);const a=n.filter(jf);Bn(),a.forEach(l=>{const u=l.el,c=u.style;at(u,o),c.transform=c.webkitTransform=c.transitionDuration="";const f=u[Vs]=d=>{d&&d.target!==u||(!d||/transform$/.test(d.propertyName))&&(u.removeEventListener("transitionend",f),u[Vs]=null,Pt(u,o))};u.addEventListener("transitionend",f)}),n=[]}),()=>{const o=te(e),a=uf(o);let l=o.tag||Ke;if(n=[],i)for(let u=0;u<i.length;u++){const c=i[u];c.el&&c.el instanceof Element&&(n.push(c),is(c,In(c,a,s,r)),bl.set(c,c.el.getBoundingClientRect()))}i=t.default?Ba(t.default()):[];for(let u=0;u<i.length;u++){const c=i[u];c.key!=null&&is(c,In(c,a,s,r))}return re(l,null,i)}}}),Of=Pf;function Rf(e){const t=e.el;t[Vs]&&t[Vs](),t[po]&&t[po]()}function $f(e){Sl.set(e,e.el.getBoundingClientRect())}function jf(e){const t=bl.get(e),r=Sl.get(e),s=t.left-r.left,n=t.top-r.top;if(s||n){const i=e.el.style;return i.transform=i.webkitTransform=`translate(${s}px,${n}px)`,i.transitionDuration="0s",e}}function If(e,t,r){const s=e.cloneNode(),n=e[kr];n&&n.forEach(a=>{a.split(/\s+/).forEach(l=>l&&s.classList.remove(l))}),r.split(/\s+/).forEach(a=>a&&s.classList.add(a)),s.style.display="none";const i=t.nodeType===1?t:t.parentNode;i.appendChild(s);const{hasTransform:o}=wl(s);return i.removeChild(s),o}const Bt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return F(t)?r=>As(t,r):t};function Lf(e){e.target.composing=!0}function go(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Je=Symbol("_assign"),qn={created(e,{modifiers:{lazy:t,trim:r,number:s}},n){e[Je]=Bt(n);const i=s||n.props&&n.props.type==="number";bt(e,t?"change":"input",o=>{if(o.target.composing)return;let a=e.value;r&&(a=a.trim()),i&&(a=Is(a)),e[Je](a)}),r&&bt(e,"change",()=>{e.value=e.value.trim()}),t||(bt(e,"compositionstart",Lf),bt(e,"compositionend",go),bt(e,"change",go))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:r,modifiers:{lazy:s,trim:n,number:i}},o){if(e[Je]=Bt(o),e.composing)return;const a=(i||e.type==="number")&&!/^0\d/.test(e.value)?Is(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(s&&t===r||n&&e.value.trim()===l)||(e.value=l))}},em={deep:!0,created(e,t,r){e[Je]=Bt(r),bt(e,"change",()=>{const s=e._modelValue,n=Tr(e),i=e.checked,o=e[Je];if(F(s)){const a=ai(s,n),l=a!==-1;if(i&&!l)o(s.concat(n));else if(!i&&l){const u=[...s];u.splice(a,1),o(u)}}else if(Or(s)){const a=new Set(s);i?a.add(n):a.delete(n),o(a)}else o(xl(e,i))})},mounted:mo,beforeUpdate(e,t,r){e[Je]=Bt(r),mo(e,t,r)}};function mo(e,{value:t,oldValue:r},s){e._modelValue=t;let n;if(F(t))n=ai(t,s.props.value)>-1;else if(Or(t))n=t.has(s.props.value);else{if(t===r)return;n=er(t,xl(e,!0))}e.checked!==n&&(e.checked=n)}const tm={created(e,{value:t},r){e.checked=er(t,r.props.value),e[Je]=Bt(r),bt(e,"change",()=>{e[Je](Tr(e))})},beforeUpdate(e,{value:t,oldValue:r},s){e[Je]=Bt(s),t!==r&&(e.checked=er(t,s.props.value))}},rm={deep:!0,created(e,{value:t,modifiers:{number:r}},s){const n=Or(t);bt(e,"change",()=>{const i=Array.prototype.filter.call(e.options,o=>o.selected).map(o=>r?Is(Tr(o)):Tr(o));e[Je](e.multiple?n?new Set(i):i:i[0]),e._assigning=!0,Zs(()=>{e._assigning=!1})}),e[Je]=Bt(s)},mounted(e,{value:t}){vo(e,t)},beforeUpdate(e,t,r){e[Je]=Bt(r)},updated(e,{value:t}){e._assigning||vo(e,t)}};function vo(e,t){const r=e.multiple,s=F(t);if(!(r&&!s&&!Or(t))){for(let n=0,i=e.options.length;n<i;n++){const o=e.options[n],a=Tr(o);if(r)if(s){const l=typeof a;l==="string"||l==="number"?o.selected=t.some(u=>String(u)===String(a)):o.selected=ai(t,a)>-1}else o.selected=t.has(a);else if(er(Tr(o),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!r&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Tr(e){return"_value"in e?e._value:e.value}function xl(e,t){const r=t?"_trueValue":"_falseValue";return r in e?e[r]:t}const Df=["ctrl","shift","alt","meta"],Mf={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Df.some(r=>e[`${r}Key`]&&!t.includes(r))},Uf=(e,t)=>{const r=e._withMods||(e._withMods={}),s=t.join(".");return r[s]||(r[s]=(n,...i)=>{for(let o=0;o<t.length;o++){const a=Mf[t[o]];if(a&&a(n,t))return}return e(n,...i)})},Nf={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},_o=(e,t)=>{const r=e._withKeys||(e._withKeys={}),s=t.join(".");return r[s]||(r[s]=n=>{if(!("key"in n))return;const i=qt(n.key);if(t.some(o=>o===i||Nf[o]===i))return e(n)})},Ff=we({patchProp:Tf},lf);let yo;function Bf(){return yo||(yo=Ou(Ff))}const qf=(...e)=>{const t=Bf().createApp(...e),{mount:r}=t;return t.mount=s=>{const n=Vf(s);if(!n)return;const i=t._component;!z(i)&&!i.render&&!i.template&&(i.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const o=r(n,!1,Hf(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),o},t};function Hf(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Vf(e){return ve(e)?document.querySelector(e):e}/*!
 * pinia v3.0.2
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let El;const nn=e=>El=e,kl=Symbol();function Hn(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Jr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Jr||(Jr={}));function Kf(){const e=va(!0),t=e.run(()=>me({}));let r=[],s=[];const n=gi({install(i){nn(n),n._a=i,i.provide(kl,n),i.config.globalProperties.$pinia=n,s.forEach(o=>r.push(o)),s=[]},use(i){return this._a?r.push(i):s.push(i),this},_p:r,_a:null,_e:e,_s:new Map,state:t});return n}const Tl=()=>{};function wo(e,t,r,s=Tl){e.push(t);const n=()=>{const i=e.indexOf(t);i>-1&&(e.splice(i,1),s())};return!r&&_a()&&wc(n),n}function ar(e,...t){e.slice().forEach(r=>{r(...t)})}const zf=e=>e(),bo=Symbol(),wn=Symbol();function Vn(e,t){e instanceof Map&&t instanceof Map?t.forEach((r,s)=>e.set(s,r)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const r in t){if(!t.hasOwnProperty(r))continue;const s=t[r],n=e[r];Hn(n)&&Hn(s)&&e.hasOwnProperty(r)&&!_e(s)&&!Nt(s)?e[r]=Vn(n,s):e[r]=s}return e}const Wf=Symbol();function Gf(e){return!Hn(e)||!Object.prototype.hasOwnProperty.call(e,Wf)}const{assign:Ot}=Object;function Jf(e){return!!(_e(e)&&e.effect)}function Qf(e,t,r,s){const{state:n,actions:i,getters:o}=t,a=r.state.value[e];let l;function u(){a||(r.state.value[e]=n?n():{});const c=Hc(r.state.value[e]);return Ot(c,i,Object.keys(o||{}).reduce((f,d)=>(f[d]=gi(ue(()=>{nn(r);const h=r._s.get(e);return o[d].call(h,h)})),f),{}))}return l=Cl(e,u,t,r,s,!0),l}function Cl(e,t,r={},s,n,i){let o;const a=Ot({actions:{}},r),l={deep:!0};let u,c,f=[],d=[],h;const m=s.state.value[e];!i&&!m&&(s.state.value[e]={}),me({});let v;function y(q){let $;u=c=!1,typeof q=="function"?(q(s.state.value[e]),$={type:Jr.patchFunction,storeId:e,events:h}):(Vn(s.state.value[e],q),$={type:Jr.patchObject,payload:q,storeId:e,events:h});const G=v=Symbol();Zs().then(()=>{v===G&&(u=!0)}),c=!0,ar(f,$,s.state.value[e])}const x=i?function(){const{state:$}=r,G=$?$():{};this.$patch(ee=>{Ot(ee,G)})}:Tl;function A(){o.stop(),f=[],d=[],s._s.delete(e)}const k=(q,$="")=>{if(bo in q)return q[wn]=$,q;const G=function(){nn(s);const ee=Array.from(arguments),D=[],X=[];function ye(J){D.push(J)}function Pe(J){X.push(J)}ar(d,{args:ee,name:G[wn],store:P,after:ye,onError:Pe});let ae;try{ae=q.apply(this&&this.$id===e?this:P,ee)}catch(J){throw ar(X,J),J}return ae instanceof Promise?ae.then(J=>(ar(D,J),J)).catch(J=>(ar(X,J),Promise.reject(J))):(ar(D,ae),ae)};return G[bo]=!0,G[wn]=$,G},T={_p:s,$id:e,$onAction:wo.bind(null,d),$patch:y,$reset:x,$subscribe(q,$={}){const G=wo(f,q,$.detached,()=>ee()),ee=o.run(()=>Wr(()=>s.state.value[e],D=>{($.flush==="sync"?c:u)&&q({storeId:e,type:Jr.direct,events:h},D)},Ot({},l,$)));return G},$dispose:A},P=ds(T);s._s.set(e,P);const V=(s._a&&s._a.runWithContext||zf)(()=>s._e.run(()=>(o=va()).run(()=>t({action:k}))));for(const q in V){const $=V[q];if(_e($)&&!Jf($)||Nt($))i||(m&&Gf($)&&(_e($)?$.value=m[q]:Vn($,m[q])),s.state.value[e][q]=$);else if(typeof $=="function"){const G=k($,q);V[q]=G,a.actions[q]=$}}return Ot(P,V),Ot(te(P),V),Object.defineProperty(P,"$state",{get:()=>s.state.value[e],set:q=>{y($=>{Ot($,q)})}}),s._p.forEach(q=>{Ot(P,o.run(()=>q({store:P,app:s._a,pinia:s,options:a})))}),m&&i&&r.hydrate&&r.hydrate(P.$state,m),u=!0,c=!0,P}/*! #__NO_SIDE_EFFECTS__ */function gs(e,t,r){let s;const n=typeof t=="function";s=n?r:t;function i(o,a){const l=xu();return o=o||(l?Ge(kl,null):null),o&&nn(o),o=El,o._s.has(e)||(n?Cl(e,t,s,o):Qf(e,s,o)),o._s.get(e)}return i.$id=e,i}const Yf="modulepreload",Xf=function(e){return"/"+e},So={},Te=function(t,r,s){let n=Promise.resolve();if(r&&r.length>0){let o=function(u){return Promise.all(u.map(c=>Promise.resolve(c).then(f=>({status:"fulfilled",value:f}),f=>({status:"rejected",reason:f}))))};document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),l=(a==null?void 0:a.nonce)||(a==null?void 0:a.getAttribute("nonce"));n=o(r.map(u=>{if(u=Xf(u),u in So)return;So[u]=!0;const c=u.endsWith(".css"),f=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${f}`))return;const d=document.createElement("link");if(d.rel=c?"stylesheet":Yf,c||(d.as="script"),d.crossOrigin="",d.href=u,l&&d.setAttribute("nonce",l),document.head.appendChild(d),c)return new Promise((h,m)=>{d.addEventListener("load",h),d.addEventListener("error",()=>m(new Error(`Unable to preload CSS for ${u}`)))})}))}function i(o){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o}return n.then(o=>{for(const a of o||[])a.status==="rejected"&&i(a.reason);return t().catch(i)})},Zf=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...r)=>Te(async()=>{const{default:s}=await Promise.resolve().then(()=>$r);return{default:s}},void 0).then(({default:s})=>s(...r)):t=fetch,(...r)=>t(...r)};class xi extends Error{constructor(t,r="FunctionsError",s){super(t),this.name=r,this.context=s}}class ed extends xi{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class td extends xi{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class rd extends xi{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var Kn;(function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"})(Kn||(Kn={}));var sd=function(e,t,r,s){function n(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(c){try{u(s.next(c))}catch(f){o(f)}}function l(c){try{u(s.throw(c))}catch(f){o(f)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((s=s.apply(e,t||[])).next())})};class nd{constructor(t,{headers:r={},customFetch:s,region:n=Kn.Any}={}){this.url=t,this.headers=r,this.region=n,this.fetch=Zf(s)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,r={}){var s;return sd(this,void 0,void 0,function*(){try{const{headers:n,method:i,body:o}=r;let a={},{region:l}=r;l||(l=this.region),l&&l!=="any"&&(a["x-region"]=l);let u;o&&(n&&!Object.prototype.hasOwnProperty.call(n,"Content-Type")||!n)&&(typeof Blob<"u"&&o instanceof Blob||o instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",u=o):typeof o=="string"?(a["Content-Type"]="text/plain",u=o):typeof FormData<"u"&&o instanceof FormData?u=o:(a["Content-Type"]="application/json",u=JSON.stringify(o)));const c=yield this.fetch(`${this.url}/${t}`,{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),n),body:u}).catch(m=>{throw new ed(m)}),f=c.headers.get("x-relay-error");if(f&&f==="true")throw new td(c);if(!c.ok)throw new rd(c);let d=((s=c.headers.get("Content-Type"))!==null&&s!==void 0?s:"text/plain").split(";")[0].trim(),h;return d==="application/json"?h=yield c.json():d==="application/octet-stream"?h=yield c.blob():d==="text/event-stream"?h=c:d==="multipart/form-data"?h=yield c.formData():h=yield c.text(),{data:h,error:null}}catch(n){return{data:null,error:n}}})}}function id(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function od(e){if(Object.prototype.hasOwnProperty.call(e,"__esModule"))return e;var t=e.default;if(typeof t=="function"){var r=function s(){return this instanceof s?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(s){var n=Object.getOwnPropertyDescriptor(e,s);Object.defineProperty(r,s,n.get?n:{enumerable:!0,get:function(){return e[s]}})}),r}var be={},lr={},cr={},ur={},fr={},dr={},ad=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},Cr=ad();const ld=Cr.fetch,Al=Cr.fetch.bind(Cr),Pl=Cr.Headers,cd=Cr.Request,ud=Cr.Response,$r=Object.freeze(Object.defineProperty({__proto__:null,Headers:Pl,Request:cd,Response:ud,default:Al,fetch:ld},Symbol.toStringTag,{value:"Module"})),fd=od($r);var xs={},xo;function Ol(){if(xo)return xs;xo=1,Object.defineProperty(xs,"__esModule",{value:!0});class e extends Error{constructor(r){super(r.message),this.name="PostgrestError",this.details=r.details,this.hint=r.hint,this.code=r.code}}return xs.default=e,xs}var Eo;function Rl(){if(Eo)return dr;Eo=1;var e=dr&&dr.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(dr,"__esModule",{value:!0});const t=e(fd),r=e(Ol());class s{constructor(i){this.shouldThrowOnError=!1,this.method=i.method,this.url=i.url,this.headers=i.headers,this.schema=i.schema,this.body=i.body,this.shouldThrowOnError=i.shouldThrowOnError,this.signal=i.signal,this.isMaybeSingle=i.isMaybeSingle,i.fetch?this.fetch=i.fetch:typeof fetch>"u"?this.fetch=t.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(i,o){return this.headers=Object.assign({},this.headers),this.headers[i]=o,this}then(i,o){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const a=this.fetch;let l=a(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async u=>{var c,f,d;let h=null,m=null,v=null,y=u.status,x=u.statusText;if(u.ok){if(this.method!=="HEAD"){const P=await u.text();P===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?m=P:m=JSON.parse(P))}const k=(c=this.headers.Prefer)===null||c===void 0?void 0:c.match(/count=(exact|planned|estimated)/),T=(f=u.headers.get("content-range"))===null||f===void 0?void 0:f.split("/");k&&T&&T.length>1&&(v=parseInt(T[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(m)&&(m.length>1?(h={code:"PGRST116",details:`Results contain ${m.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},m=null,v=null,y=406,x="Not Acceptable"):m.length===1?m=m[0]:m=null)}else{const k=await u.text();try{h=JSON.parse(k),Array.isArray(h)&&u.status===404&&(m=[],h=null,y=200,x="OK")}catch{u.status===404&&k===""?(y=204,x="No Content"):h={message:k}}if(h&&this.isMaybeSingle&&(!((d=h==null?void 0:h.details)===null||d===void 0)&&d.includes("0 rows"))&&(h=null,y=200,x="OK"),h&&this.shouldThrowOnError)throw new r.default(h)}return{error:h,data:m,count:v,status:y,statusText:x}});return this.shouldThrowOnError||(l=l.catch(u=>{var c,f,d;return{error:{message:`${(c=u==null?void 0:u.name)!==null&&c!==void 0?c:"FetchError"}: ${u==null?void 0:u.message}`,details:`${(f=u==null?void 0:u.stack)!==null&&f!==void 0?f:""}`,hint:"",code:`${(d=u==null?void 0:u.code)!==null&&d!==void 0?d:""}`},data:null,count:null,status:0,statusText:""}})),l.then(i,o)}returns(){return this}overrideTypes(){return this}}return dr.default=s,dr}var ko;function $l(){if(ko)return fr;ko=1;var e=fr&&fr.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(fr,"__esModule",{value:!0});const t=e(Rl());class r extends t.default{select(n){let i=!1;const o=(n??"*").split("").map(a=>/\s/.test(a)&&!i?"":(a==='"'&&(i=!i),a)).join("");return this.url.searchParams.set("select",o),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(n,{ascending:i=!0,nullsFirst:o,foreignTable:a,referencedTable:l=a}={}){const u=l?`${l}.order`:"order",c=this.url.searchParams.get(u);return this.url.searchParams.set(u,`${c?`${c},`:""}${n}.${i?"asc":"desc"}${o===void 0?"":o?".nullsfirst":".nullslast"}`),this}limit(n,{foreignTable:i,referencedTable:o=i}={}){const a=typeof o>"u"?"limit":`${o}.limit`;return this.url.searchParams.set(a,`${n}`),this}range(n,i,{foreignTable:o,referencedTable:a=o}={}){const l=typeof a>"u"?"offset":`${a}.offset`,u=typeof a>"u"?"limit":`${a}.limit`;return this.url.searchParams.set(l,`${n}`),this.url.searchParams.set(u,`${i-n+1}`),this}abortSignal(n){return this.signal=n,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:n=!1,verbose:i=!1,settings:o=!1,buffers:a=!1,wal:l=!1,format:u="text"}={}){var c;const f=[n?"analyze":null,i?"verbose":null,o?"settings":null,a?"buffers":null,l?"wal":null].filter(Boolean).join("|"),d=(c=this.headers.Accept)!==null&&c!==void 0?c:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${u}; for="${d}"; options=${f};`,u==="json"?this:this}rollback(){var n;return((n=this.headers.Prefer)!==null&&n!==void 0?n:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}return fr.default=r,fr}var To;function Ei(){if(To)return ur;To=1;var e=ur&&ur.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(ur,"__esModule",{value:!0});const t=e($l());class r extends t.default{eq(n,i){return this.url.searchParams.append(n,`eq.${i}`),this}neq(n,i){return this.url.searchParams.append(n,`neq.${i}`),this}gt(n,i){return this.url.searchParams.append(n,`gt.${i}`),this}gte(n,i){return this.url.searchParams.append(n,`gte.${i}`),this}lt(n,i){return this.url.searchParams.append(n,`lt.${i}`),this}lte(n,i){return this.url.searchParams.append(n,`lte.${i}`),this}like(n,i){return this.url.searchParams.append(n,`like.${i}`),this}likeAllOf(n,i){return this.url.searchParams.append(n,`like(all).{${i.join(",")}}`),this}likeAnyOf(n,i){return this.url.searchParams.append(n,`like(any).{${i.join(",")}}`),this}ilike(n,i){return this.url.searchParams.append(n,`ilike.${i}`),this}ilikeAllOf(n,i){return this.url.searchParams.append(n,`ilike(all).{${i.join(",")}}`),this}ilikeAnyOf(n,i){return this.url.searchParams.append(n,`ilike(any).{${i.join(",")}}`),this}is(n,i){return this.url.searchParams.append(n,`is.${i}`),this}in(n,i){const o=Array.from(new Set(i)).map(a=>typeof a=="string"&&new RegExp("[,()]").test(a)?`"${a}"`:`${a}`).join(",");return this.url.searchParams.append(n,`in.(${o})`),this}contains(n,i){return typeof i=="string"?this.url.searchParams.append(n,`cs.${i}`):Array.isArray(i)?this.url.searchParams.append(n,`cs.{${i.join(",")}}`):this.url.searchParams.append(n,`cs.${JSON.stringify(i)}`),this}containedBy(n,i){return typeof i=="string"?this.url.searchParams.append(n,`cd.${i}`):Array.isArray(i)?this.url.searchParams.append(n,`cd.{${i.join(",")}}`):this.url.searchParams.append(n,`cd.${JSON.stringify(i)}`),this}rangeGt(n,i){return this.url.searchParams.append(n,`sr.${i}`),this}rangeGte(n,i){return this.url.searchParams.append(n,`nxl.${i}`),this}rangeLt(n,i){return this.url.searchParams.append(n,`sl.${i}`),this}rangeLte(n,i){return this.url.searchParams.append(n,`nxr.${i}`),this}rangeAdjacent(n,i){return this.url.searchParams.append(n,`adj.${i}`),this}overlaps(n,i){return typeof i=="string"?this.url.searchParams.append(n,`ov.${i}`):this.url.searchParams.append(n,`ov.{${i.join(",")}}`),this}textSearch(n,i,{config:o,type:a}={}){let l="";a==="plain"?l="pl":a==="phrase"?l="ph":a==="websearch"&&(l="w");const u=o===void 0?"":`(${o})`;return this.url.searchParams.append(n,`${l}fts${u}.${i}`),this}match(n){return Object.entries(n).forEach(([i,o])=>{this.url.searchParams.append(i,`eq.${o}`)}),this}not(n,i,o){return this.url.searchParams.append(n,`not.${i}.${o}`),this}or(n,{foreignTable:i,referencedTable:o=i}={}){const a=o?`${o}.or`:"or";return this.url.searchParams.append(a,`(${n})`),this}filter(n,i,o){return this.url.searchParams.append(n,`${i}.${o}`),this}}return ur.default=r,ur}var Co;function jl(){if(Co)return cr;Co=1;var e=cr&&cr.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(cr,"__esModule",{value:!0});const t=e(Ei());class r{constructor(n,{headers:i={},schema:o,fetch:a}){this.url=n,this.headers=i,this.schema=o,this.fetch=a}select(n,{head:i=!1,count:o}={}){const a=i?"HEAD":"GET";let l=!1;const u=(n??"*").split("").map(c=>/\s/.test(c)&&!l?"":(c==='"'&&(l=!l),c)).join("");return this.url.searchParams.set("select",u),o&&(this.headers.Prefer=`count=${o}`),new t.default({method:a,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(n,{count:i,defaultToNull:o=!0}={}){const a="POST",l=[];if(this.headers.Prefer&&l.push(this.headers.Prefer),i&&l.push(`count=${i}`),o||l.push("missing=default"),this.headers.Prefer=l.join(","),Array.isArray(n)){const u=n.reduce((c,f)=>c.concat(Object.keys(f)),[]);if(u.length>0){const c=[...new Set(u)].map(f=>`"${f}"`);this.url.searchParams.set("columns",c.join(","))}}return new t.default({method:a,url:this.url,headers:this.headers,schema:this.schema,body:n,fetch:this.fetch,allowEmpty:!1})}upsert(n,{onConflict:i,ignoreDuplicates:o=!1,count:a,defaultToNull:l=!0}={}){const u="POST",c=[`resolution=${o?"ignore":"merge"}-duplicates`];if(i!==void 0&&this.url.searchParams.set("on_conflict",i),this.headers.Prefer&&c.push(this.headers.Prefer),a&&c.push(`count=${a}`),l||c.push("missing=default"),this.headers.Prefer=c.join(","),Array.isArray(n)){const f=n.reduce((d,h)=>d.concat(Object.keys(h)),[]);if(f.length>0){const d=[...new Set(f)].map(h=>`"${h}"`);this.url.searchParams.set("columns",d.join(","))}}return new t.default({method:u,url:this.url,headers:this.headers,schema:this.schema,body:n,fetch:this.fetch,allowEmpty:!1})}update(n,{count:i}={}){const o="PATCH",a=[];return this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),this.headers.Prefer=a.join(","),new t.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:n,fetch:this.fetch,allowEmpty:!1})}delete({count:n}={}){const i="DELETE",o=[];return n&&o.push(`count=${n}`),this.headers.Prefer&&o.unshift(this.headers.Prefer),this.headers.Prefer=o.join(","),new t.default({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}return cr.default=r,cr}var Dr={},Mr={},Ao;function dd(){return Ao||(Ao=1,Object.defineProperty(Mr,"__esModule",{value:!0}),Mr.version=void 0,Mr.version="0.0.0-automated"),Mr}var Po;function hd(){if(Po)return Dr;Po=1,Object.defineProperty(Dr,"__esModule",{value:!0}),Dr.DEFAULT_HEADERS=void 0;const e=dd();return Dr.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${e.version}`},Dr}var Oo;function pd(){if(Oo)return lr;Oo=1;var e=lr&&lr.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(lr,"__esModule",{value:!0});const t=e(jl()),r=e(Ei()),s=hd();class n{constructor(o,{headers:a={},schema:l,fetch:u}={}){this.url=o,this.headers=Object.assign(Object.assign({},s.DEFAULT_HEADERS),a),this.schemaName=l,this.fetch=u}from(o){const a=new URL(`${this.url}/${o}`);return new t.default(a,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(o){return new n(this.url,{headers:this.headers,schema:o,fetch:this.fetch})}rpc(o,a={},{head:l=!1,get:u=!1,count:c}={}){let f;const d=new URL(`${this.url}/rpc/${o}`);let h;l||u?(f=l?"HEAD":"GET",Object.entries(a).filter(([v,y])=>y!==void 0).map(([v,y])=>[v,Array.isArray(y)?`{${y.join(",")}}`:`${y}`]).forEach(([v,y])=>{d.searchParams.append(v,y)})):(f="POST",h=a);const m=Object.assign({},this.headers);return c&&(m.Prefer=`count=${c}`),new r.default({method:f,url:d,headers:m,schema:this.schemaName,body:h,fetch:this.fetch,allowEmpty:!1})}}return lr.default=n,lr}var Ro;function gd(){if(Ro)return be;Ro=1;var e=be&&be.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(be,"__esModule",{value:!0}),be.PostgrestError=be.PostgrestBuilder=be.PostgrestTransformBuilder=be.PostgrestFilterBuilder=be.PostgrestQueryBuilder=be.PostgrestClient=void 0;const t=e(pd());be.PostgrestClient=t.default;const r=e(jl());be.PostgrestQueryBuilder=r.default;const s=e(Ei());be.PostgrestFilterBuilder=s.default;const n=e($l());be.PostgrestTransformBuilder=n.default;const i=e(Rl());be.PostgrestBuilder=i.default;const o=e(Ol());return be.PostgrestError=o.default,be.default={PostgrestClient:t.default,PostgrestQueryBuilder:r.default,PostgrestFilterBuilder:s.default,PostgrestTransformBuilder:n.default,PostgrestBuilder:i.default,PostgrestError:o.default},be}var md=gd();const vd=id(md),{PostgrestClient:_d,PostgrestQueryBuilder:sm,PostgrestFilterBuilder:nm,PostgrestTransformBuilder:im,PostgrestBuilder:om,PostgrestError:am}=vd,yd="2.11.2",wd={"X-Client-Info":`realtime-js/${yd}`},bd="1.0.0",Il=1e4,Sd=1e3;var Er;(function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"})(Er||(Er={}));var Be;(function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"})(Be||(Be={}));var Xe;(function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"})(Xe||(Xe={}));var zn;(function(e){e.websocket="websocket"})(zn||(zn={}));var Yt;(function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"})(Yt||(Yt={}));class xd{constructor(){this.HEADER_LENGTH=1}decode(t,r){return t.constructor===ArrayBuffer?r(this._binaryDecode(t)):r(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){const r=new DataView(t),s=new TextDecoder;return this._decodeBroadcast(t,r,s)}_decodeBroadcast(t,r,s){const n=r.getUint8(1),i=r.getUint8(2);let o=this.HEADER_LENGTH+2;const a=s.decode(t.slice(o,o+n));o=o+n;const l=s.decode(t.slice(o,o+i));o=o+i;const u=JSON.parse(s.decode(t.slice(o,t.byteLength)));return{ref:null,topic:a,event:l,payload:u}}}class Ll{constructor(t,r){this.callback=t,this.timerCalc=r,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=r}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var he;(function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"})(he||(he={}));const $o=(e,t,r={})=>{var s;const n=(s=r.skipTypes)!==null&&s!==void 0?s:[];return Object.keys(t).reduce((i,o)=>(i[o]=Ed(o,e,t,n),i),{})},Ed=(e,t,r,s)=>{const n=t.find(a=>a.name===e),i=n==null?void 0:n.type,o=r[e];return i&&!s.includes(i)?Dl(i,o):Wn(o)},Dl=(e,t)=>{if(e.charAt(0)==="_"){const r=e.slice(1,e.length);return Ad(t,r)}switch(e){case he.bool:return kd(t);case he.float4:case he.float8:case he.int2:case he.int4:case he.int8:case he.numeric:case he.oid:return Td(t);case he.json:case he.jsonb:return Cd(t);case he.timestamp:return Pd(t);case he.abstime:case he.date:case he.daterange:case he.int4range:case he.int8range:case he.money:case he.reltime:case he.text:case he.time:case he.timestamptz:case he.timetz:case he.tsrange:case he.tstzrange:return Wn(t);default:return Wn(t)}},Wn=e=>e,kd=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},Td=e=>{if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},Cd=e=>{if(typeof e=="string")try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},Ad=(e,t)=>{if(typeof e!="string")return e;const r=e.length-1,s=e[r];if(e[0]==="{"&&s==="}"){let i;const o=e.slice(1,r);try{i=JSON.parse("["+o+"]")}catch{i=o?o.split(","):[]}return i.map(a=>Dl(t,a))}return e},Pd=e=>typeof e=="string"?e.replace(" ","T"):e,Ml=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class bn{constructor(t,r,s={},n=Il){this.channel=t,this.event=r,this.payload=s,this.timeout=n,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,r){var s;return this._hasReceived(t)&&r((s=this.receivedResp)===null||s===void 0?void 0:s.response),this.recHooks.push({status:t,callback:r}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const t=r=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=r,this._matchReceive(r)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,r){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:r})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:r}){this.recHooks.filter(s=>s.status===t).forEach(s=>s.callback(r))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var jo;(function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"})(jo||(jo={}));class Qr{constructor(t,r){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const s=(r==null?void 0:r.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},n=>{const{onJoin:i,onLeave:o,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Qr.syncState(this.state,n,i,o),this.pendingDiffs.forEach(l=>{this.state=Qr.syncDiff(this.state,l,i,o)}),this.pendingDiffs=[],a()}),this.channel._on(s.diff,{},n=>{const{onJoin:i,onLeave:o,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(n):(this.state=Qr.syncDiff(this.state,n,i,o),a())}),this.onJoin((n,i,o)=>{this.channel._trigger("presence",{event:"join",key:n,currentPresences:i,newPresences:o})}),this.onLeave((n,i,o)=>{this.channel._trigger("presence",{event:"leave",key:n,currentPresences:i,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,r,s,n){const i=this.cloneDeep(t),o=this.transformState(r),a={},l={};return this.map(i,(u,c)=>{o[u]||(l[u]=c)}),this.map(o,(u,c)=>{const f=i[u];if(f){const d=c.map(y=>y.presence_ref),h=f.map(y=>y.presence_ref),m=c.filter(y=>h.indexOf(y.presence_ref)<0),v=f.filter(y=>d.indexOf(y.presence_ref)<0);m.length>0&&(a[u]=m),v.length>0&&(l[u]=v)}else a[u]=c}),this.syncDiff(i,{joins:a,leaves:l},s,n)}static syncDiff(t,r,s,n){const{joins:i,leaves:o}={joins:this.transformState(r.joins),leaves:this.transformState(r.leaves)};return s||(s=()=>{}),n||(n=()=>{}),this.map(i,(a,l)=>{var u;const c=(u=t[a])!==null&&u!==void 0?u:[];if(t[a]=this.cloneDeep(l),c.length>0){const f=t[a].map(h=>h.presence_ref),d=c.filter(h=>f.indexOf(h.presence_ref)<0);t[a].unshift(...d)}s(a,c,l)}),this.map(o,(a,l)=>{let u=t[a];if(!u)return;const c=l.map(f=>f.presence_ref);u=u.filter(f=>c.indexOf(f.presence_ref)<0),t[a]=u,n(a,u,l),u.length===0&&delete t[a]}),t}static map(t,r){return Object.getOwnPropertyNames(t).map(s=>r(s,t[s]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((r,s)=>{const n=t[s];return"metas"in n?r[s]=n.metas.map(i=>(i.presence_ref=i.phx_ref,delete i.phx_ref,delete i.phx_ref_prev,i)):r[s]=n,r},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var Io;(function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"})(Io||(Io={}));var Lo;(function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"})(Lo||(Lo={}));var _t;(function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"})(_t||(_t={}));class ki{constructor(t,r={config:{}},s){this.topic=t,this.params=r,this.socket=s,this.bindings={},this.state=Be.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},r.config),this.timeout=this.socket.timeout,this.joinPush=new bn(this,Xe.join,this.params,this.timeout),this.rejoinTimer=new Ll(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=Be.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(n=>n.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=Be.closed,this.socket._remove(this)}),this._onError(n=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,n),this.state=Be.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=Be.errored,this.rejoinTimer.scheduleTimeout())}),this._on(Xe.reply,{},(n,i)=>{this._trigger(this._replyEventName(i),n)}),this.presence=new Qr(this),this.broadcastEndpointURL=Ml(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,r=this.timeout){var s,n;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:i,presence:o,private:a}}=this.params;this._onError(c=>t==null?void 0:t(_t.CHANNEL_ERROR,c)),this._onClose(()=>t==null?void 0:t(_t.CLOSED));const l={},u={broadcast:i,presence:o,postgres_changes:(n=(s=this.bindings.postgres_changes)===null||s===void 0?void 0:s.map(c=>c.filter))!==null&&n!==void 0?n:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:u},l)),this.joinedOnce=!0,this._rejoin(r),this.joinPush.receive("ok",async({postgres_changes:c})=>{var f;if(this.socket.setAuth(),c===void 0){t==null||t(_t.SUBSCRIBED);return}else{const d=this.bindings.postgres_changes,h=(f=d==null?void 0:d.length)!==null&&f!==void 0?f:0,m=[];for(let v=0;v<h;v++){const y=d[v],{filter:{event:x,schema:A,table:k,filter:T}}=y,P=c&&c[v];if(P&&P.event===x&&P.schema===A&&P.table===k&&P.filter===T)m.push(Object.assign(Object.assign({},y),{id:P.id}));else{this.unsubscribe(),t==null||t(_t.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=m,t&&t(_t.SUBSCRIBED);return}}).receive("error",c=>{t==null||t(_t.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(c).join(", ")||"error")))}).receive("timeout",()=>{t==null||t(_t.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(t,r={}){return await this.send({type:"presence",event:"track",payload:t},r.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,r,s){return this._on(t,r,s)}async send(t,r={}){var s,n;if(!this._canPush()&&t.type==="broadcast"){const{event:i,payload:o}=t,l={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:o,private:this.private}]})};try{const u=await this._fetchWithTimeout(this.broadcastEndpointURL,l,(s=r.timeout)!==null&&s!==void 0?s:this.timeout);return await((n=u.body)===null||n===void 0?void 0:n.cancel()),u.ok?"ok":"error"}catch(u){return u.name==="AbortError"?"timed out":"error"}}else return new Promise(i=>{var o,a,l;const u=this._push(t.type,t,r.timeout||this.timeout);t.type==="broadcast"&&!(!((l=(a=(o=this.params)===null||o===void 0?void 0:o.config)===null||a===void 0?void 0:a.broadcast)===null||l===void 0)&&l.ack)&&i("ok"),u.receive("ok",()=>i("ok")),u.receive("error",()=>i("error")),u.receive("timeout",()=>i("timed out"))})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=Be.leaving;const r=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(Xe.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(s=>{const n=new bn(this,Xe.leave,{},t);n.receive("ok",()=>{r(),s("ok")}).receive("timeout",()=>{r(),s("timed out")}).receive("error",()=>{s("error")}),n.send(),this._canPush()||n.trigger("ok",{})})}async _fetchWithTimeout(t,r,s){const n=new AbortController,i=setTimeout(()=>n.abort(),s),o=await this.socket.fetch(t,Object.assign(Object.assign({},r),{signal:n.signal}));return clearTimeout(i),o}_push(t,r,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let n=new bn(this,t,r,s);return this._canPush()?n.send():(n.startTimeout(),this.pushBuffer.push(n)),n}_onMessage(t,r,s){return r}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,r,s){var n,i;const o=t.toLocaleLowerCase(),{close:a,error:l,leave:u,join:c}=Xe;if(s&&[a,l,u,c].indexOf(o)>=0&&s!==this._joinRef())return;let d=this._onMessage(o,r,s);if(r&&!d)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(n=this.bindings.postgres_changes)===null||n===void 0||n.filter(h=>{var m,v,y;return((m=h.filter)===null||m===void 0?void 0:m.event)==="*"||((y=(v=h.filter)===null||v===void 0?void 0:v.event)===null||y===void 0?void 0:y.toLocaleLowerCase())===o}).map(h=>h.callback(d,s)):(i=this.bindings[o])===null||i===void 0||i.filter(h=>{var m,v,y,x,A,k;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in h){const T=h.id,P=(m=h.filter)===null||m===void 0?void 0:m.event;return T&&((v=r.ids)===null||v===void 0?void 0:v.includes(T))&&(P==="*"||(P==null?void 0:P.toLocaleLowerCase())===((y=r.data)===null||y===void 0?void 0:y.type.toLocaleLowerCase()))}else{const T=(A=(x=h==null?void 0:h.filter)===null||x===void 0?void 0:x.event)===null||A===void 0?void 0:A.toLocaleLowerCase();return T==="*"||T===((k=r==null?void 0:r.event)===null||k===void 0?void 0:k.toLocaleLowerCase())}else return h.type.toLocaleLowerCase()===o}).map(h=>{if(typeof d=="object"&&"ids"in d){const m=d.data,{schema:v,table:y,commit_timestamp:x,type:A,errors:k}=m;d=Object.assign(Object.assign({},{schema:v,table:y,commit_timestamp:x,eventType:A,new:{},old:{},errors:k}),this._getPayloadRecords(m))}h.callback(d,s)})}_isClosed(){return this.state===Be.closed}_isJoined(){return this.state===Be.joined}_isJoining(){return this.state===Be.joining}_isLeaving(){return this.state===Be.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,r,s){const n=t.toLocaleLowerCase(),i={type:n,filter:r,callback:s};return this.bindings[n]?this.bindings[n].push(i):this.bindings[n]=[i],this}_off(t,r){const s=t.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter(n=>{var i;return!(((i=n.type)===null||i===void 0?void 0:i.toLocaleLowerCase())===s&&ki.isEqual(n.filter,r))}),this}static isEqual(t,r){if(Object.keys(t).length!==Object.keys(r).length)return!1;for(const s in t)if(t[s]!==r[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(Xe.close,{},t)}_onError(t){this._on(Xe.error,{},r=>t(r))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=Be.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const r={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(r.new=$o(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(r.old=$o(t.columns,t.old_record)),r}}const Od=()=>{},Rd=typeof WebSocket<"u",$d=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class jd{constructor(t,r){var s;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=wd,this.params={},this.timeout=Il,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=Od,this.conn=null,this.sendBuffer=[],this.serializer=new xd,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=i=>{let o;return i?o=i:typeof fetch>"u"?o=(...a)=>Te(async()=>{const{default:l}=await Promise.resolve().then(()=>$r);return{default:l}},void 0).then(({default:l})=>l(...a)):o=fetch,(...a)=>o(...a)},this.endPoint=`${t}/${zn.websocket}`,this.httpEndpoint=Ml(t),r!=null&&r.transport?this.transport=r.transport:this.transport=null,r!=null&&r.params&&(this.params=r.params),r!=null&&r.headers&&(this.headers=Object.assign(Object.assign({},this.headers),r.headers)),r!=null&&r.timeout&&(this.timeout=r.timeout),r!=null&&r.logger&&(this.logger=r.logger),r!=null&&r.heartbeatIntervalMs&&(this.heartbeatIntervalMs=r.heartbeatIntervalMs);const n=(s=r==null?void 0:r.params)===null||s===void 0?void 0:s.apikey;if(n&&(this.accessTokenValue=n,this.apiKey=n),this.reconnectAfterMs=r!=null&&r.reconnectAfterMs?r.reconnectAfterMs:i=>[1e3,2e3,5e3,1e4][i-1]||1e4,this.encode=r!=null&&r.encode?r.encode:(i,o)=>o(JSON.stringify(i)),this.decode=r!=null&&r.decode?r.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Ll(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(r==null?void 0:r.fetch),r!=null&&r.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(r==null?void 0:r.worker)||!1,this.workerUrl=r==null?void 0:r.workerUrl}this.accessToken=(r==null?void 0:r.accessToken)||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if(Rd){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new Id(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),Te(async()=>{const{default:t}=await import("./browser-BquUDovq.js").then(r=>r.b);return{default:t}},[]).then(({default:t})=>{this.conn=new t(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:bd}))}disconnect(t,r){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,r??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(t){const r=await t.unsubscribe();return this.channels.length===0&&this.disconnect(),r}async removeAllChannels(){const t=await Promise.all(this.channels.map(r=>r.unsubscribe()));return this.disconnect(),t}log(t,r,s){this.logger(t,r,s)}connectionState(){switch(this.conn&&this.conn.readyState){case Er.connecting:return Yt.Connecting;case Er.open:return Yt.Open;case Er.closing:return Yt.Closing;default:return Yt.Closed}}isConnected(){return this.connectionState()===Yt.Open}channel(t,r={config:{}}){const s=new ki(`realtime:${t}`,r,this);return this.channels.push(s),s}push(t){const{topic:r,event:s,payload:n,ref:i}=t,o=()=>{this.encode(t,a=>{var l;(l=this.conn)===null||l===void 0||l.send(a)})};this.log("push",`${r} ${s} (${i})`,n),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(t=null){let r=t||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(r){let s=null;try{s=JSON.parse(atob(r.split(".")[1]))}catch{}if(s&&s.exp&&!(Math.floor(Date.now()/1e3)-s.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${s.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${s.exp}`);this.accessTokenValue=r,this.channels.forEach(n=>{r&&n.updateJoinPayload({access_token:r}),n.joinedOnce&&n._isJoined()&&n._push(Xe.access_token,{access_token:r})})}}async sendHeartbeat(){var t;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),(t=this.conn)===null||t===void 0||t.close(Sd,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let r=this.channels.find(s=>s.topic===t&&(s._isJoined()||s._isJoining()));r&&(this.log("transport",`leaving duplicate topic "${t}"`),r.unsubscribe())}_remove(t){this.channels=this.channels.filter(r=>r._joinRef()!==t._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,r=>{let{topic:s,event:n,payload:i,ref:o}=r;o&&o===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${s} ${n} ${o&&"("+o+")"||""}`,i),this.channels.filter(a=>a._isMember(s)).forEach(a=>a._trigger(n,i,o)),this.stateChangeCallbacks.message.forEach(a=>a(r))})}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),!this.worker)this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);else{this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=r=>{this.log("worker","worker error",r.message),this.workerRef.terminate()},this.workerRef.onmessage=r=>{r.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}this.stateChangeCallbacks.open.forEach(t=>t())}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(r=>r(t))}_onConnError(t){this.log("transport",t.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(r=>r(t))}_triggerChanError(){this.channels.forEach(t=>t._trigger(Xe.error))}_appendParams(t,r){if(Object.keys(r).length===0)return t;const s=t.match(/\?/)?"&":"?",n=new URLSearchParams(r);return`${t}${s}${n}`}_workerObjectUrl(t){let r;if(t)r=t;else{const s=new Blob([$d],{type:"application/javascript"});r=URL.createObjectURL(s)}return r}}class Id{constructor(t,r,s){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=Er.connecting,this.send=()=>{},this.url=null,this.url=t,this.close=s.close}}class Ti extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function Se(e){return typeof e=="object"&&e!==null&&"__isStorageError"in e}class Ld extends Ti{constructor(t,r){super(t),this.name="StorageApiError",this.status=r}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Gn extends Ti{constructor(t,r){super(t),this.name="StorageUnknownError",this.originalError=r}}var Dd=function(e,t,r,s){function n(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(c){try{u(s.next(c))}catch(f){o(f)}}function l(c){try{u(s.throw(c))}catch(f){o(f)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((s=s.apply(e,t||[])).next())})};const Ul=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...r)=>Te(async()=>{const{default:s}=await Promise.resolve().then(()=>$r);return{default:s}},void 0).then(({default:s})=>s(...r)):t=fetch,(...r)=>t(...r)},Md=()=>Dd(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield Te(()=>Promise.resolve().then(()=>$r),void 0)).Response:Response}),Jn=e=>{if(Array.isArray(e))return e.map(r=>Jn(r));if(typeof e=="function"||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([r,s])=>{const n=r.replace(/([-_][a-z])/gi,i=>i.toUpperCase().replace(/[-_]/g,""));t[n]=Jn(s)}),t};var rr=function(e,t,r,s){function n(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(c){try{u(s.next(c))}catch(f){o(f)}}function l(c){try{u(s.throw(c))}catch(f){o(f)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((s=s.apply(e,t||[])).next())})};const Sn=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Ud=(e,t,r)=>rr(void 0,void 0,void 0,function*(){const s=yield Md();e instanceof s&&!(r!=null&&r.noResolveJson)?e.json().then(n=>{t(new Ld(Sn(n),e.status||500))}).catch(n=>{t(new Gn(Sn(n),n))}):t(new Gn(Sn(e),e))}),Nd=(e,t,r,s)=>{const n={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?n:(n.headers=Object.assign({"Content-Type":"application/json"},t==null?void 0:t.headers),s&&(n.body=JSON.stringify(s)),Object.assign(Object.assign({},n),r))};function ms(e,t,r,s,n,i){return rr(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(r,Nd(t,s,n,i)).then(l=>{if(!l.ok)throw l;return s!=null&&s.noResolveJson?l:l.json()}).then(l=>o(l)).catch(l=>Ud(l,a,s))})})}function Ks(e,t,r,s){return rr(this,void 0,void 0,function*(){return ms(e,"GET",t,r,s)})}function Lt(e,t,r,s,n){return rr(this,void 0,void 0,function*(){return ms(e,"POST",t,s,n,r)})}function Fd(e,t,r,s,n){return rr(this,void 0,void 0,function*(){return ms(e,"PUT",t,s,n,r)})}function Bd(e,t,r,s){return rr(this,void 0,void 0,function*(){return ms(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),s)})}function Nl(e,t,r,s,n){return rr(this,void 0,void 0,function*(){return ms(e,"DELETE",t,s,n,r)})}var Le=function(e,t,r,s){function n(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(c){try{u(s.next(c))}catch(f){o(f)}}function l(c){try{u(s.throw(c))}catch(f){o(f)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((s=s.apply(e,t||[])).next())})};const qd={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Do={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Hd{constructor(t,r={},s,n){this.url=t,this.headers=r,this.bucketId=s,this.fetch=Ul(n)}uploadOrUpdate(t,r,s,n){return Le(this,void 0,void 0,function*(){try{let i;const o=Object.assign(Object.assign({},Do),n);let a=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(o.upsert)});const l=o.metadata;typeof Blob<"u"&&s instanceof Blob?(i=new FormData,i.append("cacheControl",o.cacheControl),l&&i.append("metadata",this.encodeMetadata(l)),i.append("",s)):typeof FormData<"u"&&s instanceof FormData?(i=s,i.append("cacheControl",o.cacheControl),l&&i.append("metadata",this.encodeMetadata(l))):(i=s,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType,l&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),n!=null&&n.headers&&(a=Object.assign(Object.assign({},a),n.headers));const u=this._removeEmptyFolders(r),c=this._getFinalPath(u),f=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:t,body:i,headers:a},o!=null&&o.duplex?{duplex:o.duplex}:{})),d=yield f.json();return f.ok?{data:{path:u,id:d.Id,fullPath:d.Key},error:null}:{data:null,error:d}}catch(i){if(Se(i))return{data:null,error:i};throw i}})}upload(t,r,s){return Le(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,r,s)})}uploadToSignedUrl(t,r,s,n){return Le(this,void 0,void 0,function*(){const i=this._removeEmptyFolders(t),o=this._getFinalPath(i),a=new URL(this.url+`/object/upload/sign/${o}`);a.searchParams.set("token",r);try{let l;const u=Object.assign({upsert:Do.upsert},n),c=Object.assign(Object.assign({},this.headers),{"x-upsert":String(u.upsert)});typeof Blob<"u"&&s instanceof Blob?(l=new FormData,l.append("cacheControl",u.cacheControl),l.append("",s)):typeof FormData<"u"&&s instanceof FormData?(l=s,l.append("cacheControl",u.cacheControl)):(l=s,c["cache-control"]=`max-age=${u.cacheControl}`,c["content-type"]=u.contentType);const f=yield this.fetch(a.toString(),{method:"PUT",body:l,headers:c}),d=yield f.json();return f.ok?{data:{path:i,fullPath:d.Key},error:null}:{data:null,error:d}}catch(l){if(Se(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(t,r){return Le(this,void 0,void 0,function*(){try{let s=this._getFinalPath(t);const n=Object.assign({},this.headers);r!=null&&r.upsert&&(n["x-upsert"]="true");const i=yield Lt(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:n}),o=new URL(this.url+i.url),a=o.searchParams.get("token");if(!a)throw new Ti("No token returned by API");return{data:{signedUrl:o.toString(),path:t,token:a},error:null}}catch(s){if(Se(s))return{data:null,error:s};throw s}})}update(t,r,s){return Le(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,r,s)})}move(t,r,s){return Le(this,void 0,void 0,function*(){try{return{data:yield Lt(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:r,destinationBucket:s==null?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(n){if(Se(n))return{data:null,error:n};throw n}})}copy(t,r,s){return Le(this,void 0,void 0,function*(){try{return{data:{path:(yield Lt(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:r,destinationBucket:s==null?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(n){if(Se(n))return{data:null,error:n};throw n}})}createSignedUrl(t,r,s){return Le(this,void 0,void 0,function*(){try{let n=this._getFinalPath(t),i=yield Lt(this.fetch,`${this.url}/object/sign/${n}`,Object.assign({expiresIn:r},s!=null&&s.transform?{transform:s.transform}:{}),{headers:this.headers});const o=s!=null&&s.download?`&download=${s.download===!0?"":s.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${o}`)},{data:i,error:null}}catch(n){if(Se(n))return{data:null,error:n};throw n}})}createSignedUrls(t,r,s){return Le(this,void 0,void 0,function*(){try{const n=yield Lt(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:r,paths:t},{headers:this.headers}),i=s!=null&&s.download?`&download=${s.download===!0?"":s.download}`:"";return{data:n.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${i}`):null})),error:null}}catch(n){if(Se(n))return{data:null,error:n};throw n}})}download(t,r){return Le(this,void 0,void 0,function*(){const n=typeof(r==null?void 0:r.transform)<"u"?"render/image/authenticated":"object",i=this.transformOptsToQueryString((r==null?void 0:r.transform)||{}),o=i?`?${i}`:"";try{const a=this._getFinalPath(t);return{data:yield(yield Ks(this.fetch,`${this.url}/${n}/${a}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(Se(a))return{data:null,error:a};throw a}})}info(t){return Le(this,void 0,void 0,function*(){const r=this._getFinalPath(t);try{const s=yield Ks(this.fetch,`${this.url}/object/info/${r}`,{headers:this.headers});return{data:Jn(s),error:null}}catch(s){if(Se(s))return{data:null,error:s};throw s}})}exists(t){return Le(this,void 0,void 0,function*(){const r=this._getFinalPath(t);try{return yield Bd(this.fetch,`${this.url}/object/${r}`,{headers:this.headers}),{data:!0,error:null}}catch(s){if(Se(s)&&s instanceof Gn){const n=s.originalError;if([400,404].includes(n==null?void 0:n.status))return{data:!1,error:s}}throw s}})}getPublicUrl(t,r){const s=this._getFinalPath(t),n=[],i=r!=null&&r.download?`download=${r.download===!0?"":r.download}`:"";i!==""&&n.push(i);const a=typeof(r==null?void 0:r.transform)<"u"?"render/image":"object",l=this.transformOptsToQueryString((r==null?void 0:r.transform)||{});l!==""&&n.push(l);let u=n.join("&");return u!==""&&(u=`?${u}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${s}${u}`)}}}remove(t){return Le(this,void 0,void 0,function*(){try{return{data:yield Nl(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(r){if(Se(r))return{data:null,error:r};throw r}})}list(t,r,s){return Le(this,void 0,void 0,function*(){try{const n=Object.assign(Object.assign(Object.assign({},qd),r),{prefix:t||""});return{data:yield Lt(this.fetch,`${this.url}/object/list/${this.bucketId}`,n,{headers:this.headers},s),error:null}}catch(n){if(Se(n))return{data:null,error:n};throw n}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const r=[];return t.width&&r.push(`width=${t.width}`),t.height&&r.push(`height=${t.height}`),t.resize&&r.push(`resize=${t.resize}`),t.format&&r.push(`format=${t.format}`),t.quality&&r.push(`quality=${t.quality}`),r.join("&")}}const Vd="2.7.1",Kd={"X-Client-Info":`storage-js/${Vd}`};var hr=function(e,t,r,s){function n(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(c){try{u(s.next(c))}catch(f){o(f)}}function l(c){try{u(s.throw(c))}catch(f){o(f)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((s=s.apply(e,t||[])).next())})};class zd{constructor(t,r={},s){this.url=t,this.headers=Object.assign(Object.assign({},Kd),r),this.fetch=Ul(s)}listBuckets(){return hr(this,void 0,void 0,function*(){try{return{data:yield Ks(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(Se(t))return{data:null,error:t};throw t}})}getBucket(t){return hr(this,void 0,void 0,function*(){try{return{data:yield Ks(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(r){if(Se(r))return{data:null,error:r};throw r}})}createBucket(t,r={public:!1}){return hr(this,void 0,void 0,function*(){try{return{data:yield Lt(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:r.public,file_size_limit:r.fileSizeLimit,allowed_mime_types:r.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(Se(s))return{data:null,error:s};throw s}})}updateBucket(t,r){return hr(this,void 0,void 0,function*(){try{return{data:yield Fd(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:r.public,file_size_limit:r.fileSizeLimit,allowed_mime_types:r.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(Se(s))return{data:null,error:s};throw s}})}emptyBucket(t){return hr(this,void 0,void 0,function*(){try{return{data:yield Lt(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(r){if(Se(r))return{data:null,error:r};throw r}})}deleteBucket(t){return hr(this,void 0,void 0,function*(){try{return{data:yield Nl(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(r){if(Se(r))return{data:null,error:r};throw r}})}}class Wd extends zd{constructor(t,r={},s){super(t,r,s)}from(t){return new Hd(this.url,this.headers,t,this.fetch)}}const Gd="2.49.8";let Br="";typeof Deno<"u"?Br="deno":typeof document<"u"?Br="web":typeof navigator<"u"&&navigator.product==="ReactNative"?Br="react-native":Br="node";const Jd={"X-Client-Info":`supabase-js-${Br}/${Gd}`},Qd={headers:Jd},Yd={schema:"public"},Xd={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},Zd={};var eh=function(e,t,r,s){function n(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(c){try{u(s.next(c))}catch(f){o(f)}}function l(c){try{u(s.throw(c))}catch(f){o(f)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((s=s.apply(e,t||[])).next())})};const th=e=>{let t;return e?t=e:typeof fetch>"u"?t=Al:t=fetch,(...r)=>t(...r)},rh=()=>typeof Headers>"u"?Pl:Headers,sh=(e,t,r)=>{const s=th(r),n=rh();return(i,o)=>eh(void 0,void 0,void 0,function*(){var a;const l=(a=yield t())!==null&&a!==void 0?a:e;let u=new n(o==null?void 0:o.headers);return u.has("apikey")||u.set("apikey",e),u.has("Authorization")||u.set("Authorization",`Bearer ${l}`),s(i,Object.assign(Object.assign({},o),{headers:u}))})};var nh=function(e,t,r,s){function n(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(c){try{u(s.next(c))}catch(f){o(f)}}function l(c){try{u(s.throw(c))}catch(f){o(f)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((s=s.apply(e,t||[])).next())})};function ih(e){return e.endsWith("/")?e:e+"/"}function oh(e,t){var r,s;const{db:n,auth:i,realtime:o,global:a}=e,{db:l,auth:u,realtime:c,global:f}=t,d={db:Object.assign(Object.assign({},l),n),auth:Object.assign(Object.assign({},u),i),realtime:Object.assign(Object.assign({},c),o),global:Object.assign(Object.assign(Object.assign({},f),a),{headers:Object.assign(Object.assign({},(r=f==null?void 0:f.headers)!==null&&r!==void 0?r:{}),(s=a==null?void 0:a.headers)!==null&&s!==void 0?s:{})}),accessToken:()=>nh(this,void 0,void 0,function*(){return""})};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}const Fl="2.69.1",_r=30*1e3,Qn=3,xn=Qn*_r,ah="http://localhost:9999",lh="supabase.auth.token",ch={"X-Client-Info":`gotrue-js/${Fl}`},Yn="X-Supabase-Api-Version",Bl={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},uh=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,fh=6e5;class Ci extends Error{constructor(t,r,s){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=r,this.code=s}}function Q(e){return typeof e=="object"&&e!==null&&"__isAuthError"in e}class dh extends Ci{constructor(t,r,s){super(t,r,s),this.name="AuthApiError",this.status=r,this.code=s}}function hh(e){return Q(e)&&e.name==="AuthApiError"}class ql extends Ci{constructor(t,r){super(t),this.name="AuthUnknownError",this.originalError=r}}class Ht extends Ci{constructor(t,r,s,n){super(t,s,n),this.name=r,this.status=s}}class Rt extends Ht{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function ph(e){return Q(e)&&e.name==="AuthSessionMissingError"}class En extends Ht{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Es extends Ht{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class ks extends Ht{constructor(t,r=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=r}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function gh(e){return Q(e)&&e.name==="AuthImplicitGrantRedirectError"}class Mo extends Ht{constructor(t,r=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=r}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Xn extends Ht{constructor(t,r){super(t,"AuthRetryableFetchError",r,void 0)}}function kn(e){return Q(e)&&e.name==="AuthRetryableFetchError"}class Uo extends Ht{constructor(t,r,s){super(t,"AuthWeakPasswordError",r,"weak_password"),this.reasons=s}}class Yr extends Ht{constructor(t){super(t,"AuthInvalidJwtError",400,"invalid_jwt")}}const No="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Fo=` 	
\r=`.split(""),mh=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<Fo.length;t+=1)e[Fo[t].charCodeAt(0)]=-2;for(let t=0;t<No.length;t+=1)e[No[t].charCodeAt(0)]=t;return e})();function Hl(e,t,r){const s=mh[e];if(s>-1)for(t.queue=t.queue<<6|s,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else{if(s===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}}function Bo(e){const t=[],r=o=>{t.push(String.fromCodePoint(o))},s={utf8seq:0,codepoint:0},n={queue:0,queuedBits:0},i=o=>{yh(o,s,r)};for(let o=0;o<e.length;o+=1)Hl(e.charCodeAt(o),n,i);return t.join("")}function vh(e,t){if(e<=127){t(e);return}else if(e<=2047){t(192|e>>6),t(128|e&63);return}else if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|e&63);return}else if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|e&63);return}throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function _h(e,t){for(let r=0;r<e.length;r+=1){let s=e.charCodeAt(r);if(s>55295&&s<=56319){const n=(s-55296)*1024&65535;s=(e.charCodeAt(r+1)-56320&65535|n)+65536,r+=1}vh(s,t)}}function yh(e,t,r){if(t.utf8seq===0){if(e<=127){r(e);return}for(let s=1;s<6;s+=1)if((e>>7-s&1)===0){t.utf8seq=s;break}if(t.utf8seq===2)t.codepoint=e&31;else if(t.utf8seq===3)t.codepoint=e&15;else if(t.utf8seq===4)t.codepoint=e&7;else throw new Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|e&63,t.utf8seq-=1,t.utf8seq===0&&r(t.codepoint)}}function wh(e){const t=[],r={queue:0,queuedBits:0},s=n=>{t.push(n)};for(let n=0;n<e.length;n+=1)Hl(e.charCodeAt(n),r,s);return new Uint8Array(t)}function bh(e){const t=[];return _h(e,r=>t.push(r)),new Uint8Array(t)}function Sh(e){return Math.round(Date.now()/1e3)+e}function xh(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e=="x"?t:t&3|8).toString(16)})}const lt=()=>typeof window<"u"&&typeof document<"u",Wt={tested:!1,writable:!1},Xr=()=>{if(!lt())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(Wt.tested)return Wt.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),Wt.tested=!0,Wt.writable=!0}catch{Wt.tested=!0,Wt.writable=!1}return Wt.writable};function Eh(e){const t={},r=new URL(e);if(r.hash&&r.hash[0]==="#")try{new URLSearchParams(r.hash.substring(1)).forEach((n,i)=>{t[i]=n})}catch{}return r.searchParams.forEach((s,n)=>{t[n]=s}),t}const Vl=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...r)=>Te(async()=>{const{default:s}=await Promise.resolve().then(()=>$r);return{default:s}},void 0).then(({default:s})=>s(...r)):t=fetch,(...r)=>t(...r)},kh=e=>typeof e=="object"&&e!==null&&"status"in e&&"ok"in e&&"json"in e&&typeof e.json=="function",Kl=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},Ts=async(e,t)=>{const r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch{return r}},Cs=async(e,t)=>{await e.removeItem(t)};class on{constructor(){this.promise=new on.promiseConstructor((t,r)=>{this.resolve=t,this.reject=r})}}on.promiseConstructor=Promise;function Tn(e){const t=e.split(".");if(t.length!==3)throw new Yr("Invalid JWT structure");for(let s=0;s<t.length;s++)if(!uh.test(t[s]))throw new Yr("JWT not in base64url format");return{header:JSON.parse(Bo(t[0])),payload:JSON.parse(Bo(t[1])),signature:wh(t[2]),raw:{header:t[0],payload:t[1]}}}async function Th(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function Ch(e,t){return new Promise((s,n)=>{(async()=>{for(let i=0;i<1/0;i++)try{const o=await e(i);if(!t(i,null,o)){s(o);return}}catch(o){if(!t(i,o)){n(o);return}}})()})}function Ah(e){return("0"+e.toString(16)).substr(-2)}function Ph(){const t=new Uint32Array(56);if(typeof crypto>"u"){const r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",s=r.length;let n="";for(let i=0;i<56;i++)n+=r.charAt(Math.floor(Math.random()*s));return n}return crypto.getRandomValues(t),Array.from(t,Ah).join("")}async function Oh(e){const r=new TextEncoder().encode(e),s=await crypto.subtle.digest("SHA-256",r),n=new Uint8Array(s);return Array.from(n).map(i=>String.fromCharCode(i)).join("")}async function Rh(e){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const r=await Oh(e);return btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function pr(e,t,r=!1){const s=Ph();let n=s;r&&(n+="/PASSWORD_RECOVERY"),await Kl(e,`${t}-code-verifier`,n);const i=await Rh(s);return[i,s===i?"plain":"s256"]}const $h=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function jh(e){const t=e.headers.get(Yn);if(!t||!t.match($h))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch{return null}}function Ih(e){if(!e)throw new Error("Missing exp claim");const t=Math.floor(Date.now()/1e3);if(e<=t)throw new Error("JWT has expired")}function Lh(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}var Dh=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,s=Object.getOwnPropertySymbols(e);n<s.length;n++)t.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]]);return r};const Qt=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Mh=[502,503,504];async function qo(e){var t;if(!kh(e))throw new Xn(Qt(e),0);if(Mh.includes(e.status))throw new Xn(Qt(e),e.status);let r;try{r=await e.json()}catch(i){throw new ql(Qt(i),i)}let s;const n=jh(e);if(n&&n.getTime()>=Bl["2024-01-01"].timestamp&&typeof r=="object"&&r&&typeof r.code=="string"?s=r.code:typeof r=="object"&&r&&typeof r.error_code=="string"&&(s=r.error_code),s){if(s==="weak_password")throw new Uo(Qt(r),e.status,((t=r.weak_password)===null||t===void 0?void 0:t.reasons)||[]);if(s==="session_not_found")throw new Rt}else if(typeof r=="object"&&r&&typeof r.weak_password=="object"&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((i,o)=>i&&typeof o=="string",!0))throw new Uo(Qt(r),e.status,r.weak_password.reasons);throw new dh(Qt(r),e.status||500,s)}const Uh=(e,t,r,s)=>{const n={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?n:(n.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t==null?void 0:t.headers),n.body=JSON.stringify(s),Object.assign(Object.assign({},n),r))};async function Y(e,t,r,s){var n;const i=Object.assign({},s==null?void 0:s.headers);i[Yn]||(i[Yn]=Bl["2024-01-01"].name),s!=null&&s.jwt&&(i.Authorization=`Bearer ${s.jwt}`);const o=(n=s==null?void 0:s.query)!==null&&n!==void 0?n:{};s!=null&&s.redirectTo&&(o.redirect_to=s.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=await Nh(e,t,r+a,{headers:i,noResolveJson:s==null?void 0:s.noResolveJson},{},s==null?void 0:s.body);return s!=null&&s.xform?s==null?void 0:s.xform(l):{data:Object.assign({},l),error:null}}async function Nh(e,t,r,s,n,i){const o=Uh(t,s,n,i);let a;try{a=await e(r,Object.assign({},o))}catch(l){throw console.error(l),new Xn(Qt(l),0)}if(a.ok||await qo(a),s!=null&&s.noResolveJson)return a;try{return await a.json()}catch(l){await qo(l)}}function $t(e){var t;let r=null;Hh(e)&&(r=Object.assign({},e),e.expires_at||(r.expires_at=Sh(e.expires_in)));const s=(t=e.user)!==null&&t!==void 0?t:e;return{data:{session:r,user:s},error:null}}function Ho(e){const t=$t(e);return!t.error&&e.weak_password&&typeof e.weak_password=="object"&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&typeof e.weak_password.message=="string"&&e.weak_password.reasons.reduce((r,s)=>r&&typeof s=="string",!0)&&(t.data.weak_password=e.weak_password),t}function Mt(e){var t;return{data:{user:(t=e.user)!==null&&t!==void 0?t:e},error:null}}function Fh(e){return{data:e,error:null}}function Bh(e){const{action_link:t,email_otp:r,hashed_token:s,redirect_to:n,verification_type:i}=e,o=Dh(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:t,email_otp:r,hashed_token:s,redirect_to:n,verification_type:i},l=Object.assign({},o);return{data:{properties:a,user:l},error:null}}function qh(e){return e}function Hh(e){return e.access_token&&e.refresh_token&&e.expires_in}var Vh=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,s=Object.getOwnPropertySymbols(e);n<s.length;n++)t.indexOf(s[n])<0&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]]);return r};class Kh{constructor({url:t="",headers:r={},fetch:s}){this.url=t,this.headers=r,this.fetch=Vl(s),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,r="global"){try{return await Y(this.fetch,"POST",`${this.url}/logout?scope=${r}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(s){if(Q(s))return{data:null,error:s};throw s}}async inviteUserByEmail(t,r={}){try{return await Y(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:r.data},headers:this.headers,redirectTo:r.redirectTo,xform:Mt})}catch(s){if(Q(s))return{data:{user:null},error:s};throw s}}async generateLink(t){try{const{options:r}=t,s=Vh(t,["options"]),n=Object.assign(Object.assign({},s),r);return"newEmail"in s&&(n.new_email=s==null?void 0:s.newEmail,delete n.newEmail),await Y(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:n,headers:this.headers,xform:Bh,redirectTo:r==null?void 0:r.redirectTo})}catch(r){if(Q(r))return{data:{properties:null,user:null},error:r};throw r}}async createUser(t){try{return await Y(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:Mt})}catch(r){if(Q(r))return{data:{user:null},error:r};throw r}}async listUsers(t){var r,s,n,i,o,a,l;try{const u={nextPage:null,lastPage:0,total:0},c=await Y(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(s=(r=t==null?void 0:t.page)===null||r===void 0?void 0:r.toString())!==null&&s!==void 0?s:"",per_page:(i=(n=t==null?void 0:t.perPage)===null||n===void 0?void 0:n.toString())!==null&&i!==void 0?i:""},xform:qh});if(c.error)throw c.error;const f=await c.json(),d=(o=c.headers.get("x-total-count"))!==null&&o!==void 0?o:0,h=(l=(a=c.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&l!==void 0?l:[];return h.length>0&&(h.forEach(m=>{const v=parseInt(m.split(";")[0].split("=")[1].substring(0,1)),y=JSON.parse(m.split(";")[1].split("=")[1]);u[`${y}Page`]=v}),u.total=parseInt(d)),{data:Object.assign(Object.assign({},f),u),error:null}}catch(u){if(Q(u))return{data:{users:[]},error:u};throw u}}async getUserById(t){try{return await Y(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:Mt})}catch(r){if(Q(r))return{data:{user:null},error:r};throw r}}async updateUserById(t,r){try{return await Y(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:r,headers:this.headers,xform:Mt})}catch(s){if(Q(s))return{data:{user:null},error:s};throw s}}async deleteUser(t,r=!1){try{return await Y(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:r},xform:Mt})}catch(s){if(Q(s))return{data:{user:null},error:s};throw s}}async _listFactors(t){try{const{data:r,error:s}=await Y(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:n=>({data:{factors:n},error:null})});return{data:r,error:s}}catch(r){if(Q(r))return{data:null,error:r};throw r}}async _deleteFactor(t){try{return{data:await Y(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(r){if(Q(r))return{data:null,error:r};throw r}}}const zh={getItem:e=>Xr()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{Xr()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{Xr()&&globalThis.localStorage.removeItem(e)}};function Vo(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}function Wh(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const gr={debug:!!(globalThis&&Xr()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class zl extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class Gh extends zl{}async function Jh(e,t,r){gr.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const s=new globalThis.AbortController;return t>0&&setTimeout(()=>{s.abort(),gr.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,t===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:s.signal},async n=>{if(n){gr.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,n.name);try{return await r()}finally{gr.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,n.name)}}else{if(t===0)throw gr.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new Gh(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(gr.debug)try{const i=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(i,null,"  "))}catch(i){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",i)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}}))}Wh();const Qh={url:ah,storageKey:lh,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:ch,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function Ko(e,t,r){return await r()}class ls{constructor(t){var r,s;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=ls.nextInstanceID,ls.nextInstanceID+=1,this.instanceID>0&&lt()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const n=Object.assign(Object.assign({},Qh),t);if(this.logDebugMessages=!!n.debug,typeof n.debug=="function"&&(this.logger=n.debug),this.persistSession=n.persistSession,this.storageKey=n.storageKey,this.autoRefreshToken=n.autoRefreshToken,this.admin=new Kh({url:n.url,headers:n.headers,fetch:n.fetch}),this.url=n.url,this.headers=n.headers,this.fetch=Vl(n.fetch),this.lock=n.lock||Ko,this.detectSessionInUrl=n.detectSessionInUrl,this.flowType=n.flowType,this.hasCustomAuthorizationHeader=n.hasCustomAuthorizationHeader,n.lock?this.lock=n.lock:lt()&&(!((r=globalThis==null?void 0:globalThis.navigator)===null||r===void 0)&&r.locks)?this.lock=Jh:this.lock=Ko,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?n.storage?this.storage=n.storage:Xr()?this.storage=zh:(this.memoryStorage={},this.storage=Vo(this.memoryStorage)):(this.memoryStorage={},this.storage=Vo(this.memoryStorage)),lt()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",i)}(s=this.broadcastChannel)===null||s===void 0||s.addEventListener("message",async i=>{this._debug("received broadcast notification from other tab or client",i),await this._notifyAllSubscribers(i.data.event,i.data.session,!1)})}this.initialize()}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Fl}) ${new Date().toISOString()}`,...t),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var t;try{const r=Eh(window.location.href);let s="none";if(this._isImplicitGrantCallback(r)?s="implicit":await this._isPKCECallback(r)&&(s="pkce"),lt()&&this.detectSessionInUrl&&s!=="none"){const{data:n,error:i}=await this._getSessionFromURL(r,s);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),gh(i)){const l=(t=i.details)===null||t===void 0?void 0:t.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:i}}return await this._removeSession(),{error:i}}const{session:o,redirectType:a}=n;return this._debug("#_initialize()","detected session in URL",o,"redirect type",a),await this._saveSession(o),setTimeout(async()=>{a==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(r){return Q(r)?{error:r}:{error:new ql("Unexpected error during initialization",r)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var r,s,n;try{const i=await Y(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(s=(r=t==null?void 0:t.options)===null||r===void 0?void 0:r.data)!==null&&s!==void 0?s:{},gotrue_meta_security:{captcha_token:(n=t==null?void 0:t.options)===null||n===void 0?void 0:n.captchaToken}},xform:$t}),{data:o,error:a}=i;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(i){if(Q(i))return{data:{user:null,session:null},error:i};throw i}}async signUp(t){var r,s,n;try{let i;if("email"in t){const{email:c,password:f,options:d}=t;let h=null,m=null;this.flowType==="pkce"&&([h,m]=await pr(this.storage,this.storageKey)),i=await Y(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:d==null?void 0:d.emailRedirectTo,body:{email:c,password:f,data:(r=d==null?void 0:d.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:d==null?void 0:d.captchaToken},code_challenge:h,code_challenge_method:m},xform:$t})}else if("phone"in t){const{phone:c,password:f,options:d}=t;i=await Y(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:c,password:f,data:(s=d==null?void 0:d.data)!==null&&s!==void 0?s:{},channel:(n=d==null?void 0:d.channel)!==null&&n!==void 0?n:"sms",gotrue_meta_security:{captcha_token:d==null?void 0:d.captchaToken}},xform:$t})}else throw new Es("You must provide either an email or phone number and a password");const{data:o,error:a}=i;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(i){if(Q(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithPassword(t){try{let r;if("email"in t){const{email:i,password:o,options:a}=t;r=await Y(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:i,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:Ho})}else if("phone"in t){const{phone:i,password:o,options:a}=t;r=await Y(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:i,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:Ho})}else throw new Es("You must provide either an email or phone number and a password");const{data:s,error:n}=r;return n?{data:{user:null,session:null},error:n}:!s||!s.session||!s.user?{data:{user:null,session:null},error:new En}:(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:n})}catch(r){if(Q(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithOAuth(t){var r,s,n,i;return await this._handleProviderSignIn(t.provider,{redirectTo:(r=t.options)===null||r===void 0?void 0:r.redirectTo,scopes:(s=t.options)===null||s===void 0?void 0:s.scopes,queryParams:(n=t.options)===null||n===void 0?void 0:n.queryParams,skipBrowserRedirect:(i=t.options)===null||i===void 0?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(t))}async _exchangeCodeForSession(t){const r=await Ts(this.storage,`${this.storageKey}-code-verifier`),[s,n]=(r??"").split("/");try{const{data:i,error:o}=await Y(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:s},xform:$t});if(await Cs(this.storage,`${this.storageKey}-code-verifier`),o)throw o;return!i||!i.session||!i.user?{data:{user:null,session:null,redirectType:null},error:new En}:(i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:Object.assign(Object.assign({},i),{redirectType:n??null}),error:o})}catch(i){if(Q(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}}async signInWithIdToken(t){try{const{options:r,provider:s,token:n,access_token:i,nonce:o}=t,a=await Y(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:s,id_token:n,access_token:i,nonce:o,gotrue_meta_security:{captcha_token:r==null?void 0:r.captchaToken}},xform:$t}),{data:l,error:u}=a;return u?{data:{user:null,session:null},error:u}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new En}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:u})}catch(r){if(Q(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithOtp(t){var r,s,n,i,o;try{if("email"in t){const{email:a,options:l}=t;let u=null,c=null;this.flowType==="pkce"&&([u,c]=await pr(this.storage,this.storageKey));const{error:f}=await Y(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:a,data:(r=l==null?void 0:l.data)!==null&&r!==void 0?r:{},create_user:(s=l==null?void 0:l.shouldCreateUser)!==null&&s!==void 0?s:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:u,code_challenge_method:c},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:f}}if("phone"in t){const{phone:a,options:l}=t,{data:u,error:c}=await Y(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:a,data:(n=l==null?void 0:l.data)!==null&&n!==void 0?n:{},create_user:(i=l==null?void 0:l.shouldCreateUser)!==null&&i!==void 0?i:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(o=l==null?void 0:l.channel)!==null&&o!==void 0?o:"sms"}});return{data:{user:null,session:null,messageId:u==null?void 0:u.message_id},error:c}}throw new Es("You must provide either an email or phone number.")}catch(a){if(Q(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(t){var r,s;try{let n,i;"options"in t&&(n=(r=t.options)===null||r===void 0?void 0:r.redirectTo,i=(s=t.options)===null||s===void 0?void 0:s.captchaToken);const{data:o,error:a}=await Y(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:i}}),redirectTo:n,xform:$t});if(a)throw a;if(!o)throw new Error("An error occurred on token verification.");const l=o.session,u=o.user;return l!=null&&l.access_token&&(await this._saveSession(l),await this._notifyAllSubscribers(t.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(n){if(Q(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithSSO(t){var r,s,n;try{let i=null,o=null;return this.flowType==="pkce"&&([i,o]=await pr(this.storage,this.storageKey)),await Y(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:(s=(r=t.options)===null||r===void 0?void 0:r.redirectTo)!==null&&s!==void 0?s:void 0}),!((n=t==null?void 0:t.options)===null||n===void 0)&&n.captchaToken?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:o}),headers:this.headers,xform:Fh})}catch(i){if(Q(i))return{data:null,error:i};throw i}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async t=>{const{data:{session:r},error:s}=t;if(s)throw s;if(!r)throw new Rt;const{error:n}=await Y(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:r.access_token});return{data:{user:null,session:null},error:n}})}catch(t){if(Q(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const r=`${this.url}/resend`;if("email"in t){const{email:s,type:n,options:i}=t,{error:o}=await Y(this.fetch,"POST",r,{headers:this.headers,body:{email:s,type:n,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}},redirectTo:i==null?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:o}}else if("phone"in t){const{phone:s,type:n,options:i}=t,{data:o,error:a}=await Y(this.fetch,"POST",r,{headers:this.headers,body:{phone:s,type:n,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:o==null?void 0:o.message_id},error:a}}throw new Es("You must provide either an email or phone number and a type")}catch(r){if(Q(r))return{data:{user:null,session:null},error:r};throw r}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async r=>r))}async _acquireLock(t,r){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const s=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),n=(async()=>(await s,await r()))();return this.pendingInLock.push((async()=>{try{await n}catch{}})()),n}return await this.lock(`lock:${this.storageKey}`,t,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const s=r();for(this.pendingInLock.push((async()=>{try{await s}catch{}})()),await s;this.pendingInLock.length;){const n=[...this.pendingInLock];await Promise.all(n),this.pendingInLock.splice(0,n.length)}return await s}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const r=await this.__loadSession();return await t(r)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let t=null;const r=await Ts(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",r),r!==null&&(this._isValidSession(r)?t=r:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const s=t.expires_at?t.expires_at*1e3-Date.now()<xn:!1;if(this._debug("#__loadSession()",`session has${s?"":" not"} expired`,"expires_at",t.expires_at),!s){if(this.storage.isServer){let o=this.suppressGetSessionWarning;t=new Proxy(t,{get:(l,u,c)=>(!o&&u==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),o=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,u,c))})}return{data:{session:t},error:null}}const{session:n,error:i}=await this._callRefreshToken(t.refresh_token);return i?{data:{session:null},error:i}:{data:{session:n},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){return t?await this._getUser(t):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(t){try{return t?await Y(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:Mt}):await this._useSession(async r=>{var s,n,i;const{data:o,error:a}=r;if(a)throw a;return!(!((s=o.session)===null||s===void 0)&&s.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new Rt}:await Y(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(i=(n=o.session)===null||n===void 0?void 0:n.access_token)!==null&&i!==void 0?i:void 0,xform:Mt})})}catch(r){if(Q(r))return ph(r)&&(await this._removeSession(),await Cs(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:r};throw r}}async updateUser(t,r={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(t,r))}async _updateUser(t,r={}){try{return await this._useSession(async s=>{const{data:n,error:i}=s;if(i)throw i;if(!n.session)throw new Rt;const o=n.session;let a=null,l=null;this.flowType==="pkce"&&t.email!=null&&([a,l]=await pr(this.storage,this.storageKey));const{data:u,error:c}=await Y(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:r==null?void 0:r.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:a,code_challenge_method:l}),jwt:o.access_token,xform:Mt});if(c)throw c;return o.user=u.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(s){if(Q(s))return{data:{user:null},error:s};throw s}}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(t))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new Rt;const r=Date.now()/1e3;let s=r,n=!0,i=null;const{payload:o}=Tn(t.access_token);if(o.exp&&(s=o.exp,n=s<=r),n){const{session:a,error:l}=await this._callRefreshToken(t.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!a)return{data:{user:null,session:null},error:null};i=a}else{const{data:a,error:l}=await this._getUser(t.access_token);if(l)throw l;i={access_token:t.access_token,refresh_token:t.refresh_token,user:a.user,token_type:"bearer",expires_in:s-r,expires_at:s},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(r){if(Q(r))return{data:{session:null,user:null},error:r};throw r}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(t))}async _refreshSession(t){try{return await this._useSession(async r=>{var s;if(!t){const{data:o,error:a}=r;if(a)throw a;t=(s=o.session)!==null&&s!==void 0?s:void 0}if(!(t!=null&&t.refresh_token))throw new Rt;const{session:n,error:i}=await this._callRefreshToken(t.refresh_token);return i?{data:{user:null,session:null},error:i}:n?{data:{user:n.user,session:n},error:null}:{data:{user:null,session:null},error:null}})}catch(r){if(Q(r))return{data:{user:null,session:null},error:r};throw r}}async _getSessionFromURL(t,r){try{if(!lt())throw new ks("No browser detected.");if(t.error||t.error_description||t.error_code)throw new ks(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(r){case"implicit":if(this.flowType==="pkce")throw new Mo("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new ks("Not a valid implicit grant flow url.");break;default:}if(r==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new Mo("No code detected.");const{data:A,error:k}=await this._exchangeCodeForSession(t.code);if(k)throw k;const T=new URL(window.location.href);return T.searchParams.delete("code"),window.history.replaceState(window.history.state,"",T.toString()),{data:{session:A.session,redirectType:null},error:null}}const{provider_token:s,provider_refresh_token:n,access_token:i,refresh_token:o,expires_in:a,expires_at:l,token_type:u}=t;if(!i||!a||!o||!u)throw new ks("No session defined in URL");const c=Math.round(Date.now()/1e3),f=parseInt(a);let d=c+f;l&&(d=parseInt(l));const h=d-c;h*1e3<=_r&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${h}s, should have been closer to ${f}s`);const m=d-f;c-m>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",m,d,c):c-m<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",m,d,c);const{data:v,error:y}=await this._getUser(i);if(y)throw y;const x={provider_token:s,provider_refresh_token:n,access_token:i,expires_in:f,expires_at:d,refresh_token:o,token_type:u,user:v.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:x,redirectType:t.type},error:null}}catch(s){if(Q(s))return{data:{session:null,redirectType:null},error:s};throw s}}_isImplicitGrantCallback(t){return!!(t.access_token||t.error_description)}async _isPKCECallback(t){const r=await Ts(this.storage,`${this.storageKey}-code-verifier`);return!!(t.code&&r)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(t))}async _signOut({scope:t}={scope:"global"}){return await this._useSession(async r=>{var s;const{data:n,error:i}=r;if(i)return{error:i};const o=(s=n.session)===null||s===void 0?void 0:s.access_token;if(o){const{error:a}=await this.admin.signOut(o,t);if(a&&!(hh(a)&&(a.status===404||a.status===401||a.status===403)))return{error:a}}return t!=="others"&&(await this._removeSession(),await Cs(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(t){const r=xh(),s={id:r,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",r),this.stateChangeEmitters.delete(r)}};return this._debug("#onAuthStateChange()","registered callback with id",r),this.stateChangeEmitters.set(r,s),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(r)})))(),{data:{subscription:s}}}async _emitInitialSession(t){return await this._useSession(async r=>{var s,n;try{const{data:{session:i},error:o}=r;if(o)throw o;await((s=this.stateChangeEmitters.get(t))===null||s===void 0?void 0:s.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",t,"session",i)}catch(i){await((n=this.stateChangeEmitters.get(t))===null||n===void 0?void 0:n.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",i),console.error(i)}})}async resetPasswordForEmail(t,r={}){let s=null,n=null;this.flowType==="pkce"&&([s,n]=await pr(this.storage,this.storageKey,!0));try{return await Y(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:s,code_challenge_method:n,gotrue_meta_security:{captcha_token:r.captchaToken}},headers:this.headers,redirectTo:r.redirectTo})}catch(i){if(Q(i))return{data:null,error:i};throw i}}async getUserIdentities(){var t;try{const{data:r,error:s}=await this.getUser();if(s)throw s;return{data:{identities:(t=r.user.identities)!==null&&t!==void 0?t:[]},error:null}}catch(r){if(Q(r))return{data:null,error:r};throw r}}async linkIdentity(t){var r;try{const{data:s,error:n}=await this._useSession(async i=>{var o,a,l,u,c;const{data:f,error:d}=i;if(d)throw d;const h=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:(o=t.options)===null||o===void 0?void 0:o.redirectTo,scopes:(a=t.options)===null||a===void 0?void 0:a.scopes,queryParams:(l=t.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return await Y(this.fetch,"GET",h,{headers:this.headers,jwt:(c=(u=f.session)===null||u===void 0?void 0:u.access_token)!==null&&c!==void 0?c:void 0})});if(n)throw n;return lt()&&!(!((r=t.options)===null||r===void 0)&&r.skipBrowserRedirect)&&window.location.assign(s==null?void 0:s.url),{data:{provider:t.provider,url:s==null?void 0:s.url},error:null}}catch(s){if(Q(s))return{data:{provider:t.provider,url:null},error:s};throw s}}async unlinkIdentity(t){try{return await this._useSession(async r=>{var s,n;const{data:i,error:o}=r;if(o)throw o;return await Y(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:(n=(s=i.session)===null||s===void 0?void 0:s.access_token)!==null&&n!==void 0?n:void 0})})}catch(r){if(Q(r))return{data:null,error:r};throw r}}async _refreshAccessToken(t){const r=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(r,"begin");try{const s=Date.now();return await Ch(async n=>(n>0&&await Th(200*Math.pow(2,n-1)),this._debug(r,"refreshing attempt",n),await Y(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:$t})),(n,i)=>{const o=200*Math.pow(2,n);return i&&kn(i)&&Date.now()+o-s<_r})}catch(s){if(this._debug(r,"error",s),Q(s))return{data:{session:null,user:null},error:s};throw s}finally{this._debug(r,"end")}}_isValidSession(t){return typeof t=="object"&&t!==null&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,r){const s=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:r.redirectTo,scopes:r.scopes,queryParams:r.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",r,"url",s),lt()&&!r.skipBrowserRedirect&&window.location.assign(s),{data:{provider:t,url:s},error:null}}async _recoverAndRefresh(){var t;const r="#_recoverAndRefresh()";this._debug(r,"begin");try{const s=await Ts(this.storage,this.storageKey);if(this._debug(r,"session from storage",s),!this._isValidSession(s)){this._debug(r,"session is not valid"),s!==null&&await this._removeSession();return}const n=((t=s.expires_at)!==null&&t!==void 0?t:1/0)*1e3-Date.now()<xn;if(this._debug(r,`session has${n?"":" not"} expired with margin of ${xn}s`),n){if(this.autoRefreshToken&&s.refresh_token){const{error:i}=await this._callRefreshToken(s.refresh_token);i&&(console.error(i),kn(i)||(this._debug(r,"refresh failed with a non-retryable error, removing the session",i),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(s){this._debug(r,"error",s),console.error(s);return}finally{this._debug(r,"end")}}async _callRefreshToken(t){var r,s;if(!t)throw new Rt;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const n=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(n,"begin");try{this.refreshingDeferred=new on;const{data:i,error:o}=await this._refreshAccessToken(t);if(o)throw o;if(!i.session)throw new Rt;await this._saveSession(i.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);const a={session:i.session,error:null};return this.refreshingDeferred.resolve(a),a}catch(i){if(this._debug(n,"error",i),Q(i)){const o={session:null,error:i};return kn(i)||await this._removeSession(),(r=this.refreshingDeferred)===null||r===void 0||r.resolve(o),o}throw(s=this.refreshingDeferred)===null||s===void 0||s.reject(i),i}finally{this.refreshingDeferred=null,this._debug(n,"end")}}async _notifyAllSubscribers(t,r,s=!0){const n=`#_notifyAllSubscribers(${t})`;this._debug(n,"begin",r,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:t,session:r});const i=[],o=Array.from(this.stateChangeEmitters.values()).map(async a=>{try{await a.callback(t,r)}catch(l){i.push(l)}});if(await Promise.all(o),i.length>0){for(let a=0;a<i.length;a+=1)console.error(i[a]);throw i[0]}}finally{this._debug(n,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,await Kl(this.storage,this.storageKey,t)}async _removeSession(){this._debug("#_removeSession()"),await Cs(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&lt()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",t)}catch(r){console.error("removing visibilitychange callback failed",r)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),_r);this.autoRefreshTicker=t,t&&typeof t=="object"&&typeof t.unref=="function"?t.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(t),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async r=>{const{data:{session:s}}=r;if(!s||!s.refresh_token||!s.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const n=Math.floor((s.expires_at*1e3-t)/_r);this._debug("#_autoRefreshTokenTick()",`access token expires in ${n} ticks, a tick lasts ${_r}ms, refresh threshold is ${Qn} ticks`),n<=Qn&&await this._callRefreshToken(s.refresh_token)})}catch(r){console.error("Auto refresh tick failed with error. This is likely a transient error.",r)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(t){if(t.isAcquireTimeout||t instanceof zl)this._debug("auto refresh token tick lock not available");else throw t}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!lt()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){console.error("_handleVisibilityChange",t)}}async _onVisibilityChanged(t){const r=`#_onVisibilityChanged(${t})`;this._debug(r,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(r,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,r,s){const n=[`provider=${encodeURIComponent(r)}`];if(s!=null&&s.redirectTo&&n.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),s!=null&&s.scopes&&n.push(`scopes=${encodeURIComponent(s.scopes)}`),this.flowType==="pkce"){const[i,o]=await pr(this.storage,this.storageKey),a=new URLSearchParams({code_challenge:`${encodeURIComponent(i)}`,code_challenge_method:`${encodeURIComponent(o)}`});n.push(a.toString())}if(s!=null&&s.queryParams){const i=new URLSearchParams(s.queryParams);n.push(i.toString())}return s!=null&&s.skipBrowserRedirect&&n.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${t}?${n.join("&")}`}async _unenroll(t){try{return await this._useSession(async r=>{var s;const{data:n,error:i}=r;return i?{data:null,error:i}:await Y(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:(s=n==null?void 0:n.session)===null||s===void 0?void 0:s.access_token})})}catch(r){if(Q(r))return{data:null,error:r};throw r}}async _enroll(t){try{return await this._useSession(async r=>{var s,n;const{data:i,error:o}=r;if(o)return{data:null,error:o};const a=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},t.factorType==="phone"?{phone:t.phone}:{issuer:t.issuer}),{data:l,error:u}=await Y(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:(s=i==null?void 0:i.session)===null||s===void 0?void 0:s.access_token});return u?{data:null,error:u}:(t.factorType==="totp"&&(!((n=l==null?void 0:l.totp)===null||n===void 0)&&n.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(r){if(Q(r))return{data:null,error:r};throw r}}async _verify(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async r=>{var s;const{data:n,error:i}=r;if(i)return{data:null,error:i};const{data:o,error:a}=await Y(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:(s=n==null?void 0:n.session)===null||s===void 0?void 0:s.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:a})})}catch(r){if(Q(r))return{data:null,error:r};throw r}})}async _challenge(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async r=>{var s;const{data:n,error:i}=r;return i?{data:null,error:i}:await Y(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:(s=n==null?void 0:n.session)===null||s===void 0?void 0:s.access_token})})}catch(r){if(Q(r))return{data:null,error:r};throw r}})}async _challengeAndVerify(t){const{data:r,error:s}=await this._challenge({factorId:t.factorId});return s?{data:null,error:s}:await this._verify({factorId:t.factorId,challengeId:r.id,code:t.code})}async _listFactors(){const{data:{user:t},error:r}=await this.getUser();if(r)return{data:null,error:r};const s=(t==null?void 0:t.factors)||[],n=s.filter(o=>o.factor_type==="totp"&&o.status==="verified"),i=s.filter(o=>o.factor_type==="phone"&&o.status==="verified");return{data:{all:s,totp:n,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async t=>{var r,s;const{data:{session:n},error:i}=t;if(i)return{data:null,error:i};if(!n)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=Tn(n.access_token);let a=null;o.aal&&(a=o.aal);let l=a;((s=(r=n.user.factors)===null||r===void 0?void 0:r.filter(f=>f.status==="verified"))!==null&&s!==void 0?s:[]).length>0&&(l="aal2");const c=o.amr||[];return{data:{currentLevel:a,nextLevel:l,currentAuthenticationMethods:c},error:null}}))}async fetchJwk(t,r={keys:[]}){let s=r.keys.find(o=>o.kid===t);if(s||(s=this.jwks.keys.find(o=>o.kid===t),s&&this.jwks_cached_at+fh>Date.now()))return s;const{data:n,error:i}=await Y(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;if(!n.keys||n.keys.length===0)throw new Yr("JWKS is empty");if(this.jwks=n,this.jwks_cached_at=Date.now(),s=n.keys.find(o=>o.kid===t),!s)throw new Yr("No matching signing key found in JWKS");return s}async getClaims(t,r={keys:[]}){try{let s=t;if(!s){const{data:h,error:m}=await this.getSession();if(m||!h.session)return{data:null,error:m};s=h.session.access_token}const{header:n,payload:i,signature:o,raw:{header:a,payload:l}}=Tn(s);if(Ih(i.exp),!n.kid||n.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:h}=await this.getUser(s);if(h)throw h;return{data:{claims:i,header:n,signature:o},error:null}}const u=Lh(n.alg),c=await this.fetchJwk(n.kid,r),f=await crypto.subtle.importKey("jwk",c,u,!0,["verify"]);if(!await crypto.subtle.verify(u,f,o,bh(`${a}.${l}`)))throw new Yr("Invalid JWT signature");return{data:{claims:i,header:n,signature:o},error:null}}catch(s){if(Q(s))return{data:null,error:s};throw s}}}ls.nextInstanceID=0;const Yh=ls;class Xh extends Yh{constructor(t){super(t)}}var Zh=function(e,t,r,s){function n(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(c){try{u(s.next(c))}catch(f){o(f)}}function l(c){try{u(s.throw(c))}catch(f){o(f)}}function u(c){c.done?i(c.value):n(c.value).then(a,l)}u((s=s.apply(e,t||[])).next())})};class ep{constructor(t,r,s){var n,i,o;if(this.supabaseUrl=t,this.supabaseKey=r,!t)throw new Error("supabaseUrl is required.");if(!r)throw new Error("supabaseKey is required.");const a=ih(t),l=new URL(a);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const u=`sb-${l.hostname.split(".")[0]}-auth-token`,c={db:Yd,realtime:Zd,auth:Object.assign(Object.assign({},Xd),{storageKey:u}),global:Qd},f=oh(s??{},c);this.storageKey=(n=f.auth.storageKey)!==null&&n!==void 0?n:"",this.headers=(i=f.global.headers)!==null&&i!==void 0?i:{},f.accessToken?(this.accessToken=f.accessToken,this.auth=new Proxy({},{get:(d,h)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(h)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((o=f.auth)!==null&&o!==void 0?o:{},this.headers,f.global.fetch),this.fetch=sh(r,this._getAccessToken.bind(this),f.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},f.realtime)),this.rest=new _d(new URL("rest/v1",l).href,{headers:this.headers,schema:f.db.schema,fetch:this.fetch}),f.accessToken||this._listenForAuthEvents()}get functions(){return new nd(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new Wd(this.storageUrl.href,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,r={},s={}){return this.rest.rpc(t,r,s)}channel(t,r={config:{}}){return this.realtime.channel(t,r)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,r;return Zh(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:s}=yield this.auth.getSession();return(r=(t=s.session)===null||t===void 0?void 0:t.access_token)!==null&&r!==void 0?r:null})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:r,detectSessionInUrl:s,storage:n,storageKey:i,flowType:o,lock:a,debug:l},u,c){const f={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Xh({url:this.authUrl.href,headers:Object.assign(Object.assign({},f),u),storageKey:i,autoRefreshToken:t,persistSession:r,detectSessionInUrl:s,storage:n,flowType:o,lock:a,debug:l,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new jd(this.realtimeUrl.href,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t==null?void 0:t.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((r,s)=>{this._handleTokenChanged(r,"CLIENT",s==null?void 0:s.access_token)})}_handleTokenChanged(t,r,s){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==s?this.changedAccessToken=s:t==="SIGNED_OUT"&&(this.realtime.setAuth(),r=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const tp=(e,t,r)=>new ep(e,t,r),rp="https://kfirwysuqdviaclnwoet.supabase.co",sp="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtmaXJ3eXN1cWR2aWFjbG53b2V0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyOTk5MDQsImV4cCI6MjA2Mzg3NTkwNH0.VlmnRBtVoHHcXrRkF72SzuSpNPFXCsrbRVsAmjJvtlE",ge=tp(rp,sp),an=gs("auth",()=>{const e=me(null),t=me(null),r=me(null),s=me(!0),n=ue(()=>!!e.value),i=async()=>{try{s.value=!0;const{data:{session:h}}=await ge.auth.getSession();h&&(t.value=h,e.value=h.user,await o()),ge.auth.onAuthStateChange(async(m,v)=>{t.value=v,e.value=(v==null?void 0:v.user)??null,m==="SIGNED_IN"&&(v!=null&&v.user)?await o():m==="SIGNED_OUT"&&(r.value=null)})}catch(h){console.error("Error initializing auth:",h)}finally{s.value=!1}},o=async()=>{if(e.value)try{const{data:h,error:m}=await ge.from("profiles").select("*").eq("id",e.value.id).single();if(m)if(m.code==="PGRST116")await a();else throw m;else r.value=h}catch(h){console.error("Error fetching profile:",h)}},a=async()=>{var h;if(e.value)try{const{data:m,error:v}=await ge.from("profiles").insert({id:e.value.id,email:e.value.email,full_name:((h=e.value.user_metadata)==null?void 0:h.full_name)||null}).select().single();if(v)throw v;r.value=m}catch(m){console.error("Error creating profile:",m)}},l=async(h,m,v)=>{try{const{data:y,error:x}=await ge.auth.signUp({email:h,password:m,options:{data:{full_name:v}}});if(x)throw x;return{data:y,error:null}}catch(y){return console.error("Error signing up:",y),{data:null,error:y}}},u=async(h,m)=>{try{const{data:v,error:y}=await ge.auth.signInWithPassword({email:h,password:m});if(y)throw y;return{data:v,error:null}}catch(v){return console.error("Error signing in:",v),{data:null,error:v}}},c=async()=>{try{const{error:h}=await ge.auth.signOut();if(h)throw h;e.value=null,t.value=null,r.value=null}catch(h){console.error("Error signing out:",h)}},f=async h=>{if(e.value)try{const{data:m,error:v}=await ge.from("profiles").update(h).eq("id",e.value.id).select().single();if(v)throw v;return r.value=m,{data:m,error:null}}catch(m){return console.error("Error updating profile:",m),{data:null,error:m}}},d=async h=>{try{const{error:m}=await ge.auth.resetPasswordForEmail(h,{redirectTo:`${window.location.origin}/reset-password`});if(m)throw m;return{error:null}}catch(m){return console.error("Error resetting password:",m),{error:m}}};return{user:ze(e),session:ze(t),profile:ze(r),loading:ze(s),isAuthenticated:n,initialize:i,signUp:l,signIn:u,signOut:c,updateProfile:f,resetPassword:d}}),vs=gs("toast",()=>{const e=me([]),t=(l,u="info",c=3e3)=>{const f=Date.now().toString(),d={id:f,message:l,type:u,duration:c};return e.value.push(d),c>0&&setTimeout(()=>{r(f)},c),f},r=l=>{const u=e.value.findIndex(c=>c.id===l);u>-1&&e.value.splice(u,1)};return{toasts:e,addToast:t,removeToast:r,clearAllToasts:()=>{e.value=[]},success:(l,u)=>t(l,"success",u),error:(l,u)=>t(l,"error",u),warning:(l,u)=>t(l,"warning",u),info:(l,u)=>t(l,"info",u)}}),Wl=gs("cart",()=>{const e=me([]),t=()=>{try{return vs()}catch{return null}},r=ue(()=>e.value.reduce((m,v)=>m+v.quantity,0)),s=ue(()=>e.value.reduce((m,v)=>m+v.product.price*v.quantity,0)),n=ue(()=>e.value.length===0),i=(m,v=1)=>{const y=e.value.find(A=>A.product.id===m.id);y?y.quantity+=v:e.value.push({product:m,quantity:v}),f();const x=t();if(x){const A=y?`Updated ${m.name} quantity in cart`:`Added ${m.name} to cart`;x.success(A)}},o=m=>{const v=e.value.findIndex(y=>y.product.id===m);if(v>-1){const y=e.value[v];e.value.splice(v,1),f();const x=t();x&&x.info(`Removed ${y.product.name} from cart`)}},a=(m,v)=>{const y=e.value.find(x=>x.product.id===m);y&&(v<=0?o(m):(y.quantity=v,f()))},l=()=>{const m=e.value.length;e.value=[],f();const v=t();v&&m>0&&v.info(`Cleared ${m} item${m!==1?"s":""} from cart`)},u=m=>{const v=e.value.find(y=>y.product.id===m);return v?v.quantity:0},c=m=>e.value.some(v=>v.product.id===m),f=()=>{try{localStorage.setItem("cart",JSON.stringify(e.value))}catch(m){console.error("Error saving cart to localStorage:",m)}},d=()=>{try{const m=localStorage.getItem("cart");m&&(e.value=JSON.parse(m))}catch(m){console.error("Error loading cart from localStorage:",m),e.value=[]}},h=()=>{d()};return{items:ue(()=>[...e.value]),itemCount:r,totalAmount:s,isEmpty:n,addItem:i,removeItem:o,updateQuantity:a,clearCart:l,getItemQuantity:u,isInCart:c,initialize:h}});/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const yr=typeof document<"u";function Gl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function np(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Gl(e.default)}const ne=Object.assign;function Cn(e,t){const r={};for(const s in t){const n=t[s];r[s]=tt(n)?n.map(e):e(n)}return r}const Zr=()=>{},tt=Array.isArray,Jl=/#/g,ip=/&/g,op=/\//g,ap=/=/g,lp=/\?/g,Ql=/\+/g,cp=/%5B/g,up=/%5D/g,Yl=/%5E/g,fp=/%60/g,Xl=/%7B/g,dp=/%7C/g,Zl=/%7D/g,hp=/%20/g;function Ai(e){return encodeURI(""+e).replace(dp,"|").replace(cp,"[").replace(up,"]")}function pp(e){return Ai(e).replace(Xl,"{").replace(Zl,"}").replace(Yl,"^")}function Zn(e){return Ai(e).replace(Ql,"%2B").replace(hp,"+").replace(Jl,"%23").replace(ip,"%26").replace(fp,"`").replace(Xl,"{").replace(Zl,"}").replace(Yl,"^")}function gp(e){return Zn(e).replace(ap,"%3D")}function mp(e){return Ai(e).replace(Jl,"%23").replace(lp,"%3F")}function vp(e){return e==null?"":mp(e).replace(op,"%2F")}function cs(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const _p=/\/$/,yp=e=>e.replace(_p,"");function An(e,t,r="/"){let s,n={},i="",o="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(s=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),n=e(i)),a>-1&&(s=s||t.slice(0,a),o=t.slice(a,t.length)),s=xp(s??t,r),{fullPath:s+(i&&"?")+i+o,path:s,query:n,hash:cs(o)}}function wp(e,t){const r=t.query?e(t.query):"";return t.path+(r&&"?")+r+(t.hash||"")}function zo(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function bp(e,t,r){const s=t.matched.length-1,n=r.matched.length-1;return s>-1&&s===n&&Ar(t.matched[s],r.matched[n])&&ec(t.params,r.params)&&e(t.query)===e(r.query)&&t.hash===r.hash}function Ar(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ec(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(!Sp(e[r],t[r]))return!1;return!0}function Sp(e,t){return tt(e)?Wo(e,t):tt(t)?Wo(t,e):e===t}function Wo(e,t){return tt(t)?e.length===t.length&&e.every((r,s)=>r===t[s]):e.length===1&&e[0]===t}function xp(e,t){if(e.startsWith("/"))return e;if(!e)return t;const r=t.split("/"),s=e.split("/"),n=s[s.length-1];(n===".."||n===".")&&s.push("");let i=r.length-1,o,a;for(o=0;o<s.length;o++)if(a=s[o],a!==".")if(a==="..")i>1&&i--;else break;return r.slice(0,i).join("/")+"/"+s.slice(o).join("/")}const At={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var us;(function(e){e.pop="pop",e.push="push"})(us||(us={}));var es;(function(e){e.back="back",e.forward="forward",e.unknown=""})(es||(es={}));function Ep(e){if(!e)if(yr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),yp(e)}const kp=/^[^#]+#/;function Tp(e,t){return e.replace(kp,"#")+t}function Cp(e,t){const r=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-r.left-(t.left||0),top:s.top-r.top-(t.top||0)}}const ln=()=>({left:window.scrollX,top:window.scrollY});function Ap(e){let t;if("el"in e){const r=e.el,s=typeof r=="string"&&r.startsWith("#"),n=typeof r=="string"?s?document.getElementById(r.slice(1)):document.querySelector(r):r;if(!n)return;t=Cp(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Go(e,t){return(history.state?history.state.position-t:-1)+e}const ei=new Map;function Pp(e,t){ei.set(e,t)}function Op(e){const t=ei.get(e);return ei.delete(e),t}let Rp=()=>location.protocol+"//"+location.host;function tc(e,t){const{pathname:r,search:s,hash:n}=t,i=e.indexOf("#");if(i>-1){let a=n.includes(e.slice(i))?e.slice(i).length:1,l=n.slice(a);return l[0]!=="/"&&(l="/"+l),zo(l,"")}return zo(r,e)+s+n}function $p(e,t,r,s){let n=[],i=[],o=null;const a=({state:d})=>{const h=tc(e,location),m=r.value,v=t.value;let y=0;if(d){if(r.value=h,t.value=d,o&&o===m){o=null;return}y=v?d.position-v.position:0}else s(h);n.forEach(x=>{x(r.value,m,{delta:y,type:us.pop,direction:y?y>0?es.forward:es.back:es.unknown})})};function l(){o=r.value}function u(d){n.push(d);const h=()=>{const m=n.indexOf(d);m>-1&&n.splice(m,1)};return i.push(h),h}function c(){const{history:d}=window;d.state&&d.replaceState(ne({},d.state,{scroll:ln()}),"")}function f(){for(const d of i)d();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:l,listen:u,destroy:f}}function Jo(e,t,r,s=!1,n=!1){return{back:e,current:t,forward:r,replaced:s,position:window.history.length,scroll:n?ln():null}}function jp(e){const{history:t,location:r}=window,s={value:tc(e,r)},n={value:t.state};n.value||i(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(l,u,c){const f=e.indexOf("#"),d=f>-1?(r.host&&document.querySelector("base")?e:e.slice(f))+l:Rp()+e+l;try{t[c?"replaceState":"pushState"](u,"",d),n.value=u}catch(h){console.error(h),r[c?"replace":"assign"](d)}}function o(l,u){const c=ne({},t.state,Jo(n.value.back,l,n.value.forward,!0),u,{position:n.value.position});i(l,c,!0),s.value=l}function a(l,u){const c=ne({},n.value,t.state,{forward:l,scroll:ln()});i(c.current,c,!0);const f=ne({},Jo(s.value,l,null),{position:c.position+1},u);i(l,f,!1),s.value=l}return{location:s,state:n,push:a,replace:o}}function Ip(e){e=Ep(e);const t=jp(e),r=$p(e,t.state,t.location,t.replace);function s(i,o=!0){o||r.pauseListeners(),history.go(i)}const n=ne({location:"",base:e,go:s,createHref:Tp.bind(null,e)},t,r);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function Lp(e){return typeof e=="string"||e&&typeof e=="object"}function rc(e){return typeof e=="string"||typeof e=="symbol"}const sc=Symbol("");var Qo;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Qo||(Qo={}));function Pr(e,t){return ne(new Error,{type:e,[sc]:!0},t)}function mt(e,t){return e instanceof Error&&sc in e&&(t==null||!!(e.type&t))}const Yo="[^/]+?",Dp={sensitive:!1,strict:!1,start:!0,end:!0},Mp=/[.+*?^${}()[\]/\\]/g;function Up(e,t){const r=ne({},Dp,t),s=[];let n=r.start?"^":"";const i=[];for(const u of e){const c=u.length?[]:[90];r.strict&&!u.length&&(n+="/");for(let f=0;f<u.length;f++){const d=u[f];let h=40+(r.sensitive?.25:0);if(d.type===0)f||(n+="/"),n+=d.value.replace(Mp,"\\$&"),h+=40;else if(d.type===1){const{value:m,repeatable:v,optional:y,regexp:x}=d;i.push({name:m,repeatable:v,optional:y});const A=x||Yo;if(A!==Yo){h+=10;try{new RegExp(`(${A})`)}catch(T){throw new Error(`Invalid custom RegExp for param "${m}" (${A}): `+T.message)}}let k=v?`((?:${A})(?:/(?:${A}))*)`:`(${A})`;f||(k=y&&u.length<2?`(?:/${k})`:"/"+k),y&&(k+="?"),n+=k,h+=20,y&&(h+=-8),v&&(h+=-20),A===".*"&&(h+=-50)}c.push(h)}s.push(c)}if(r.strict&&r.end){const u=s.length-1;s[u][s[u].length-1]+=.7000000000000001}r.strict||(n+="/?"),r.end?n+="$":r.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const o=new RegExp(n,r.sensitive?"":"i");function a(u){const c=u.match(o),f={};if(!c)return null;for(let d=1;d<c.length;d++){const h=c[d]||"",m=i[d-1];f[m.name]=h&&m.repeatable?h.split("/"):h}return f}function l(u){let c="",f=!1;for(const d of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const h of d)if(h.type===0)c+=h.value;else if(h.type===1){const{value:m,repeatable:v,optional:y}=h,x=m in u?u[m]:"";if(tt(x)&&!v)throw new Error(`Provided param "${m}" is an array but it is not repeatable (* or + modifiers)`);const A=tt(x)?x.join("/"):x;if(!A)if(y)d.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${m}"`);c+=A}}return c||"/"}return{re:o,score:s,keys:i,parse:a,stringify:l}}function Np(e,t){let r=0;for(;r<e.length&&r<t.length;){const s=t[r]-e[r];if(s)return s;r++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function nc(e,t){let r=0;const s=e.score,n=t.score;for(;r<s.length&&r<n.length;){const i=Np(s[r],n[r]);if(i)return i;r++}if(Math.abs(n.length-s.length)===1){if(Xo(s))return 1;if(Xo(n))return-1}return n.length-s.length}function Xo(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Fp={type:0,value:""},Bp=/[a-zA-Z0-9_]/;function qp(e){if(!e)return[[]];if(e==="/")return[[Fp]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(h){throw new Error(`ERR (${r})/"${u}": ${h}`)}let r=0,s=r;const n=[];let i;function o(){i&&n.push(i),i=[]}let a=0,l,u="",c="";function f(){u&&(r===0?i.push({type:0,value:u}):r===1||r===2||r===3?(i.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:u,regexp:c,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),u="")}function d(){u+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&r!==2){s=r,r=4;continue}switch(r){case 0:l==="/"?(u&&f(),o()):l===":"?(f(),r=1):d();break;case 4:d(),r=s;break;case 1:l==="("?r=2:Bp.test(l)?d():(f(),r=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+l:r=3:c+=l;break;case 3:f(),r=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,c="";break;default:t("Unknown state");break}}return r===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),o(),n}function Hp(e,t,r){const s=Up(qp(e.path),r),n=ne(s,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function Vp(e,t){const r=[],s=new Map;t=ra({strict:!1,end:!0,sensitive:!1},t);function n(f){return s.get(f)}function i(f,d,h){const m=!h,v=ea(f);v.aliasOf=h&&h.record;const y=ra(t,f),x=[v];if("alias"in f){const T=typeof f.alias=="string"?[f.alias]:f.alias;for(const P of T)x.push(ea(ne({},v,{components:h?h.record.components:v.components,path:P,aliasOf:h?h.record:v})))}let A,k;for(const T of x){const{path:P}=T;if(d&&P[0]!=="/"){const W=d.record.path,V=W[W.length-1]==="/"?"":"/";T.path=d.record.path+(P&&V+P)}if(A=Hp(T,d,y),h?h.alias.push(A):(k=k||A,k!==A&&k.alias.push(A),m&&f.name&&!ta(A)&&o(f.name)),ic(A)&&l(A),v.children){const W=v.children;for(let V=0;V<W.length;V++)i(W[V],A,h&&h.children[V])}h=h||A}return k?()=>{o(k)}:Zr}function o(f){if(rc(f)){const d=s.get(f);d&&(s.delete(f),r.splice(r.indexOf(d),1),d.children.forEach(o),d.alias.forEach(o))}else{const d=r.indexOf(f);d>-1&&(r.splice(d,1),f.record.name&&s.delete(f.record.name),f.children.forEach(o),f.alias.forEach(o))}}function a(){return r}function l(f){const d=Wp(f,r);r.splice(d,0,f),f.record.name&&!ta(f)&&s.set(f.record.name,f)}function u(f,d){let h,m={},v,y;if("name"in f&&f.name){if(h=s.get(f.name),!h)throw Pr(1,{location:f});y=h.record.name,m=ne(Zo(d.params,h.keys.filter(k=>!k.optional).concat(h.parent?h.parent.keys.filter(k=>k.optional):[]).map(k=>k.name)),f.params&&Zo(f.params,h.keys.map(k=>k.name))),v=h.stringify(m)}else if(f.path!=null)v=f.path,h=r.find(k=>k.re.test(v)),h&&(m=h.parse(v),y=h.record.name);else{if(h=d.name?s.get(d.name):r.find(k=>k.re.test(d.path)),!h)throw Pr(1,{location:f,currentLocation:d});y=h.record.name,m=ne({},d.params,f.params),v=h.stringify(m)}const x=[];let A=h;for(;A;)x.unshift(A.record),A=A.parent;return{name:y,path:v,params:m,matched:x,meta:zp(x)}}e.forEach(f=>i(f));function c(){r.length=0,s.clear()}return{addRoute:i,resolve:u,removeRoute:o,clearRoutes:c,getRoutes:a,getRecordMatcher:n}}function Zo(e,t){const r={};for(const s of t)s in e&&(r[s]=e[s]);return r}function ea(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Kp(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Kp(e){const t={},r=e.props||!1;if("component"in e)t.default=r;else for(const s in e.components)t[s]=typeof r=="object"?r[s]:r;return t}function ta(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function zp(e){return e.reduce((t,r)=>ne(t,r.meta),{})}function ra(e,t){const r={};for(const s in e)r[s]=s in t?t[s]:e[s];return r}function Wp(e,t){let r=0,s=t.length;for(;r!==s;){const i=r+s>>1;nc(e,t[i])<0?s=i:r=i+1}const n=Gp(e);return n&&(s=t.lastIndexOf(n,s-1)),s}function Gp(e){let t=e;for(;t=t.parent;)if(ic(t)&&nc(e,t)===0)return t}function ic({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Jp(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<s.length;++n){const i=s[n].replace(Ql," "),o=i.indexOf("="),a=cs(o<0?i:i.slice(0,o)),l=o<0?null:cs(i.slice(o+1));if(a in t){let u=t[a];tt(u)||(u=t[a]=[u]),u.push(l)}else t[a]=l}return t}function sa(e){let t="";for(let r in e){const s=e[r];if(r=gp(r),s==null){s!==void 0&&(t+=(t.length?"&":"")+r);continue}(tt(s)?s.map(i=>i&&Zn(i)):[s&&Zn(s)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+r,i!=null&&(t+="="+i))})}return t}function Qp(e){const t={};for(const r in e){const s=e[r];s!==void 0&&(t[r]=tt(s)?s.map(n=>n==null?null:""+n):s==null?s:""+s)}return t}const Yp=Symbol(""),na=Symbol(""),cn=Symbol(""),Pi=Symbol(""),ti=Symbol("");function Ur(){let e=[];function t(s){return e.push(s),()=>{const n=e.indexOf(s);n>-1&&e.splice(n,1)}}function r(){e=[]}return{add:t,list:()=>e.slice(),reset:r}}function Dt(e,t,r,s,n,i=o=>o()){const o=s&&(s.enterCallbacks[n]=s.enterCallbacks[n]||[]);return()=>new Promise((a,l)=>{const u=d=>{d===!1?l(Pr(4,{from:r,to:t})):d instanceof Error?l(d):Lp(d)?l(Pr(2,{from:t,to:d})):(o&&s.enterCallbacks[n]===o&&typeof d=="function"&&o.push(d),a())},c=i(()=>e.call(s&&s.instances[n],t,r,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(d=>l(d))})}function Pn(e,t,r,s,n=i=>i()){const i=[];for(const o of e)for(const a in o.components){let l=o.components[a];if(!(t!=="beforeRouteEnter"&&!o.instances[a]))if(Gl(l)){const c=(l.__vccOpts||l)[t];c&&i.push(Dt(c,r,s,o,a,n))}else{let u=l();i.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${a}" at "${o.path}"`);const f=np(c)?c.default:c;o.mods[a]=c,o.components[a]=f;const h=(f.__vccOpts||f)[t];return h&&Dt(h,r,s,o,a,n)()}))}}return i}function ia(e){const t=Ge(cn),r=Ge(Pi),s=ue(()=>{const l=Sr(e.to);return t.resolve(l)}),n=ue(()=>{const{matched:l}=s.value,{length:u}=l,c=l[u-1],f=r.matched;if(!c||!f.length)return-1;const d=f.findIndex(Ar.bind(null,c));if(d>-1)return d;const h=oa(l[u-2]);return u>1&&oa(c)===h&&f[f.length-1].path!==h?f.findIndex(Ar.bind(null,l[u-2])):d}),i=ue(()=>n.value>-1&&rg(r.params,s.value.params)),o=ue(()=>n.value>-1&&n.value===r.matched.length-1&&ec(r.params,s.value.params));function a(l={}){if(tg(l)){const u=t[Sr(e.replace)?"replace":"push"](Sr(e.to)).catch(Zr);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:s,href:ue(()=>s.value.href),isActive:i,isExactActive:o,navigate:a}}function Xp(e){return e.length===1?e[0]:e}const Zp=Rr({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:ia,setup(e,{slots:t}){const r=ds(ia(e)),{options:s}=Ge(cn),n=ue(()=>({[aa(e.activeClass,s.linkActiveClass,"router-link-active")]:r.isActive,[aa(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:r.isExactActive}));return()=>{const i=t.default&&Xp(t.default(r));return e.custom?i:ut("a",{"aria-current":r.isExactActive?e.ariaCurrentValue:null,href:r.href,onClick:r.navigate,class:n.value},i)}}}),eg=Zp;function tg(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function rg(e,t){for(const r in t){const s=t[r],n=e[r];if(typeof s=="string"){if(s!==n)return!1}else if(!tt(n)||n.length!==s.length||s.some((i,o)=>i!==n[o]))return!1}return!0}function oa(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const aa=(e,t,r)=>e??t??r,sg=Rr({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:r}){const s=Ge(ti),n=ue(()=>e.route||s.value),i=Ge(na,0),o=ue(()=>{let u=Sr(i);const{matched:c}=n.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),a=ue(()=>n.value.matched[o.value]);Ps(na,ue(()=>o.value+1)),Ps(Yp,a),Ps(ti,n);const l=me();return Wr(()=>[l.value,a.value,e.name],([u,c,f],[d,h,m])=>{c&&(c.instances[f]=u,h&&h!==c&&u&&u===d&&(c.leaveGuards.size||(c.leaveGuards=h.leaveGuards),c.updateGuards.size||(c.updateGuards=h.updateGuards))),u&&c&&(!h||!Ar(c,h)||!d)&&(c.enterCallbacks[f]||[]).forEach(v=>v(u))},{flush:"post"}),()=>{const u=n.value,c=e.name,f=a.value,d=f&&f.components[c];if(!d)return la(r.default,{Component:d,route:u});const h=f.props[c],m=h?h===!0?u.params:typeof h=="function"?h(u):h:null,y=ut(d,ne({},m,t,{onVnodeUnmounted:x=>{x.component.isUnmounted&&(f.instances[c]=null)},ref:l}));return la(r.default,{Component:y,route:u})||y}}});function la(e,t){if(!e)return null;const r=e(t);return r.length===1?r[0]:r}const ng=sg;function ig(e){const t=Vp(e.routes,e),r=e.parseQuery||Jp,s=e.stringifyQuery||sa,n=e.history,i=Ur(),o=Ur(),a=Ur(),l=Fc(At);let u=At;yr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=Cn.bind(null,b=>""+b),f=Cn.bind(null,vp),d=Cn.bind(null,cs);function h(b,M){let I,N;return rc(b)?(I=t.getRecordMatcher(b),N=M):N=b,t.addRoute(N,I)}function m(b){const M=t.getRecordMatcher(b);M&&t.removeRoute(M)}function v(){return t.getRoutes().map(b=>b.record)}function y(b){return!!t.getRecordMatcher(b)}function x(b,M){if(M=ne({},M||l.value),typeof b=="string"){const _=An(r,b,M.path),w=t.resolve({path:_.path},M),E=n.createHref(_.fullPath);return ne(_,w,{params:d(w.params),hash:cs(_.hash),redirectedFrom:void 0,href:E})}let I;if(b.path!=null)I=ne({},b,{path:An(r,b.path,M.path).path});else{const _=ne({},b.params);for(const w in _)_[w]==null&&delete _[w];I=ne({},b,{params:f(_)}),M.params=f(M.params)}const N=t.resolve(I,M),le=b.hash||"";N.params=c(d(N.params));const p=wp(s,ne({},b,{hash:pp(le),path:N.path})),g=n.createHref(p);return ne({fullPath:p,hash:le,query:s===sa?Qp(b.query):b.query||{}},N,{redirectedFrom:void 0,href:g})}function A(b){return typeof b=="string"?An(r,b,l.value.path):ne({},b)}function k(b,M){if(u!==b)return Pr(8,{from:M,to:b})}function T(b){return V(b)}function P(b){return T(ne(A(b),{replace:!0}))}function W(b){const M=b.matched[b.matched.length-1];if(M&&M.redirect){const{redirect:I}=M;let N=typeof I=="function"?I(b):I;return typeof N=="string"&&(N=N.includes("?")||N.includes("#")?N=A(N):{path:N},N.params={}),ne({query:b.query,hash:b.hash,params:N.path!=null?{}:b.params},N)}}function V(b,M){const I=u=x(b),N=l.value,le=b.state,p=b.force,g=b.replace===!0,_=W(I);if(_)return V(ne(A(_),{state:typeof _=="object"?ne({},le,_.state):le,force:p,replace:g}),M||I);const w=I;w.redirectedFrom=M;let E;return!p&&bp(s,N,I)&&(E=Pr(16,{to:w,from:N}),rt(N,N,!0,!1)),(E?Promise.resolve(E):G(w,N)).catch(S=>mt(S)?mt(S,2)?S:Tt(S):se(S,w,N)).then(S=>{if(S){if(mt(S,2))return V(ne({replace:g},A(S.to),{state:typeof S.to=="object"?ne({},le,S.to.state):le,force:p}),M||w)}else S=D(w,N,!0,g,le);return ee(w,N,S),S})}function q(b,M){const I=k(b,M);return I?Promise.reject(I):Promise.resolve()}function $(b){const M=nr.values().next().value;return M&&typeof M.runWithContext=="function"?M.runWithContext(b):b()}function G(b,M){let I;const[N,le,p]=og(b,M);I=Pn(N.reverse(),"beforeRouteLeave",b,M);for(const _ of N)_.leaveGuards.forEach(w=>{I.push(Dt(w,b,M))});const g=q.bind(null,b,M);return I.push(g),He(I).then(()=>{I=[];for(const _ of i.list())I.push(Dt(_,b,M));return I.push(g),He(I)}).then(()=>{I=Pn(le,"beforeRouteUpdate",b,M);for(const _ of le)_.updateGuards.forEach(w=>{I.push(Dt(w,b,M))});return I.push(g),He(I)}).then(()=>{I=[];for(const _ of p)if(_.beforeEnter)if(tt(_.beforeEnter))for(const w of _.beforeEnter)I.push(Dt(w,b,M));else I.push(Dt(_.beforeEnter,b,M));return I.push(g),He(I)}).then(()=>(b.matched.forEach(_=>_.enterCallbacks={}),I=Pn(p,"beforeRouteEnter",b,M,$),I.push(g),He(I))).then(()=>{I=[];for(const _ of o.list())I.push(Dt(_,b,M));return I.push(g),He(I)}).catch(_=>mt(_,8)?_:Promise.reject(_))}function ee(b,M,I){a.list().forEach(N=>$(()=>N(b,M,I)))}function D(b,M,I,N,le){const p=k(b,M);if(p)return p;const g=M===At,_=yr?history.state:{};I&&(N||g?n.replace(b.fullPath,ne({scroll:g&&_&&_.scroll},le)):n.push(b.fullPath,le)),l.value=b,rt(b,M,I,g),Tt()}let X;function ye(){X||(X=n.listen((b,M,I)=>{if(!_s.listening)return;const N=x(b),le=W(N);if(le){V(ne(le,{replace:!0,force:!0}),N).catch(Zr);return}u=N;const p=l.value;yr&&Pp(Go(p.fullPath,I.delta),ln()),G(N,p).catch(g=>mt(g,12)?g:mt(g,2)?(V(ne(A(g.to),{force:!0}),N).then(_=>{mt(_,20)&&!I.delta&&I.type===us.pop&&n.go(-1,!1)}).catch(Zr),Promise.reject()):(I.delta&&n.go(-I.delta,!1),se(g,N,p))).then(g=>{g=g||D(N,p,!1),g&&(I.delta&&!mt(g,8)?n.go(-I.delta,!1):I.type===us.pop&&mt(g,20)&&n.go(-1,!1)),ee(N,p,g)}).catch(Zr)}))}let Pe=Ur(),ae=Ur(),J;function se(b,M,I){Tt(b);const N=ae.list();return N.length?N.forEach(le=>le(b,M,I)):console.error(b),Promise.reject(b)}function pt(){return J&&l.value!==At?Promise.resolve():new Promise((b,M)=>{Pe.add([b,M])})}function Tt(b){return J||(J=!b,ye(),Pe.list().forEach(([M,I])=>b?I(b):M()),Pe.reset()),b}function rt(b,M,I,N){const{scrollBehavior:le}=e;if(!yr||!le)return Promise.resolve();const p=!I&&Op(Go(b.fullPath,0))||(N||!I)&&history.state&&history.state.scroll||null;return Zs().then(()=>le(b,M,p)).then(g=>g&&Ap(g)).catch(g=>se(g,b,M))}const je=b=>n.go(b);let sr;const nr=new Set,_s={currentRoute:l,listening:!0,addRoute:h,removeRoute:m,clearRoutes:t.clearRoutes,hasRoute:y,getRoutes:v,resolve:x,options:e,push:T,replace:P,go:je,back:()=>je(-1),forward:()=>je(1),beforeEach:i.add,beforeResolve:o.add,afterEach:a.add,onError:ae.add,isReady:pt,install(b){const M=this;b.component("RouterLink",eg),b.component("RouterView",ng),b.config.globalProperties.$router=M,Object.defineProperty(b.config.globalProperties,"$route",{enumerable:!0,get:()=>Sr(l)}),yr&&!sr&&l.value===At&&(sr=!0,T(n.location).catch(le=>{}));const I={};for(const le in At)Object.defineProperty(I,le,{get:()=>l.value[le],enumerable:!0});b.provide(cn,M),b.provide(Pi,ja(I)),b.provide(ti,l);const N=b.unmount;nr.add(b),b.unmount=function(){nr.delete(b),nr.size<1&&(u=At,X&&X(),X=null,l.value=At,sr=!1,J=!1),N()}}};function He(b){return b.reduce((M,I)=>M.then(()=>$(I)),Promise.resolve())}return _s}function og(e,t){const r=[],s=[],n=[],i=Math.max(t.matched.length,e.matched.length);for(let o=0;o<i;o++){const a=t.matched[o];a&&(e.matched.find(u=>Ar(u,a))?s.push(a):r.push(a));const l=e.matched[o];l&&(t.matched.find(u=>Ar(u,l))||n.push(l))}return[r,s,n]}function ag(){return Ge(cn)}function lm(e){return Ge(Pi)}const lg={class:"bg-white shadow-sm sticky top-0 z-40"},cg={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ug={class:"flex justify-between items-center h-16"},fg={class:"flex items-center"},dg={class:"hidden md:flex items-center space-x-8"},hg={class:"flex items-center space-x-4"},pg={class:"hidden lg:block"},gg={class:"relative"},mg={key:0,class:"absolute -top-1 -right-1 bg-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"},vg={key:0,class:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50"},_g={key:1,class:"flex items-center space-x-2"},yg={key:0,class:"md:hidden border-t border-gray-200 py-4"},wg={class:"space-y-2"},bg={class:"px-4 py-2"},Sg=Rr({__name:"AppHeader",props:{isAuthenticated:{type:Boolean},cartItemCount:{}},setup(e){const t=ag(),r=an(),s=me(""),n=me(!1),i=me(!1),o=me(),a=()=>{s.value.trim()&&(t.push({name:"products",query:{search:s.value}}),s.value="",i.value=!1)},l=async()=>{await r.signOut(),n.value=!1,t.push("/")},u=c=>{o.value&&!o.value.contains(c.target)&&(n.value=!1)};return vi(()=>{document.addEventListener("click",u)}),_i(()=>{document.removeEventListener("click",u)}),(c,f)=>{const d=yi("router-link");return Ae(),Me("header",lg,[U("div",cg,[U("div",ug,[U("div",fg,[re(d,{to:"/",class:"flex items-center space-x-2"},{default:De(()=>f[8]||(f[8]=[U("div",{class:"text-2xl"},"🌿",-1),U("span",{class:"text-xl font-serif font-bold text-primary-600"}," Savory Tunisian Bounty ",-1)])),_:1,__:[8]})]),U("nav",dg,[re(d,{to:"/",class:"text-gray-700 hover:text-primary-600 font-medium transition-colors","active-class":"text-primary-600"},{default:De(()=>f[9]||(f[9]=[Ye(" Home ")])),_:1,__:[9]}),re(d,{to:"/products",class:"text-gray-700 hover:text-primary-600 font-medium transition-colors","active-class":"text-primary-600"},{default:De(()=>f[10]||(f[10]=[Ye(" Products ")])),_:1,__:[10]}),f[11]||(f[11]=U("a",{href:"#",class:"text-gray-700 hover:text-primary-600 font-medium transition-colors"}," About ",-1)),f[12]||(f[12]=U("a",{href:"#",class:"text-gray-700 hover:text-primary-600 font-medium transition-colors"}," Contact ",-1))]),U("div",hg,[U("div",pg,[U("div",gg,[jn(U("input",{"onUpdate:modelValue":f[0]||(f[0]=h=>s.value=h),type:"text",placeholder:"Search products...",class:"w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",onKeyup:_o(a,["enter"])},null,544),[[qn,s.value]]),f[13]||(f[13]=U("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[U("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[U("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1))])]),re(d,{to:"/cart",class:"relative p-2 text-gray-700 hover:text-primary-600 transition-colors"},{default:De(()=>[f[14]||(f[14]=U("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[U("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0V19a2 2 0 002 2h7a2 2 0 002-2v-1.5"})],-1)),c.cartItemCount>0?(Ae(),Me("span",mg,ts(c.cartItemCount),1)):$s("",!0)]),_:1,__:[14]}),c.isAuthenticated?(Ae(),Me("div",{key:0,class:"relative",ref_key:"userMenuRef",ref:o},[U("button",{onClick:f[1]||(f[1]=h=>n.value=!n.value),class:"flex items-center space-x-2 p-2 text-gray-700 hover:text-primary-600 transition-colors"},f[15]||(f[15]=[U("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[U("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1),U("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[U("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})],-1)])),n.value?(Ae(),Me("div",vg,[re(d,{to:"/profile",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:f[2]||(f[2]=h=>n.value=!1)},{default:De(()=>f[16]||(f[16]=[Ye(" Profile ")])),_:1,__:[16]}),re(d,{to:"/orders",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:f[3]||(f[3]=h=>n.value=!1)},{default:De(()=>f[17]||(f[17]=[Ye(" Order History ")])),_:1,__:[17]}),U("button",{onClick:l,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Sign Out ")])):$s("",!0)],512)):(Ae(),Me("div",_g,[re(d,{to:"/login",class:"text-gray-700 hover:text-primary-600 font-medium transition-colors"},{default:De(()=>f[18]||(f[18]=[Ye(" Sign In ")])),_:1,__:[18]}),re(d,{to:"/register",class:"btn-primary px-4 py-2"},{default:De(()=>f[19]||(f[19]=[Ye(" Sign Up ")])),_:1,__:[19]})])),U("button",{onClick:f[4]||(f[4]=h=>i.value=!i.value),class:"md:hidden p-2 text-gray-700 hover:text-primary-600 transition-colors"},f[20]||(f[20]=[U("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[U("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])]),i.value?(Ae(),Me("div",yg,[U("div",wg,[re(d,{to:"/",class:"block px-4 py-2 text-gray-700 hover:text-primary-600 font-medium",onClick:f[5]||(f[5]=h=>i.value=!1)},{default:De(()=>f[21]||(f[21]=[Ye(" Home ")])),_:1,__:[21]}),re(d,{to:"/products",class:"block px-4 py-2 text-gray-700 hover:text-primary-600 font-medium",onClick:f[6]||(f[6]=h=>i.value=!1)},{default:De(()=>f[22]||(f[22]=[Ye(" Products ")])),_:1,__:[22]}),f[23]||(f[23]=U("a",{href:"#",class:"block px-4 py-2 text-gray-700 hover:text-primary-600 font-medium"}," About ",-1)),f[24]||(f[24]=U("a",{href:"#",class:"block px-4 py-2 text-gray-700 hover:text-primary-600 font-medium"}," Contact ",-1)),U("div",bg,[jn(U("input",{"onUpdate:modelValue":f[7]||(f[7]=h=>s.value=h),type:"text",placeholder:"Search products...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",onKeyup:_o(a,["enter"])},null,544),[[qn,s.value]])])])])):$s("",!0)])])}}}),xg={class:"bg-gray-900 text-white"},Eg={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"},kg={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Tg={class:"space-y-2"},Cg={class:"border-t border-gray-800 mt-8 pt-8"},Ag={class:"max-w-md mx-auto text-center"},Pg=["disabled"],Og={key:0},Rg={key:1},$g={key:0,class:"mt-2 text-sm text-green-400"},jg={class:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400"},Ig=Rr({__name:"AppFooter",setup(e){const t=me(""),r=me(!1),s=me(""),n=ue(()=>new Date().getFullYear()),i=async()=>{try{r.value=!0,await new Promise(o=>setTimeout(o,1e3)),s.value="Thank you for subscribing!",t.value="",setTimeout(()=>{s.value=""},3e3)}catch(o){console.error("Newsletter signup error:",o),s.value="Failed to subscribe. Please try again."}finally{r.value=!1}};return(o,a)=>{const l=yi("router-link");return Ae(),Me("footer",xg,[U("div",Eg,[U("div",kg,[a[7]||(a[7]=Gi('<div class="col-span-1 md:col-span-2"><div class="flex items-center space-x-2 mb-4"><div class="text-2xl">🌿</div><span class="text-xl font-serif font-bold text-primary-400"> Savory Tunisian Bounty </span></div><p class="text-gray-300 mb-4 max-w-md"> Bringing you the authentic flavors of Tunisia through premium spices, olive oils, and traditional artisanal products sourced directly from local producers. </p><div class="flex space-x-4"><a href="#" class="text-gray-400 hover:text-primary-400 transition-colors"><svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path></svg></a><a href="#" class="text-gray-400 hover:text-primary-400 transition-colors"><svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"></path></svg></a><a href="#" class="text-gray-400 hover:text-primary-400 transition-colors"><svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"></path></svg></a><a href="#" class="text-gray-400 hover:text-primary-400 transition-colors"><svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"></path></svg></a></div></div>',1)),U("div",null,[a[6]||(a[6]=U("h3",{class:"text-lg font-semibold mb-4"},"Quick Links",-1)),U("ul",Tg,[U("li",null,[re(l,{to:"/",class:"text-gray-300 hover:text-primary-400 transition-colors"},{default:De(()=>a[1]||(a[1]=[Ye(" Home ")])),_:1,__:[1]})]),U("li",null,[re(l,{to:"/products",class:"text-gray-300 hover:text-primary-400 transition-colors"},{default:De(()=>a[2]||(a[2]=[Ye(" Products ")])),_:1,__:[2]})]),a[3]||(a[3]=U("li",null,[U("a",{href:"#",class:"text-gray-300 hover:text-primary-400 transition-colors"}," About Us ")],-1)),a[4]||(a[4]=U("li",null,[U("a",{href:"#",class:"text-gray-300 hover:text-primary-400 transition-colors"}," Producer Stories ")],-1)),a[5]||(a[5]=U("li",null,[U("a",{href:"#",class:"text-gray-300 hover:text-primary-400 transition-colors"}," Contact ")],-1))])]),a[8]||(a[8]=Gi('<div><h3 class="text-lg font-semibold mb-4">Customer Service</h3><ul class="space-y-2"><li><a href="#" class="text-gray-300 hover:text-primary-400 transition-colors"> Shipping Info </a></li><li><a href="#" class="text-gray-300 hover:text-primary-400 transition-colors"> Returns &amp; Exchanges </a></li><li><a href="#" class="text-gray-300 hover:text-primary-400 transition-colors"> FAQ </a></li><li><a href="#" class="text-gray-300 hover:text-primary-400 transition-colors"> Privacy Policy </a></li><li><a href="#" class="text-gray-300 hover:text-primary-400 transition-colors"> Terms of Service </a></li></ul></div>',1))]),U("div",Cg,[U("div",Ag,[a[9]||(a[9]=U("h3",{class:"text-lg font-semibold mb-2"},"Stay Updated",-1)),a[10]||(a[10]=U("p",{class:"text-gray-300 mb-4"}," Subscribe to our newsletter for exclusive offers and new product announcements ",-1)),U("form",{onSubmit:Uf(i,["prevent"]),class:"flex"},[jn(U("input",{"onUpdate:modelValue":a[0]||(a[0]=u=>t.value=u),type:"email",placeholder:"Enter your email",required:"",class:"flex-1 px-4 py-2 rounded-l-lg border-0 focus:outline-none focus:ring-2 focus:ring-primary-500 text-gray-900"},null,512),[[qn,t.value]]),U("button",{type:"submit",disabled:r.value,class:"bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-r-lg transition-colors disabled:opacity-50"},[r.value?(Ae(),Me("span",Og,"...")):(Ae(),Me("span",Rg,"Subscribe"))],8,Pg)],32),s.value?(Ae(),Me("p",$g,ts(s.value),1)):$s("",!0)])]),U("div",jg,[U("p",null,"© "+ts(n.value)+" Savory Tunisian Bounty. All rights reserved.",1)])])])}}}),Lg={class:"fixed top-4 right-4 z-50 space-y-2"},Dg={class:"p-4"},Mg={class:"flex items-start"},Ug={class:"flex-shrink-0"},Ng={class:"ml-3 w-0 flex-1 pt-0.5"},Fg={class:"text-sm font-medium text-gray-900"},Bg={class:"ml-4 flex-shrink-0 flex"},qg=["onClick"],Hg=Rr({__name:"ToastContainer",setup(e){const t=vs(),r=ue(()=>t.toasts),s=o=>{t.removeToast(o)},n={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"},i={success:()=>ut("svg",{class:"text-green-400",fill:"currentColor",viewBox:"0 0 20 20"},[ut("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})]),error:()=>ut("svg",{class:"text-red-400",fill:"currentColor",viewBox:"0 0 20 20"},[ut("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})]),warning:()=>ut("svg",{class:"text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20"},[ut("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})]),info:()=>ut("svg",{class:"text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[ut("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z","clip-rule":"evenodd"})])};return(o,a)=>(Ae(),Me("div",Lg,[re(Of,{name:"toast",tag:"div",class:"space-y-2"},{default:De(()=>[(Ae(!0),Me(Ke,null,pu(r.value,l=>(Ae(),Me("div",{key:l.id,class:Qs(["max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden",n[l.type]])},[U("div",Dg,[U("div",Mg,[U("div",Ug,[(Ae(),pl(hu(i[l.type]),{class:"h-6 w-6"}))]),U("div",Ng,[U("p",Fg,ts(l.message),1)]),U("div",Bg,[U("button",{onClick:u=>s(l.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},a[0]||(a[0]=[U("span",{class:"sr-only"},"Close",-1),U("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[U("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,qg)])])])],2))),128))]),_:1})]))}}),Vg=(e,t)=>{const r=e.__vccOpts||e;for(const[s,n]of t)r[s]=n;return r},Kg=Vg(Hg,[["__scopeId","data-v-53de0714"]]),zg={id:"app",class:"min-h-screen flex flex-col"},Wg={class:"flex-1"},Gg=Rr({__name:"App",setup(e){const t=an(),r=Wl(),s=ue(()=>t.isAuthenticated),n=ue(()=>r.itemCount);return(i,o)=>{const a=yi("RouterView");return Ae(),Me("div",zg,[re(Sg,{"is-authenticated":s.value,"cart-item-count":n.value},null,8,["is-authenticated","cart-item-count"]),U("main",Wg,[re(a)]),re(Ig),re(Kg)])}}}),oc=ig({history:Ip("/"),routes:[{path:"/",name:"home",component:()=>Te(()=>import("./HomeView-BeqJDZnt.js"),__vite__mapDeps([0,1,2]))},{path:"/products",name:"products",component:()=>Te(()=>import("./ProductsView-DOD-q-Fm.js"),__vite__mapDeps([3,1,2]))},{path:"/products/:id",name:"product-detail",component:()=>Te(()=>import("./ProductDetailView-BL47125-.js"),__vite__mapDeps([4,1,2])),props:!0},{path:"/cart",name:"cart",component:()=>Te(()=>import("./CartView-BQUm6eEw.js"),[])},{path:"/login",name:"login",component:()=>Te(()=>import("./LoginView-BdCsQ28p.js"),[]),meta:{requiresGuest:!0}},{path:"/register",name:"register",component:()=>Te(()=>import("./RegisterView-BedkGpjy.js"),[]),meta:{requiresGuest:!0}},{path:"/profile",name:"profile",component:()=>Te(()=>import("./ProfileView-BEpWjvsw.js"),[]),meta:{requiresAuth:!0}},{path:"/checkout",name:"checkout",component:()=>Te(()=>import("./CheckoutView-CK9-6OQw.js"),[]),meta:{requiresAuth:!0}},{path:"/orders",name:"orders",component:()=>Te(()=>import("./OrdersView-plBu6N9H.js"),[]),meta:{requiresAuth:!0}},{path:"/orders/:id",name:"order-confirmation",component:()=>Te(()=>import("./OrderConfirmationView-BqjAYiWj.js"),[]),meta:{requiresAuth:!0}}]});oc.beforeEach((e,t,r)=>{const s=an();if(e.meta.requiresAuth&&!s.isAuthenticated){r({name:"login",query:{redirect:e.fullPath}});return}if(e.meta.requiresGuest&&s.isAuthenticated){r({name:"home"});return}r()});class Nr{static async getUserAddresses(t){try{const{data:r,error:s}=await ge.from("user_addresses").select("*").eq("user_id",t).order("is_default",{ascending:!1}).order("created_at",{ascending:!1});if(s)throw s;return r||[]}catch(r){throw console.error("Error fetching user addresses:",r),r}}static async getDefaultAddress(t){try{const{data:r,error:s}=await ge.from("user_addresses").select("*").eq("user_id",t).eq("is_default",!0).single();if(s&&s.code!=="PGRST116")throw s;return r||null}catch(r){throw console.error("Error fetching default address:",r),r}}static async createAddress(t){try{t.is_default&&await this.unsetDefaultAddresses(t.user_id);const{data:r,error:s}=await ge.from("user_addresses").insert(t).select().single();if(s)throw s;return r}catch(r){throw console.error("Error creating address:",r),r}}static async updateAddress(t,r){try{r.is_default&&r.user_id&&await this.unsetDefaultAddresses(r.user_id);const{data:s,error:n}=await ge.from("user_addresses").update(r).eq("id",t).select().single();if(n)throw n;return s}catch(s){throw console.error("Error updating address:",s),s}}static async deleteAddress(t){try{const{error:r}=await ge.from("user_addresses").delete().eq("id",t);if(r)throw r}catch(r){throw console.error("Error deleting address:",r),r}}static async setDefaultAddress(t,r){try{await this.unsetDefaultAddresses(r);const{data:s,error:n}=await ge.from("user_addresses").update({is_default:!0}).eq("id",t).select().single();if(n)throw n;return s}catch(s){throw console.error("Error setting default address:",s),s}}static async unsetDefaultAddresses(t){try{const{error:r}=await ge.from("user_addresses").update({is_default:!1}).eq("user_id",t).eq("is_default",!0);if(r)throw r}catch(r){throw console.error("Error unsetting default addresses:",r),r}}static async getAddressById(t){try{const{data:r,error:s}=await ge.from("user_addresses").select("*").eq("id",t).single();if(s&&s.code!=="PGRST116")throw s;return r||null}catch(r){throw console.error("Error fetching address by ID:",r),r}}static validateAddress(t){var s,n,i,o;const r=[];return(s=t.address_line_1)!=null&&s.trim()||r.push("Address line 1 is required"),(n=t.city)!=null&&n.trim()||r.push("City is required"),(i=t.postal_code)!=null&&i.trim()||r.push("Postal code is required"),(o=t.country)!=null&&o.trim()||r.push("Country is required"),r}}const Jg=gs("address",()=>{const e=me([]),t=me(!1),r=me(null),s=()=>{try{return vs()}catch{return null}},n=ue(()=>e.value.length>0),i=ue(()=>!!r.value),o=async m=>{try{t.value=!0;const v=await Nr.getUserAddresses(m);e.value=v,r.value=v.find(y=>y.is_default)||null}catch(v){console.error("Error fetching addresses:",v);const y=s();y&&y.error("Failed to load addresses")}finally{t.value=!1}},a=async m=>{try{t.value=!0;const v=await Nr.createAddress(m);e.value.unshift(v),v.is_default&&(e.value.forEach(x=>{x.id!==v.id&&(x.is_default=!1)}),r.value=v);const y=s();return y&&y.success("Address added successfully"),v}catch(v){console.error("Error creating address:",v);const y=s();return y&&y.error("Failed to add address"),null}finally{t.value=!1}},l=async(m,v)=>{try{t.value=!0;const y=await Nr.updateAddress(m,v),x=e.value.findIndex(k=>k.id===m);x!==-1&&(e.value[x]=y),y.is_default&&(e.value.forEach(k=>{k.id!==y.id&&(k.is_default=!1)}),r.value=y);const A=s();return A&&A.success("Address updated successfully"),y}catch(y){console.error("Error updating address:",y);const x=s();return x&&x.error("Failed to update address"),null}finally{t.value=!1}},u=async m=>{try{t.value=!0,await Nr.deleteAddress(m);const v=e.value.find(x=>x.id===m);e.value=e.value.filter(x=>x.id!==m),v!=null&&v.is_default&&(r.value=e.value.find(x=>x.is_default)||null);const y=s();return y&&y.success("Address deleted successfully"),!0}catch(v){console.error("Error deleting address:",v);const y=s();return y&&y.error("Failed to delete address"),!1}finally{t.value=!1}},c=async(m,v)=>{try{t.value=!0;const y=await Nr.setDefaultAddress(m,v);e.value.forEach(A=>{A.is_default=A.id===m}),r.value=y;const x=s();return x&&x.success("Default address updated"),!0}catch(y){console.error("Error setting default address:",y);const x=s();return x&&x.error("Failed to update default address"),!1}finally{t.value=!1}},f=m=>e.value.find(v=>v.id===m),d=()=>{e.value=[],r.value=null},h=m=>[m.address_line_1,m.address_line_2,m.city,m.postal_code,m.country].filter(Boolean).join(", ");return{addresses:ze(e),defaultAddress:ze(r),loading:ze(t),hasAddresses:n,hasDefaultAddress:i,fetchAddresses:o,createAddress:a,updateAddress:l,deleteAddress:u,setDefaultAddress:c,getAddressById:f,clearAddresses:d,formatAddress:h}});class Gt{static async createOrder(t){try{const{userId:r,cartItems:s,shippingAddressId:n,paymentIntentId:i}=t,o=s.reduce((d,h)=>d+h.product.price*h.quantity,0),a={user_id:r,status:"pending",total_amount:o,shipping_address_id:n,payment_status:i?"paid":"pending",payment_intent_id:i||null},{data:l,error:u}=await ge.from("orders").insert(a).select().single();if(u)throw u;const c=s.map(d=>({order_id:l.id,product_id:d.product.id,quantity:d.quantity,unit_price:d.product.price,total_price:d.product.price*d.quantity})),{error:f}=await ge.from("order_items").insert(c);if(f)throw f;return await this.updateProductStock(s),l}catch(r){throw console.error("Error creating order:",r),r}}static async updateProductStock(t){try{for(const r of t){const s=r.product.stock_quantity-r.quantity,{error:n}=await ge.from("products").update({stock_quantity:Math.max(0,s)}).eq("id",r.product.id);n&&console.error(`Error updating stock for product ${r.product.id}:`,n)}}catch(r){console.error("Error updating product stock:",r)}}static async getOrderById(t){try{const{data:r,error:s}=await ge.from("orders").select(`
          *,
          order_items (
            *,
            products (
              id,
              name,
              price,
              image_url
            )
          ),
          user_addresses (
            id,
            address_line_1,
            address_line_2,
            city,
            postal_code,
            country
          )
        `).eq("id",t).single();if(s&&s.code!=="PGRST116")throw s;return r||null}catch(r){throw console.error("Error fetching order:",r),r}}static async getUserOrders(t){try{const{data:r,error:s}=await ge.from("orders").select(`
          *,
          order_items (
            *,
            products (
              id,
              name,
              price,
              image_url
            )
          ),
          user_addresses (
            id,
            address_line_1,
            address_line_2,
            city,
            postal_code,
            country
          )
        `).eq("user_id",t).order("created_at",{ascending:!1});if(s)throw s;return r||[]}catch(r){throw console.error("Error fetching user orders:",r),r}}static async updateOrderStatus(t,r){try{const{data:s,error:n}=await ge.from("orders").update({status:r}).eq("id",t).select().single();if(n)throw n;return s}catch(s){throw console.error("Error updating order status:",s),s}}static async updatePaymentStatus(t,r,s){try{const n={payment_status:r};s&&(n.payment_intent_id=s);const{data:i,error:o}=await ge.from("orders").update(n).eq("id",t).select().single();if(o)throw o;return i}catch(n){throw console.error("Error updating payment status:",n),n}}static async validateCartItems(t){const r=[];if(!t.length)return r.push("Cart is empty"),r;try{for(const s of t){const{data:n,error:i}=await ge.from("products").select("stock_quantity, is_active").eq("id",s.product.id).single();if(i){r.push(`Product ${s.product.name} not found`);continue}n.is_active||r.push(`Product ${s.product.name} is no longer available`),n.stock_quantity<s.quantity&&r.push(`Insufficient stock for ${s.product.name}. Available: ${n.stock_quantity}, Requested: ${s.quantity}`)}}catch(s){console.error("Error validating cart items:",s),r.push("Error validating cart items")}return r}}const Qg=gs("order",()=>{const e=me([]),t=me(null),r=me(!1),s=me(!1),n=()=>{try{return vs()}catch{return null}},i=ue(()=>e.value.length>0),o=ue(()=>e.value.slice(0,5)),a=ue(()=>e.value.length),l=async k=>{try{s.value=!0;const T=await Gt.validateCartItems(k.cartItems);if(T.length>0){const q=n();return q&&T.forEach($=>q.error($)),null}const P=await Gt.createOrder(k),W=await Gt.getOrderById(P.id);W&&(e.value.unshift(W),t.value=W);const V=n();return V&&V.success("Order placed successfully!"),P}catch(T){console.error("Error creating order:",T);const P=n();return P&&P.error("Failed to place order. Please try again."),null}finally{s.value=!1}},u=async k=>{try{r.value=!0;const T=await Gt.getUserOrders(k);e.value=T}catch(T){console.error("Error fetching user orders:",T);const P=n();P&&P.error("Failed to load orders")}finally{r.value=!1}},c=async k=>{try{r.value=!0;const T=await Gt.getOrderById(k);if(T){t.value=T;const P=e.value.findIndex(W=>W.id===k);P!==-1?e.value[P]=T:e.value.unshift(T)}return T}catch(T){console.error("Error fetching order:",T);const P=n();return P&&P.error("Failed to load order details"),null}finally{r.value=!1}},f=async(k,T)=>{var P;try{const W=await Gt.updateOrderStatus(k,T),V=e.value.findIndex($=>$.id===k);V!==-1&&(e.value[V]={...e.value[V],...W}),((P=t.value)==null?void 0:P.id)===k&&(t.value={...t.value,...W});const q=n();return q&&q.success("Order status updated"),!0}catch(W){console.error("Error updating order status:",W);const V=n();return V&&V.error("Failed to update order status"),!1}},d=async(k,T,P)=>{var W;try{const V=await Gt.updatePaymentStatus(k,T,P),q=e.value.findIndex($=>$.id===k);return q!==-1&&(e.value[q]={...e.value[q],...V}),((W=t.value)==null?void 0:W.id)===k&&(t.value={...t.value,...V}),!0}catch(V){return console.error("Error updating payment status:",V),!1}},h=()=>{e.value=[],t.value=null},m=()=>{t.value=null},v=k=>{switch(k.toLowerCase()){case"pending":return"text-yellow-600 bg-yellow-100";case"confirmed":return"text-blue-600 bg-blue-100";case"processing":return"text-purple-600 bg-purple-100";case"shipped":return"text-indigo-600 bg-indigo-100";case"delivered":return"text-green-600 bg-green-100";case"cancelled":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},y=k=>{switch(k.toLowerCase()){case"paid":return"text-green-600 bg-green-100";case"pending":return"text-yellow-600 bg-yellow-100";case"failed":return"text-red-600 bg-red-100";case"refunded":return"text-gray-600 bg-gray-100";default:return"text-gray-600 bg-gray-100"}},x=k=>`#${k.id.slice(-8).toUpperCase()}`,A=k=>k.order_items.reduce((T,P)=>T+P.total_price,0);return{orders:ue(()=>[...e.value]),currentOrder:ze(t),loading:ze(r),creating:ze(s),hasOrders:i,recentOrders:o,orderCount:a,createOrder:l,fetchUserOrders:u,fetchOrderById:c,updateOrderStatus:f,updatePaymentStatus:d,clearOrders:h,clearCurrentOrder:m,getOrderStatusColor:v,getPaymentStatusColor:y,formatOrderNumber:x,calculateOrderTotal:A}}),Oi=qf(Gg),Yg=Kf();Oi.use(Yg);Oi.use(oc);const Xg=an(),Zg=Wl();vs();Jg();Qg();Xg.initialize();Zg.initialize();Oi.mount("#app");export{ge as A,an as B,ds as C,Uf as D,em as E,Ke as F,Nr as G,Jg as H,tm as I,Qg as J,vs as K,Sr as L,id as M,Vg as _,Me as a,U as b,ue as c,Rr as d,re as e,yi as f,pu as g,Ye as h,Ae as i,pl as j,Gi as k,Wr as l,$s as m,jn as n,vi as o,Qs as p,rm as q,me as r,lm as s,ts as t,ag as u,qn as v,De as w,Wl as x,gs as y,ze as z};
