{"root": ["../../env.d.ts", "../../src/app.vue", "../../src/main.ts", "../../src/components/addressform.vue", "../../src/components/addressselector.vue", "../../src/components/appfooter.vue", "../../src/components/appheader.vue", "../../src/components/helloworld.vue", "../../src/components/nutritionalinfocard.vue", "../../src/components/paymentform.vue", "../../src/components/producerstorycard.vue", "../../src/components/productcard.vue", "../../src/components/productfilters.vue", "../../src/components/searchbar.vue", "../../src/components/thewelcome.vue", "../../src/components/toastcontainer.vue", "../../src/components/welcomeitem.vue", "../../src/components/icons/iconcommunity.vue", "../../src/components/icons/icondocumentation.vue", "../../src/components/icons/iconecosystem.vue", "../../src/components/icons/iconsupport.vue", "../../src/components/icons/icontooling.vue", "../../src/lib/supabase.ts", "../../src/router/index.ts", "../../src/services/addressservice.ts", "../../src/services/adminservice.ts", "../../src/services/nutritionalinfoservice.ts", "../../src/services/orderservice.ts", "../../src/services/paymentservice.ts", "../../src/services/producerstoryservice.ts", "../../src/stores/address.ts", "../../src/stores/admin.ts", "../../src/stores/auth.ts", "../../src/stores/cart.ts", "../../src/stores/nutritionalinfo.ts", "../../src/stores/order.ts", "../../src/stores/producerstories.ts", "../../src/stores/products.ts", "../../src/stores/toast.ts", "../../src/types/index.ts", "../../src/views/cartview.vue", "../../src/views/checkoutview.vue", "../../src/views/homeview.vue", "../../src/views/orderconfirmationview.vue", "../../src/views/ordersview.vue", "../../src/views/producerstoriesview.vue", "../../src/views/producerstorydetailview.vue", "../../src/views/productdetailview.vue", "../../src/views/productsview.vue", "../../src/views/profileview.vue", "../../src/views/admin/admincategoriesview.vue", "../../src/views/admin/admindashboardview.vue", "../../src/views/admin/adminordersview.vue", "../../src/views/admin/adminproductformview.vue", "../../src/views/admin/adminproductsview.vue", "../../src/views/auth/loginview.vue", "../../src/views/auth/registerview.vue", "../../src/views/auth/resetpasswordview.vue"], "errors": true, "version": "5.8.3"}