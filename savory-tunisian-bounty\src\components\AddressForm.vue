<template>
  <form @submit.prevent="handleSubmit" class="space-y-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Address Line 1 -->
      <div class="md:col-span-2">
        <label
          for="address_line_1"
          class="block text-sm font-medium text-gray-700 mb-1"
        >
          Address Line 1 *
        </label>
        <input
          id="address_line_1"
          v-model="form.address_line_1"
          type="text"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
          placeholder="Street address, P.O. box, company name"
        />
      </div>

      <!-- Address Line 2 -->
      <div class="md:col-span-2">
        <label
          for="address_line_2"
          class="block text-sm font-medium text-gray-700 mb-1"
        >
          Address Line 2
        </label>
        <input
          id="address_line_2"
          v-model="form.address_line_2"
          type="text"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
          placeholder="Apartment, suite, unit, building, floor, etc."
        />
      </div>

      <!-- City -->
      <div>
        <label for="city" class="block text-sm font-medium text-gray-700 mb-1">
          City *
        </label>
        <input
          id="city"
          v-model="form.city"
          type="text"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
          placeholder="City"
        />
      </div>

      <!-- Postal Code -->
      <div>
        <label
          for="postal_code"
          class="block text-sm font-medium text-gray-700 mb-1"
        >
          Postal Code *
        </label>
        <input
          id="postal_code"
          v-model="form.postal_code"
          type="text"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
          placeholder="Postal code"
        />
      </div>

      <!-- Country -->
      <div class="md:col-span-2">
        <label
          for="country"
          class="block text-sm font-medium text-gray-700 mb-1"
        >
          Country *
        </label>
        <select
          id="country"
          v-model="form.country"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
        >
          <option value="">Select a country</option>
          <option value="US">United States</option>
          <option value="CA">Canada</option>
          <option value="TN">Tunisia</option>
          <option value="FR">France</option>
          <option value="DE">Germany</option>
          <option value="GB">United Kingdom</option>
          <option value="IT">Italy</option>
          <option value="ES">Spain</option>
          <option value="AU">Australia</option>
          <option value="JP">Japan</option>
        </select>
      </div>

      <!-- Set as Default -->
      <div class="md:col-span-2">
        <label class="flex items-center">
          <input
            v-model="form.is_default"
            type="checkbox"
            class="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
          />
          <span class="ml-2 text-sm text-gray-700">Set as default address</span>
        </label>
      </div>
    </div>

    <!-- Error Messages -->
    <div
      v-if="errors.length > 0"
      class="bg-red-50 border border-red-200 rounded-md p-3"
    >
      <ul class="text-sm text-red-600 space-y-1">
        <li v-for="error in errors" :key="error">{{ error }}</li>
      </ul>
    </div>

    <!-- Form Actions -->
    <div class="flex justify-end space-x-3 pt-4">
      <button
        type="button"
        @click="$emit('cancel')"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-amber-500"
      >
        Cancel
      </button>
      <button
        type="submit"
        :disabled="loading"
        class="px-4 py-2 text-sm font-medium text-white bg-amber-600 border border-transparent rounded-md hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span v-if="loading" class="flex items-center">
          <svg
            class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          Saving...
        </span>
        <span v-else>{{ editMode ? "Update Address" : "Add Address" }}</span>
      </button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { AddressService } from "@/services/addressService";
import type { UserAddress, UserAddressInsert } from "@/types";

interface Props {
  address?: UserAddress | null;
  userId: string;
  editMode?: boolean;
}

interface Emits {
  (e: "submit", address: UserAddress): void;
  (e: "cancel"): void;
}

const props = withDefaults(defineProps<Props>(), {
  address: null,
  editMode: false,
});

const emit = defineEmits<Emits>();

const loading = ref(false);
const errors = ref<string[]>([]);

const form = reactive<Partial<UserAddressInsert>>({
  user_id: props.userId,
  address_line_1: "",
  address_line_2: "",
  city: "",
  postal_code: "",
  country: "",
  is_default: false,
});

// Watch for address prop changes (for edit mode)
watch(
  () => props.address,
  (newAddress) => {
    if (newAddress) {
      form.address_line_1 = newAddress.address_line_1;
      form.address_line_2 = newAddress.address_line_2 || "";
      form.city = newAddress.city;
      form.postal_code = newAddress.postal_code;
      form.country = newAddress.country;
      form.is_default = newAddress.is_default;
    }
  },
  { immediate: true }
);

const handleSubmit = async () => {
  try {
    loading.value = true;
    errors.value = [];

    // Validate form
    const validationErrors = AddressService.validateAddress(form);
    if (validationErrors.length > 0) {
      errors.value = validationErrors;
      return;
    }

    let result: UserAddress | null = null;

    if (props.editMode && props.address) {
      // Update existing address
      result = await AddressService.updateAddress(props.address.id, form);
    } else {
      // Create new address
      result = await AddressService.createAddress(form as UserAddressInsert);
    }

    if (result) {
      emit("submit", result);
    }
  } catch (error) {
    console.error("Error saving address:", error);
    errors.value = ["Failed to save address. Please try again."];
  } finally {
    loading.value = false;
  }
};
</script>
