<template>
  <div class="space-y-4">
    <!-- Existing Addresses -->
    <div v-if="addresses.length > 0" class="space-y-3">
      <h3 class="text-lg font-medium text-gray-900">Select Shipping Address</h3>

      <div class="space-y-3">
        <div
          v-for="address in addresses"
          :key="address.id"
          class="relative border rounded-lg p-4 cursor-pointer transition-colors"
          :class="[
            selectedAddressId === address.id
              ? 'border-amber-500 bg-amber-50'
              : 'border-gray-200 hover:border-gray-300',
          ]"
          @click="selectAddress(address.id)"
        >
          <div class="flex items-start justify-between">
            <div class="flex items-start space-x-3">
              <input
                type="radio"
                :value="address.id"
                :checked="selectedAddressId === address.id"
                class="mt-1 text-amber-600 focus:ring-amber-500"
                @change="selectAddress(address.id)"
              />
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <p class="text-sm font-medium text-gray-900">
                    {{ formatAddress(address) }}
                  </p>
                  <span
                    v-if="address.is_default"
                    class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800"
                  >
                    Default
                  </span>
                </div>
              </div>
            </div>

            <!-- Address Actions -->
            <div class="flex items-center space-x-2">
              <button
                type="button"
                @click.stop="editAddress(address)"
                class="text-sm text-amber-600 hover:text-amber-700"
              >
                Edit
              </button>
              <button
                v-if="!address.is_default"
                type="button"
                @click.stop="deleteAddress(address.id)"
                class="text-sm text-red-600 hover:text-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add New Address -->
    <div class="border-t pt-4">
      <button
        type="button"
        @click="showAddForm = !showAddForm"
        class="flex items-center space-x-2 text-amber-600 hover:text-amber-700"
      >
        <svg
          class="h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          />
        </svg>
        <span>{{
          addresses.length > 0 ? "Add New Address" : "Add Shipping Address"
        }}</span>
      </button>

      <!-- Add Address Form -->
      <div
        v-if="showAddForm"
        class="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50"
      >
        <h4 class="text-md font-medium text-gray-900 mb-4">Add New Address</h4>
        <AddressForm
          :user-id="userId"
          @submit="handleAddressAdded"
          @cancel="showAddForm = false"
        />
      </div>
    </div>

    <!-- Edit Address Modal/Form -->
    <div
      v-if="editingAddress"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div
        class="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-screen overflow-y-auto"
      >
        <h3 class="text-lg font-medium text-gray-900 mb-4">Edit Address</h3>
        <AddressForm
          :address="editingAddress"
          :user-id="userId"
          :edit-mode="true"
          @submit="handleAddressUpdated"
          @cancel="editingAddress = null"
        />
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-3">
      <p class="text-sm text-red-600">{{ error }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useAddressStore } from "@/stores/address";
import AddressForm from "./AddressForm.vue";
import type { UserAddress } from "@/types";

interface Props {
  userId: string;
  modelValue?: string | null;
}

interface Emits {
  (e: "update:modelValue", value: string | null): void;
  (e: "addressSelected", address: UserAddress | null): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const addressStore = useAddressStore();
const showAddForm = ref(false);
const editingAddress = ref<UserAddress | null>(null);
const error = ref("");

const addresses = computed(() => addressStore.addresses);
const loading = computed(() => addressStore.loading);

const selectedAddressId = computed({
  get: () => props.modelValue,
  set: (value: string | null) => emit("update:modelValue", value),
});

onMounted(async () => {
  try {
    await addressStore.fetchAddresses(props.userId);

    // Auto-select default address if no address is selected
    if (!selectedAddressId.value && addressStore.defaultAddress) {
      selectAddress(addressStore.defaultAddress.id);
    }
  } catch (err) {
    error.value = "Failed to load addresses";
  }
});

const selectAddress = (addressId: string) => {
  selectedAddressId.value = addressId;
  const address = addresses.value.find((addr) => addr.id === addressId);
  emit("addressSelected", address || null);
};

const handleAddressAdded = (newAddress: UserAddress) => {
  showAddForm.value = false;

  // Auto-select the new address if it's the first one or if it's set as default
  if (addresses.value.length === 1 || newAddress.is_default) {
    selectAddress(newAddress.id);
  }
};

const handleAddressUpdated = (updatedAddress: UserAddress) => {
  editingAddress.value = null;

  // Keep the address selected if it was already selected
  if (selectedAddressId.value === updatedAddress.id) {
    emit("addressSelected", updatedAddress);
  }
};

const editAddress = (address: UserAddress) => {
  editingAddress.value = address;
};

const deleteAddress = async (addressId: string) => {
  if (confirm("Are you sure you want to delete this address?")) {
    const success = await addressStore.deleteAddress(addressId);

    if (success && selectedAddressId.value === addressId) {
      // Clear selection if the selected address was deleted
      selectedAddressId.value = null;
      emit("addressSelected", null);

      // Auto-select default address if available
      if (addressStore.defaultAddress) {
        selectAddress(addressStore.defaultAddress.id);
      }
    }
  }
};

const formatAddress = (address: UserAddress): string => {
  return addressStore.formatAddress(address);
};
</script>
