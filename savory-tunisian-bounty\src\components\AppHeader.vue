<template>
  <header class="bg-white shadow-sm sticky top-0 z-40">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-2">
            <div class="text-2xl">🌿</div>
            <span class="text-xl font-serif font-bold text-primary-600">
              Savory Tunisian Bounty
            </span>
          </router-link>
        </div>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex items-center space-x-8">
          <router-link
            to="/"
            class="text-gray-700 hover:text-primary-600 font-medium transition-colors"
            active-class="text-primary-600"
          >
            Home
          </router-link>
          <router-link
            to="/products"
            class="text-gray-700 hover:text-primary-600 font-medium transition-colors"
            active-class="text-primary-600"
          >
            Products
          </router-link>
          <router-link
            to="/producer-stories"
            class="text-gray-700 hover:text-primary-600 font-medium transition-colors"
            active-class="text-primary-600"
          >
            Producer Stories
          </router-link>
          <a
            href="#"
            class="text-gray-700 hover:text-primary-600 font-medium transition-colors"
          >
            About
          </a>
          <a
            href="#"
            class="text-gray-700 hover:text-primary-600 font-medium transition-colors"
          >
            Contact
          </a>
        </nav>

        <!-- Right side actions -->
        <div class="flex items-center space-x-4">
          <!-- Search (hidden on mobile) -->
          <div class="hidden lg:block">
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search products..."
                class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                @keyup.enter="handleSearch"
              />
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
              >
                <svg
                  class="h-5 w-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>
          </div>

          <!-- Cart -->
          <router-link
            to="/cart"
            class="relative p-2 text-gray-700 hover:text-primary-600 transition-colors"
          >
            <svg
              class="h-6 w-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0V19a2 2 0 002 2h7a2 2 0 002-2v-1.5"
              />
            </svg>
            <span
              v-if="cartItemCount > 0"
              class="absolute -top-1 -right-1 bg-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
            >
              {{ cartItemCount }}
            </span>
          </router-link>

          <!-- User menu -->
          <div v-if="isAuthenticated" class="relative" ref="userMenuRef">
            <button
              @click="showUserMenu = !showUserMenu"
              class="flex items-center space-x-2 p-2 text-gray-700 hover:text-primary-600 transition-colors"
            >
              <svg
                class="h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
              <svg
                class="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            <!-- User dropdown -->
            <div
              v-if="showUserMenu"
              class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50"
            >
              <router-link
                to="/profile"
                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                @click="showUserMenu = false"
              >
                Profile
              </router-link>
              <router-link
                to="/orders"
                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                @click="showUserMenu = false"
              >
                Order History
              </router-link>
              <router-link
                v-if="authStore.isAdmin"
                to="/admin/dashboard"
                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 border-t border-gray-100"
                @click="showUserMenu = false"
              >
                Admin Dashboard
              </router-link>
              <button
                @click="handleSignOut"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                Sign Out
              </button>
            </div>
          </div>

          <!-- Auth buttons -->
          <div v-else class="flex items-center space-x-2">
            <router-link
              to="/login"
              class="text-gray-700 hover:text-primary-600 font-medium transition-colors"
            >
              Sign In
            </router-link>
            <router-link to="/register" class="btn-primary px-4 py-2">
              Sign Up
            </router-link>
          </div>

          <!-- Mobile menu button -->
          <button
            @click="showMobileMenu = !showMobileMenu"
            class="md:hidden p-2 text-gray-700 hover:text-primary-600 transition-colors"
          >
            <svg
              class="h-6 w-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div
        v-if="showMobileMenu"
        class="md:hidden border-t border-gray-200 py-4"
      >
        <div class="space-y-2">
          <router-link
            to="/"
            class="block px-4 py-2 text-gray-700 hover:text-primary-600 font-medium"
            @click="showMobileMenu = false"
          >
            Home
          </router-link>
          <router-link
            to="/products"
            class="block px-4 py-2 text-gray-700 hover:text-primary-600 font-medium"
            @click="showMobileMenu = false"
          >
            Products
          </router-link>
          <router-link
            to="/producer-stories"
            class="block px-4 py-2 text-gray-700 hover:text-primary-600 font-medium"
            @click="showMobileMenu = false"
          >
            Producer Stories
          </router-link>
          <a
            href="#"
            class="block px-4 py-2 text-gray-700 hover:text-primary-600 font-medium"
          >
            About
          </a>
          <a
            href="#"
            class="block px-4 py-2 text-gray-700 hover:text-primary-600 font-medium"
          >
            Contact
          </a>

          <!-- Mobile search -->
          <div class="px-4 py-2">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search products..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              @keyup.enter="handleSearch"
            />
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";

interface Props {
  isAuthenticated: boolean;
  cartItemCount: number;
}

defineProps<Props>();

const router = useRouter();
const authStore = useAuthStore();

const searchQuery = ref("");
const showUserMenu = ref(false);
const showMobileMenu = ref(false);
const userMenuRef = ref<HTMLElement>();

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({ name: "products", query: { search: searchQuery.value } });
    searchQuery.value = "";
    showMobileMenu.value = false;
  }
};

const handleSignOut = async () => {
  await authStore.signOut();
  showUserMenu.value = false;
  router.push("/");
};

// Close dropdowns when clicking outside
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    showUserMenu.value = false;
  }
};

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>
