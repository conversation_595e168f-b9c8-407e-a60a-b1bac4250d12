<template>
  <div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">
        Nutritional Information
      </h3>
      <div v-if="nutritionalScore > 0" class="flex items-center">
        <span class="text-sm text-gray-600 mr-2">Health Score:</span>
        <div class="flex items-center">
          <div class="w-12 h-2 bg-gray-200 rounded-full mr-2">
            <div
              class="h-2 rounded-full transition-all duration-300"
              :class="getScoreColor(nutritionalScore)"
              :style="{ width: `${nutritionalScore}%` }"
            ></div>
          </div>
          <span
            class="text-sm font-medium"
            :class="getScoreTextColor(nutritionalScore)"
          >
            {{ nutritionalScore }}/100
          </span>
        </div>
      </div>
    </div>

    <div v-if="loading" class="flex justify-center py-8">
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"
      ></div>
    </div>

    <div v-else-if="!nutritionalInfo" class="text-center py-8 text-gray-500">
      <svg
        class="mx-auto h-12 w-12 text-gray-400 mb-4"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        />
      </svg>
      <p>No nutritional information available</p>
    </div>

    <div v-else class="space-y-4">
      <!-- Macronutrients -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div
          v-if="nutritionalInfo.calories_per_100g"
          class="text-center p-3 bg-gray-50 rounded-lg"
        >
          <div class="text-2xl font-bold text-gray-900">
            {{ nutritionalInfo.calories_per_100g }}
          </div>
          <div class="text-sm text-gray-600">Calories</div>
          <div class="text-xs text-gray-500">per 100g</div>
        </div>

        <div
          v-if="nutritionalInfo.protein_per_100g"
          class="text-center p-3 bg-blue-50 rounded-lg"
        >
          <div class="text-2xl font-bold text-blue-900">
            {{ nutritionalInfo.protein_per_100g }}g
          </div>
          <div class="text-sm text-blue-700">Protein</div>
          <div class="text-xs text-blue-600">per 100g</div>
        </div>

        <div
          v-if="nutritionalInfo.carbs_per_100g"
          class="text-center p-3 bg-green-50 rounded-lg"
        >
          <div class="text-2xl font-bold text-green-900">
            {{ nutritionalInfo.carbs_per_100g }}g
          </div>
          <div class="text-sm text-green-700">Carbs</div>
          <div class="text-xs text-green-600">per 100g</div>
        </div>

        <div
          v-if="nutritionalInfo.fat_per_100g"
          class="text-center p-3 bg-yellow-50 rounded-lg"
        >
          <div class="text-2xl font-bold text-yellow-900">
            {{ nutritionalInfo.fat_per_100g }}g
          </div>
          <div class="text-sm text-yellow-700">Fat</div>
          <div class="text-xs text-yellow-600">per 100g</div>
        </div>
      </div>

      <!-- Additional nutrients -->
      <div
        v-if="hasAdditionalNutrients"
        class="grid grid-cols-2 md:grid-cols-3 gap-3 pt-4 border-t"
      >
        <div v-if="nutritionalInfo.fiber_per_100g" class="flex justify-between">
          <span class="text-sm text-gray-600">Fiber:</span>
          <span class="text-sm font-medium"
            >{{ nutritionalInfo.fiber_per_100g }}g</span
          >
        </div>

        <div v-if="nutritionalInfo.sugar_per_100g" class="flex justify-between">
          <span class="text-sm text-gray-600">Sugar:</span>
          <span class="text-sm font-medium"
            >{{ nutritionalInfo.sugar_per_100g }}g</span
          >
        </div>

        <div
          v-if="nutritionalInfo.sodium_per_100g"
          class="flex justify-between"
        >
          <span class="text-sm text-gray-600">Sodium:</span>
          <span class="text-sm font-medium"
            >{{ nutritionalInfo.sodium_per_100g }}mg</span
          >
        </div>
      </div>

      <!-- Vitamins and Minerals -->
      <div v-if="hasVitaminsOrMinerals" class="pt-4 border-t">
        <h4 class="text-sm font-medium text-gray-900 mb-2">
          Vitamins & Minerals
        </h4>
        <div class="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
          <div
            v-for="(value, key) in nutritionalInfo.vitamins"
            :key="`vitamin-${key}`"
            class="flex justify-between"
          >
            <span class="text-gray-600"
              >{{ formatNutrientName(String(key)) }}:</span
            >
            <span class="font-medium"
              >{{ value }}{{ getNutrientUnit(String(key)) }}</span
            >
          </div>
          <div
            v-for="(value, key) in nutritionalInfo.minerals"
            :key="`mineral-${key}`"
            class="flex justify-between"
          >
            <span class="text-gray-600"
              >{{ formatNutrientName(String(key)) }}:</span
            >
            <span class="font-medium"
              >{{ value }}{{ getNutrientUnit(String(key)) }}</span
            >
          </div>
        </div>
      </div>

      <!-- Allergens -->
      <div
        v-if="nutritionalInfo.allergens && nutritionalInfo.allergens.length > 0"
        class="pt-4 border-t"
      >
        <h4 class="text-sm font-medium text-gray-900 mb-2">Allergens</h4>
        <div class="flex flex-wrap gap-2">
          <span
            v-for="allergen in nutritionalInfo.allergens"
            :key="allergen"
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
          >
            {{ allergen }}
          </span>
        </div>
      </div>

      <!-- Dietary Information -->
      <div
        v-if="
          nutritionalInfo.dietary_info &&
          nutritionalInfo.dietary_info.length > 0
        "
        class="pt-4 border-t"
      >
        <h4 class="text-sm font-medium text-gray-900 mb-2">
          Dietary Information
        </h4>
        <div class="flex flex-wrap gap-2">
          <span
            v-for="info in nutritionalInfo.dietary_info"
            :key="info"
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
          >
            {{ formatDietaryInfo(info) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import { useNutritionalInfoStore } from "@/stores/nutritionalInfo";
import type { NutritionalInfo } from "@/types";

interface Props {
  productId: string;
}

const props = defineProps<Props>();

const nutritionalInfoStore = useNutritionalInfoStore();

const loading = computed(() => nutritionalInfoStore.loading);
const nutritionalInfo = computed(() =>
  nutritionalInfoStore.getNutritionalInfoByProductId(props.productId)
);

const nutritionalScore = computed(() =>
  nutritionalInfoStore.calculateNutritionalScore(props.productId)
);

const hasAdditionalNutrients = computed(() => {
  if (!nutritionalInfo.value) return false;
  return !!(
    nutritionalInfo.value.fiber_per_100g ||
    nutritionalInfo.value.sugar_per_100g ||
    nutritionalInfo.value.sodium_per_100g
  );
});

const hasVitaminsOrMinerals = computed(() => {
  if (!nutritionalInfo.value) return false;
  return !!(
    (nutritionalInfo.value.vitamins &&
      Object.keys(nutritionalInfo.value.vitamins).length > 0) ||
    (nutritionalInfo.value.minerals &&
      Object.keys(nutritionalInfo.value.minerals).length > 0)
  );
});

const getScoreColor = (score: number): string => {
  if (score >= 80) return "bg-green-500";
  if (score >= 60) return "bg-yellow-500";
  if (score >= 40) return "bg-orange-500";
  return "bg-red-500";
};

const getScoreTextColor = (score: number): string => {
  if (score >= 80) return "text-green-700";
  if (score >= 60) return "text-yellow-700";
  if (score >= 40) return "text-orange-700";
  return "text-red-700";
};

const formatNutrientName = (name: string): string => {
  return name.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
};

const getNutrientUnit = (nutrient: string): string => {
  const vitaminUnits: Record<string, string> = {
    vitamin_a: "IU",
    vitamin_c: "mg",
    vitamin_d: "IU",
    vitamin_e: "mg",
    vitamin_k: "mcg",
    thiamine: "mg",
    riboflavin: "mg",
    niacin: "mg",
    vitamin_b6: "mg",
    folate: "mcg",
    vitamin_b12: "mcg",
  };

  const mineralUnits: Record<string, string> = {
    calcium: "mg",
    iron: "mg",
    magnesium: "mg",
    phosphorus: "mg",
    potassium: "mg",
    sodium: "mg",
    zinc: "mg",
    copper: "mg",
    selenium: "mcg",
  };

  return vitaminUnits[nutrient] || mineralUnits[nutrient] || "";
};

const formatDietaryInfo = (info: string): string => {
  return info.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
};

onMounted(async () => {
  await nutritionalInfoStore.fetchNutritionalInfo(props.productId);
});
</script>
