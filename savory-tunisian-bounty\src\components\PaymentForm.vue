<template>
  <div class="space-y-6">
    <!-- Payment Method Selection -->
    <div>
      <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Method</h3>
      
      <div class="space-y-3">
        <!-- Credit Card Option -->
        <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
          <input
            v-model="paymentMethod"
            type="radio"
            value="card"
            class="text-amber-600 focus:ring-amber-500"
          />
          <div class="ml-3 flex items-center space-x-2">
            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
            <span class="text-sm font-medium text-gray-900">Credit or Debit Card</span>
          </div>
        </label>

        <!-- PayPal Option (Demo) -->
        <label class="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 opacity-50">
          <input
            type="radio"
            value="paypal"
            disabled
            class="text-amber-600 focus:ring-amber-500"
          />
          <div class="ml-3 flex items-center space-x-2">
            <svg class="h-6 w-6 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.26-.93 4.778-4.005 6.473-7.955 6.473h-2.05c-.262 0-.48.19-.523.45L8.91 20.395a.384.384 0 0 0 .38.442h2.776c.262 0 .48-.19.523-.45l.548-3.467c.043-.26.261-.45.523-.45h1.31c3.5 0 6.24-1.42 7.04-5.53.33-1.71.16-3.14-.82-4.18z"/>
            </svg>
            <span class="text-sm font-medium text-gray-900">PayPal</span>
            <span class="text-xs text-gray-500">(Coming Soon)</span>
          </div>
        </label>
      </div>
    </div>

    <!-- Credit Card Form -->
    <div v-if="paymentMethod === 'card'" class="space-y-4">
      <div class="bg-gray-50 p-4 rounded-lg">
        <p class="text-sm text-gray-600 mb-4">
          <strong>Demo Mode:</strong> Use test card number 4242 4242 4242 4242 with any future expiry date and any 3-digit CVC.
        </p>
        
        <form @submit.prevent="handleSubmit" class="space-y-4">
          <!-- Cardholder Name -->
          <div>
            <label for="cardName" class="block text-sm font-medium text-gray-700 mb-1">
              Cardholder Name *
            </label>
            <input
              id="cardName"
              v-model="cardForm.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              placeholder="John Doe"
            />
          </div>

          <!-- Card Number -->
          <div>
            <label for="cardNumber" class="block text-sm font-medium text-gray-700 mb-1">
              Card Number *
            </label>
            <div class="relative">
              <input
                id="cardNumber"
                v-model="cardForm.cardNumber"
                type="text"
                required
                maxlength="19"
                class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                placeholder="1234 5678 9012 3456"
                @input="formatCardNumber"
              />
              <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                <svg v-if="cardType === 'visa'" class="h-6 w-8" viewBox="0 0 40 24" fill="none">
                  <rect width="40" height="24" rx="4" fill="#1A1F71"/>
                  <path d="M16.5 8.5L14.2 15.5H12.1L10.9 10.4C10.8 10 10.6 9.7 10.3 9.5C9.7 9.1 8.9 8.8 8.1 8.6L8.3 8.5H11.8C12.3 8.5 12.7 8.8 12.8 9.3L13.6 13.1L15.7 8.5H16.5ZM21.2 15.5H19.1L20.7 8.5H22.8L21.2 15.5Z" fill="white"/>
                </svg>
                <svg v-else-if="cardType === 'mastercard'" class="h-6 w-8" viewBox="0 0 40 24" fill="none">
                  <rect width="40" height="24" rx="4" fill="#EB001B"/>
                  <circle cx="15" cy="12" r="7" fill="#FF5F00"/>
                  <circle cx="25" cy="12" r="7" fill="#F79E1B"/>
                </svg>
                <svg v-else class="h-6 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
              </div>
            </div>
          </div>

          <!-- Expiry Date and CVC -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label for="expiryDate" class="block text-sm font-medium text-gray-700 mb-1">
                Expiry Date *
              </label>
              <input
                id="expiryDate"
                v-model="cardForm.expiryDate"
                type="text"
                required
                maxlength="5"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                placeholder="MM/YY"
                @input="formatExpiryDate"
              />
            </div>
            <div>
              <label for="cvc" class="block text-sm font-medium text-gray-700 mb-1">
                CVC *
              </label>
              <input
                id="cvc"
                v-model="cardForm.cvc"
                type="text"
                required
                maxlength="4"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                placeholder="123"
                @input="formatCVC"
              />
            </div>
          </div>

          <!-- Error Messages -->
          <div v-if="errors.length > 0" class="bg-red-50 border border-red-200 rounded-md p-3">
            <ul class="text-sm text-red-600 space-y-1">
              <li v-for="error in errors" :key="error">{{ error }}</li>
            </ul>
          </div>

          <!-- Processing Fee Info -->
          <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
            <p class="text-sm text-blue-700">
              <strong>Total:</strong> {{ formatAmount(amount) }}
              <span v-if="processingFee > 0" class="text-xs text-blue-600">
                (includes {{ formatAmount(processingFee) }} processing fee)
              </span>
            </p>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { PaymentService } from '@/services/paymentService'

interface Props {
  amount: number
  loading?: boolean
}

interface Emits {
  (e: 'submit', paymentData: any): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

const paymentMethod = ref('card')
const errors = ref<string[]>([])

const cardForm = reactive({
  name: '',
  cardNumber: '',
  expiryDate: '',
  cvc: ''
})

const cardType = computed(() => {
  return PaymentService.getCardType(cardForm.cardNumber)
})

const processingFee = computed(() => {
  return PaymentService.calculateProcessingFee(props.amount)
})

const formatAmount = (amount: number): string => {
  return PaymentService.formatAmount(amount)
}

const formatCardNumber = (event: Event) => {
  const target = event.target as HTMLInputElement
  cardForm.cardNumber = PaymentService.formatCardNumber(target.value)
}

const formatExpiryDate = (event: Event) => {
  const target = event.target as HTMLInputElement
  cardForm.expiryDate = PaymentService.formatExpiryDate(target.value)
}

const formatCVC = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value.replace(/\D/g, '')
  cardForm.cvc = value
}

const handleSubmit = () => {
  errors.value = []

  // Validate payment data
  const validationErrors = PaymentService.validatePaymentData(cardForm)
  if (validationErrors.length > 0) {
    errors.value = validationErrors
    return
  }

  // Emit payment data
  emit('submit', {
    method: paymentMethod.value,
    cardData: { ...cardForm }
  })
}

// Expose the submit method for parent component
defineExpose({
  submit: handleSubmit
})
</script>
