<template>
  <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
    <!-- Featured Badge -->
    <div v-if="story.is_featured" class="relative">
      <div class="absolute top-4 left-4 z-10">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
          Featured
        </span>
      </div>
    </div>

    <!-- Image -->
    <div class="aspect-w-16 aspect-h-9 bg-gray-200">
      <img
        v-if="story.featured_image_url"
        :src="story.featured_image_url"
        :alt="story.title"
        class="w-full h-48 object-cover"
      />
      <div v-else class="w-full h-48 bg-gradient-to-br from-amber-100 to-amber-200 flex items-center justify-center">
        <svg class="w-12 h-12 text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      </div>
    </div>

    <!-- Content -->
    <div class="p-6">
      <!-- Producer Info -->
      <div class="flex items-start justify-between mb-3">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ story.producer_name }}</h3>
          <p v-if="story.producer_location" class="text-sm text-gray-600 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            {{ story.producer_location }}
          </p>
        </div>
        <div v-if="story.established_year" class="text-right">
          <div class="text-sm text-gray-500">Est.</div>
          <div class="text-lg font-semibold text-amber-600">{{ story.established_year }}</div>
        </div>
      </div>

      <!-- Story Title -->
      <h4 class="text-xl font-bold text-gray-900 mb-2 line-clamp-2">{{ story.title }}</h4>

      <!-- Story Summary -->
      <p v-if="story.story_summary" class="text-gray-600 text-sm mb-4 line-clamp-3">
        {{ story.story_summary }}
      </p>

      <!-- Specialties -->
      <div v-if="story.specialties && story.specialties.length > 0" class="mb-4">
        <div class="flex flex-wrap gap-1">
          <span
            v-for="specialty in story.specialties.slice(0, 3)"
            :key="specialty"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
          >
            {{ specialty }}
          </span>
          <span
            v-if="story.specialties.length > 3"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600"
          >
            +{{ story.specialties.length - 3 }} more
          </span>
        </div>
      </div>

      <!-- Certifications -->
      <div v-if="story.certifications && story.certifications.length > 0" class="mb-4">
        <div class="flex flex-wrap gap-1">
          <span
            v-for="cert in story.certifications.slice(0, 2)"
            :key="cert"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
          >
            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            {{ formatCertification(cert) }}
          </span>
          <span
            v-if="story.certifications.length > 2"
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600"
          >
            +{{ story.certifications.length - 2 }} more
          </span>
        </div>
      </div>

      <!-- Action Button -->
      <div class="flex items-center justify-between">
        <router-link
          :to="{ name: 'producer-story', params: { slug: story.slug } }"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transition-colors duration-200"
        >
          Read Story
          <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </router-link>

        <!-- Video indicator -->
        <div v-if="story.video_url" class="flex items-center text-sm text-gray-500">
          <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
          </svg>
          Video
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ProducerStory } from '@/types';

interface Props {
  story: ProducerStory;
}

defineProps<Props>();

const formatCertification = (cert: string): string => {
  return cert.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
