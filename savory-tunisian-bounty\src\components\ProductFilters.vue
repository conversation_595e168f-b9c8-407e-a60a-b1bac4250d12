<template>
  <div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
      <button
        @click="clearAllFilters"
        class="text-sm text-amber-600 hover:text-amber-700 font-medium"
        :disabled="!hasActiveFilters"
      >
        Clear All
      </button>
    </div>

    <div class="space-y-6">
      <!-- Category Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2"
          >Category</label
        >
        <select
          v-model="localFilters.category"
          @change="updateFilters"
          class="w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring-amber-500"
        >
          <option value="">All Categories</option>
          <option
            v-for="category in categories"
            :key="category.id"
            :value="category.id"
          >
            {{ category.name }}
          </option>
        </select>
      </div>

      <!-- Origin Region Filter -->
      <div v-if="originRegions.length > 0">
        <label class="block text-sm font-medium text-gray-700 mb-2"
          >Origin Region</label
        >
        <select
          v-model="localFilters.origin_region"
          @change="updateFilters"
          class="w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring-amber-500"
        >
          <option value="">All Regions</option>
          <option v-for="region in originRegions" :key="region" :value="region">
            {{ region }}
          </option>
        </select>
      </div>

      <!-- Price Range Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Price Range: ${{ localFilters.price_min || priceRange.min }} - ${{
            localFilters.price_max || priceRange.max
          }}
        </label>
        <div class="space-y-3">
          <div>
            <label class="block text-xs text-gray-500 mb-1">Min Price</label>
            <input
              type="range"
              v-model.number="localFilters.price_min"
              :min="priceRange.min"
              :max="priceRange.max"
              @input="updateFilters"
              class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>
          <div>
            <label class="block text-xs text-gray-500 mb-1">Max Price</label>
            <input
              type="range"
              v-model.number="localFilters.price_max"
              :min="priceRange.min"
              :max="priceRange.max"
              @input="updateFilters"
              class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>
        </div>
      </div>

      <!-- Dietary Preferences -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3"
          >Dietary Preferences</label
        >
        <div class="space-y-2">
          <label class="flex items-center">
            <input
              type="checkbox"
              v-model="localFilters.is_organic"
              @change="updateFilters"
              class="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
            />
            <span class="ml-2 text-sm text-gray-700">Organic</span>
          </label>

          <label class="flex items-center">
            <input
              type="checkbox"
              v-model="localFilters.is_gluten_free"
              @change="updateFilters"
              class="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
            />
            <span class="ml-2 text-sm text-gray-700">Gluten Free</span>
          </label>

          <label class="flex items-center">
            <input
              type="checkbox"
              v-model="localFilters.is_vegan"
              @change="updateFilters"
              class="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
            />
            <span class="ml-2 text-sm text-gray-700">Vegan</span>
          </label>

          <label class="flex items-center">
            <input
              type="checkbox"
              v-model="localFilters.is_fair_trade"
              @change="updateFilters"
              class="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
            />
            <span class="ml-2 text-sm text-gray-700">Fair Trade</span>
          </label>
        </div>
      </div>

      <!-- Tags Filter -->
      <div v-if="availableTags.length > 0">
        <label class="block text-sm font-medium text-gray-700 mb-3">Tags</label>
        <div class="space-y-2 max-h-32 overflow-y-auto">
          <label
            v-for="tag in availableTags"
            :key="tag"
            class="flex items-center"
          >
            <input
              type="checkbox"
              :value="tag"
              v-model="selectedTags"
              @change="updateTagFilters"
              class="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
            />
            <span class="ml-2 text-sm text-gray-700">{{ formatTag(tag) }}</span>
          </label>
        </div>
      </div>
    </div>

    <!-- Active Filters Summary -->
    <div v-if="hasActiveFilters" class="mt-6 pt-4 border-t">
      <h4 class="text-sm font-medium text-gray-700 mb-2">Active Filters:</h4>
      <div class="flex flex-wrap gap-2">
        <span
          v-for="filter in activeFiltersList"
          :key="filter.key"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800"
        >
          {{ filter.label }}
          <button
            @click="removeFilter(filter.key)"
            class="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full text-amber-400 hover:bg-amber-200 hover:text-amber-600"
          >
            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </button>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { useProductsStore } from "@/stores/products";
import type { ProductFilters } from "@/types";

const productsStore = useProductsStore();

const localFilters = ref<ProductFilters>({});
const selectedTags = ref<string[]>([]);

const categories = computed(() => productsStore.categories);
const originRegions = computed(() => productsStore.getUniqueOriginRegions);
const availableTags = computed(() => productsStore.getUniqueTags);
const priceRange = computed(() => productsStore.getPriceRange);

const hasActiveFilters = computed(() => {
  return Object.keys(localFilters.value).some((key) => {
    const value = localFilters.value[key as keyof ProductFilters];
    if (Array.isArray(value)) return value.length > 0;
    return value !== undefined && value !== null && value !== "";
  });
});

const activeFiltersList = computed(() => {
  const filters: Array<{ key: string; label: string }> = [];

  if (localFilters.value.category) {
    const category = categories.value.find(
      (c) => c.id === localFilters.value.category
    );
    if (category) {
      filters.push({ key: "category", label: `Category: ${category.name}` });
    }
  }

  if (localFilters.value.origin_region) {
    filters.push({
      key: "origin_region",
      label: `Region: ${localFilters.value.origin_region}`,
    });
  }

  if (localFilters.value.price_min !== undefined) {
    filters.push({
      key: "price_min",
      label: `Min: $${localFilters.value.price_min}`,
    });
  }

  if (localFilters.value.price_max !== undefined) {
    filters.push({
      key: "price_max",
      label: `Max: $${localFilters.value.price_max}`,
    });
  }

  if (localFilters.value.is_organic) {
    filters.push({ key: "is_organic", label: "Organic" });
  }

  if (localFilters.value.is_gluten_free) {
    filters.push({ key: "is_gluten_free", label: "Gluten Free" });
  }

  if (localFilters.value.is_vegan) {
    filters.push({ key: "is_vegan", label: "Vegan" });
  }

  if (localFilters.value.is_fair_trade) {
    filters.push({ key: "is_fair_trade", label: "Fair Trade" });
  }

  if (localFilters.value.tags && localFilters.value.tags.length > 0) {
    localFilters.value.tags.forEach((tag) => {
      filters.push({ key: `tag_${tag}`, label: formatTag(tag) });
    });
  }

  return filters;
});

const updateFilters = () => {
  const filters: ProductFilters = { ...localFilters.value };
  if (selectedTags.value.length > 0) {
    filters.tags = [...selectedTags.value];
  } else {
    delete filters.tags;
  }
  productsStore.setFilters(filters);
};

const updateTagFilters = () => {
  localFilters.value.tags = [...selectedTags.value];
  updateFilters();
};

const clearAllFilters = () => {
  localFilters.value = {};
  selectedTags.value = [];
  productsStore.clearFilters();
};

const removeFilter = (key: string) => {
  if (key.startsWith("tag_")) {
    const tag = key.replace("tag_", "");
    selectedTags.value = selectedTags.value.filter((t) => t !== tag);
    updateTagFilters();
  } else {
    delete localFilters.value[key as keyof ProductFilters];
    updateFilters();
  }
};

const formatTag = (tag: string): string => {
  return tag.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
};

// Initialize filters from store
watch(
  () => productsStore.filters,
  (newFilters) => {
    localFilters.value = {
      ...newFilters,
      tags: newFilters.tags ? [...newFilters.tags] : undefined,
    };
    selectedTags.value = newFilters.tags ? [...newFilters.tags] : [];
  },
  { immediate: true }
);

onMounted(() => {
  // Initialize price range
  if (!localFilters.value.price_min) {
    localFilters.value.price_min = priceRange.value.min;
  }
  if (!localFilters.value.price_max) {
    localFilters.value.price_max = priceRange.value.max;
  }
});
</script>

<style scoped>
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #d97706;
  cursor: pointer;
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #d97706;
  cursor: pointer;
  border: none;
}
</style>
