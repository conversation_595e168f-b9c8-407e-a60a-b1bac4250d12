<template>
  <div class="relative">
    <!-- Search Input -->
    <div class="relative">
      <div
        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
      >
        <svg
          class="h-5 w-5 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>
      <input
        v-model="searchQuery"
        @input="handleInput"
        @focus="showSuggestions = true"
        @blur="handleBlur"
        @keydown="handleKeydown"
        type="text"
        :placeholder="placeholder"
        class="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
      />
      <div class="absolute inset-y-0 right-0 flex items-center">
        <button
          v-if="searchQuery"
          @click="clearSearch"
          class="p-2 text-gray-400 hover:text-gray-600"
        >
          <svg
            class="h-5 w-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
        <button
          @click="performSearch"
          class="p-2 text-primary-600 hover:text-primary-700"
        >
          <svg
            class="h-5 w-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Search Suggestions Dropdown -->
    <div
      v-if="
        showSuggestions && (suggestions.length > 0 || recentSearches.length > 0)
      "
      class="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-96 overflow-y-auto"
    >
      <!-- Recent Searches -->
      <div
        v-if="recentSearches.length > 0 && !searchQuery"
        class="p-3 border-b border-gray-100"
      >
        <h4
          class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2"
        >
          Recent Searches
        </h4>
        <div class="space-y-1">
          <button
            v-for="recent in recentSearches.slice(0, 5)"
            :key="recent"
            @click="selectSuggestion(recent)"
            class="flex items-center w-full text-left px-2 py-1 text-sm text-gray-700 hover:bg-gray-50 rounded"
          >
            <svg
              class="h-4 w-4 text-gray-400 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            {{ recent }}
          </button>
        </div>
      </div>

      <!-- Product Suggestions -->
      <div v-if="suggestions.length > 0" class="p-3">
        <h4
          v-if="recentSearches.length > 0 && !searchQuery"
          class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2"
        >
          Suggestions
        </h4>
        <div class="space-y-1">
          <button
            v-for="(suggestion, index) in suggestions.slice(0, 8)"
            :key="suggestion.id"
            @click="selectProduct(suggestion)"
            :class="[
              'flex items-center w-full text-left px-2 py-2 text-sm hover:bg-gray-50 rounded',
              selectedIndex === index ? 'bg-primary-50' : '',
            ]"
          >
            <img
              :src="suggestion.image_url || '/placeholder-product.jpg'"
              :alt="suggestion.name"
              class="h-8 w-8 object-cover rounded mr-3"
              @error="handleImageError"
            />
            <div class="flex-1 min-w-0">
              <p
                class="font-medium text-gray-900 truncate"
                v-html="highlightMatch(suggestion.name)"
              ></p>
              <p class="text-gray-500 text-xs truncate">
                {{ suggestion.description }}
              </p>
            </div>
            <span class="text-primary-600 font-medium"
              >${{ suggestion.price.toFixed(2) }}</span
            >
          </button>
        </div>
      </div>

      <!-- No Results -->
      <div
        v-if="searchQuery && suggestions.length === 0"
        class="p-4 text-center text-gray-500"
      >
        <svg
          class="h-8 w-8 mx-auto mb-2 text-gray-300"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
        <p class="text-sm">No products found for "{{ searchQuery }}"</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useProductsStore } from "@/stores/products";
import type { Product } from "@/types";

interface Props {
  placeholder?: string;
  modelValue?: string;
}

interface Emits {
  (e: "update:modelValue", value: string): void;
  (e: "search", query: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: "Search products...",
  modelValue: "",
});

const emit = defineEmits<Emits>();

const router = useRouter();
const productsStore = useProductsStore();

const searchQuery = ref(props.modelValue);
const showSuggestions = ref(false);
const selectedIndex = ref(-1);
const recentSearches = ref<string[]>([]);

// Debounced suggestions
const suggestions = computed(() => {
  if (!searchQuery.value || searchQuery.value.length < 2) return [];

  const query = searchQuery.value.toLowerCase();
  return productsStore.products
    .filter(
      (product) =>
        product.is_active &&
        (product.name.toLowerCase().includes(query) ||
          product.description?.toLowerCase().includes(query))
    )
    .slice(0, 8);
});

// Watch for external model value changes
watch(
  () => props.modelValue,
  (newValue) => {
    searchQuery.value = newValue;
  }
);

// Watch for search query changes
watch(searchQuery, (newValue) => {
  emit("update:modelValue", newValue);
  selectedIndex.value = -1;
});

const handleInput = () => {
  showSuggestions.value = true;
};

const handleBlur = () => {
  // Delay hiding suggestions to allow for clicks
  setTimeout(() => {
    showSuggestions.value = false;
  }, 200);
};

const handleKeydown = (event: KeyboardEvent) => {
  if (!showSuggestions.value) return;

  switch (event.key) {
    case "ArrowDown":
      event.preventDefault();
      selectedIndex.value = Math.min(
        selectedIndex.value + 1,
        suggestions.value.length - 1
      );
      break;
    case "ArrowUp":
      event.preventDefault();
      selectedIndex.value = Math.max(selectedIndex.value - 1, -1);
      break;
    case "Enter":
      event.preventDefault();
      if (selectedIndex.value >= 0 && suggestions.value[selectedIndex.value]) {
        selectProduct(suggestions.value[selectedIndex.value]);
      } else {
        performSearch();
      }
      break;
    case "Escape":
      showSuggestions.value = false;
      selectedIndex.value = -1;
      break;
  }
};

const performSearch = () => {
  if (searchQuery.value.trim()) {
    addToRecentSearches(searchQuery.value.trim());
    emit("search", searchQuery.value.trim());
    showSuggestions.value = false;
  }
};

const clearSearch = () => {
  searchQuery.value = "";
  emit("update:modelValue", "");
  emit("search", "");
};

const selectSuggestion = (suggestion: string) => {
  searchQuery.value = suggestion;
  performSearch();
};

const selectProduct = (product: any) => {
  showSuggestions.value = false;
  router.push({ name: "product-detail", params: { id: product.id } });
};

const highlightMatch = (text: string) => {
  if (!searchQuery.value) return text;

  const regex = new RegExp(`(${searchQuery.value})`, "gi");
  return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
};

const addToRecentSearches = (query: string) => {
  const searches = recentSearches.value.filter((s) => s !== query);
  searches.unshift(query);
  recentSearches.value = searches.slice(0, 10);
  saveRecentSearches();
};

const loadRecentSearches = () => {
  try {
    const saved = localStorage.getItem("recentSearches");
    if (saved) {
      recentSearches.value = JSON.parse(saved);
    }
  } catch (error) {
    console.error("Error loading recent searches:", error);
  }
};

const saveRecentSearches = () => {
  try {
    localStorage.setItem(
      "recentSearches",
      JSON.stringify(recentSearches.value)
    );
  } catch (error) {
    console.error("Error saving recent searches:", error);
  }
};

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src =
    "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400";
};

onMounted(() => {
  loadRecentSearches();
});
</script>
