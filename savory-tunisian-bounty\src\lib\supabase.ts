import { createClient } from "@supabase/supabase-js";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Missing Supabase environment variables");
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          phone: string | null;
          role: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          phone?: string | null;
          role?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          phone?: string | null;
          role?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      categories: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          image_url: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          image_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          image_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      products: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          price: number;
          stock_quantity: number;
          category_id: string | null;
          image_url: string | null;
          images: string[] | null;
          is_active: boolean;
          origin_region: string | null;
          producer_story_id: string | null;
          is_organic: boolean;
          is_gluten_free: boolean;
          is_vegan: boolean;
          is_fair_trade: boolean;
          weight_grams: number | null;
          tags: string[] | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          price: number;
          stock_quantity?: number;
          category_id?: string | null;
          image_url?: string | null;
          images?: string[] | null;
          is_active?: boolean;
          origin_region?: string | null;
          producer_story_id?: string | null;
          is_organic?: boolean;
          is_gluten_free?: boolean;
          is_vegan?: boolean;
          is_fair_trade?: boolean;
          weight_grams?: number | null;
          tags?: string[] | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          price?: number;
          stock_quantity?: number;
          category_id?: string | null;
          image_url?: string | null;
          images?: string[] | null;
          is_active?: boolean;
          origin_region?: string | null;
          producer_story_id?: string | null;
          is_organic?: boolean;
          is_gluten_free?: boolean;
          is_vegan?: boolean;
          is_fair_trade?: boolean;
          weight_grams?: number | null;
          tags?: string[] | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      user_addresses: {
        Row: {
          id: string;
          user_id: string;
          address_line_1: string;
          address_line_2: string | null;
          city: string;
          postal_code: string;
          country: string;
          is_default: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          address_line_1: string;
          address_line_2?: string | null;
          city: string;
          postal_code: string;
          country?: string;
          is_default?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          address_line_1?: string;
          address_line_2?: string | null;
          city?: string;
          postal_code?: string;
          country?: string;
          is_default?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      orders: {
        Row: {
          id: string;
          user_id: string;
          status: string;
          total_amount: number;
          shipping_address_id: string | null;
          payment_status: string;
          payment_intent_id: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          status?: string;
          total_amount: number;
          shipping_address_id?: string | null;
          payment_status?: string;
          payment_intent_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          status?: string;
          total_amount?: number;
          shipping_address_id?: string | null;
          payment_status?: string;
          payment_intent_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      order_items: {
        Row: {
          id: string;
          order_id: string;
          product_id: string;
          quantity: number;
          unit_price: number;
          total_price: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          order_id: string;
          product_id: string;
          quantity: number;
          unit_price: number;
          total_price: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          order_id?: string;
          product_id?: string;
          quantity?: number;
          unit_price?: number;
          total_price?: number;
          created_at?: string;
        };
      };
      nutritional_info: {
        Row: {
          id: string;
          product_id: string | null;
          calories_per_100g: number | null;
          protein_per_100g: number | null;
          carbs_per_100g: number | null;
          fat_per_100g: number | null;
          fiber_per_100g: number | null;
          sugar_per_100g: number | null;
          sodium_per_100g: number | null;
          vitamins: any | null;
          minerals: any | null;
          allergens: string[] | null;
          dietary_info: string[] | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          product_id?: string | null;
          calories_per_100g?: number | null;
          protein_per_100g?: number | null;
          carbs_per_100g?: number | null;
          fat_per_100g?: number | null;
          fiber_per_100g?: number | null;
          sugar_per_100g?: number | null;
          sodium_per_100g?: number | null;
          vitamins?: any | null;
          minerals?: any | null;
          allergens?: string[] | null;
          dietary_info?: string[] | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          product_id?: string | null;
          calories_per_100g?: number | null;
          protein_per_100g?: number | null;
          carbs_per_100g?: number | null;
          fat_per_100g?: number | null;
          fiber_per_100g?: number | null;
          sugar_per_100g?: number | null;
          sodium_per_100g?: number | null;
          vitamins?: any | null;
          minerals?: any | null;
          allergens?: string[] | null;
          dietary_info?: string[] | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      producer_stories: {
        Row: {
          id: string;
          title: string;
          slug: string;
          producer_name: string;
          producer_location: string | null;
          story_content: string;
          story_summary: string | null;
          featured_image_url: string | null;
          gallery_images: string[] | null;
          video_url: string | null;
          established_year: number | null;
          specialties: string[] | null;
          certifications: string[] | null;
          contact_info: any | null;
          is_featured: boolean;
          is_published: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          title: string;
          slug: string;
          producer_name: string;
          producer_location?: string | null;
          story_content: string;
          story_summary?: string | null;
          featured_image_url?: string | null;
          gallery_images?: string[] | null;
          video_url?: string | null;
          established_year?: number | null;
          specialties?: string[] | null;
          certifications?: string[] | null;
          contact_info?: any | null;
          is_featured?: boolean;
          is_published?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          title?: string;
          slug?: string;
          producer_name?: string;
          producer_location?: string | null;
          story_content?: string;
          story_summary?: string | null;
          featured_image_url?: string | null;
          gallery_images?: string[] | null;
          video_url?: string | null;
          established_year?: number | null;
          specialties?: string[] | null;
          certifications?: string[] | null;
          contact_info?: any | null;
          is_featured?: boolean;
          is_published?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      product_producer_stories: {
        Row: {
          id: string;
          product_id: string | null;
          producer_story_id: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          product_id?: string | null;
          producer_story_id?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          product_id?: string | null;
          producer_story_id?: string | null;
          created_at?: string;
        };
      };
    };
  };
}
