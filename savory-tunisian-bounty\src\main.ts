import "./assets/main.css";

import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";

// Import stores for initialization
import { useAuthStore } from "./stores/auth";
import { useCartStore } from "./stores/cart";
import { useToastStore } from "./stores/toast";
import { useAddressStore } from "./stores/address";
import { useOrderStore } from "./stores/order";
import { useAdminStore } from "./stores/admin";

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.use(router);

// Initialize stores
const authStore = useAuthStore();
const cartStore = useCartStore();
const toastStore = useToastStore();
const addressStore = useAddressStore();
const orderStore = useOrderStore();
const adminStore = useAdminStore();

// Initialize auth and cart
authStore.initialize();
cartStore.initialize();

app.mount("#app");
