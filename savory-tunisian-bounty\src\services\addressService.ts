import { supabase } from "@/lib/supabase";
import type {
  UserAddress,
  UserAddressInsert,
  UserAddressUpdate,
} from "@/types";

export class AddressService {
  /**
   * Get all addresses for a user
   */
  static async getUserAddresses(userId: string): Promise<UserAddress[]> {
    try {
      const { data, error } = await supabase
        .from("user_addresses")
        .select("*")
        .eq("user_id", userId)
        .order("is_default", { ascending: false })
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error("Error fetching user addresses:", error);
      throw error;
    }
  }

  /**
   * Get user's default address
   */
  static async getDefaultAddress(userId: string): Promise<UserAddress | null> {
    try {
      const { data, error } = await supabase
        .from("user_addresses")
        .select("*")
        .eq("user_id", userId)
        .eq("is_default", true)
        .single();

      if (error && error.code !== "PGRST116") throw error;
      return data || null;
    } catch (error) {
      console.error("Error fetching default address:", error);
      throw error;
    }
  }

  /**
   * Create a new address
   */
  static async createAddress(address: UserAddressInsert): Promise<UserAddress> {
    try {
      // If this is set as default, unset other default addresses first
      if (address.is_default) {
        await this.unsetDefaultAddresses(address.user_id);
      }

      const { data, error } = await supabase
        .from("user_addresses")
        .insert(address)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("Error creating address:", error);
      throw error;
    }
  }

  /**
   * Update an existing address
   */
  static async updateAddress(
    id: string,
    updates: UserAddressUpdate
  ): Promise<UserAddress> {
    try {
      // If this is set as default, unset other default addresses first
      if (updates.is_default && updates.user_id) {
        await this.unsetDefaultAddresses(updates.user_id);
      }

      const { data, error } = await supabase
        .from("user_addresses")
        .update(updates)
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("Error updating address:", error);
      throw error;
    }
  }

  /**
   * Delete an address
   */
  static async deleteAddress(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("user_addresses")
        .delete()
        .eq("id", id);

      if (error) throw error;
    } catch (error) {
      console.error("Error deleting address:", error);
      throw error;
    }
  }

  /**
   * Set an address as default
   */
  static async setDefaultAddress(
    id: string,
    userId: string
  ): Promise<UserAddress> {
    try {
      // First unset all default addresses for this user
      await this.unsetDefaultAddresses(userId);

      // Then set this address as default
      const { data, error } = await supabase
        .from("user_addresses")
        .update({ is_default: true })
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("Error setting default address:", error);
      throw error;
    }
  }

  /**
   * Unset all default addresses for a user
   */
  private static async unsetDefaultAddresses(userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("user_addresses")
        .update({ is_default: false })
        .eq("user_id", userId)
        .eq("is_default", true);

      if (error) throw error;
    } catch (error) {
      console.error("Error unsetting default addresses:", error);
      throw error;
    }
  }

  /**
   * Get address by ID
   */
  static async getAddressById(id: string): Promise<UserAddress | null> {
    try {
      const { data, error } = await supabase
        .from("user_addresses")
        .select("*")
        .eq("id", id)
        .single();

      if (error && error.code !== "PGRST116") throw error;
      return data || null;
    } catch (error) {
      console.error("Error fetching address by ID:", error);
      throw error;
    }
  }

  /**
   * Validate address data
   */
  static validateAddress(address: Partial<UserAddressInsert>): string[] {
    const errors: string[] = [];

    if (!address.address_line_1?.trim()) {
      errors.push("Address line 1 is required");
    }

    if (!address.city?.trim()) {
      errors.push("City is required");
    }

    if (!address.postal_code?.trim()) {
      errors.push("Postal code is required");
    }

    if (!address.country?.trim()) {
      errors.push("Country is required");
    }

    return errors;
  }
}
