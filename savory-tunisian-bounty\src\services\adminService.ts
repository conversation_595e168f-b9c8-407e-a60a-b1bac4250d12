import { supabase } from "@/lib/supabase";
import type {
  Product,
  ProductInsert,
  ProductUpdate,
  Category,
  CategoryInsert,
  CategoryUpdate,
  Order,
  OrderUpdate,
} from "@/types";

export class AdminService {
  /**
   * Product Management
   */
  
  // Get all products (including inactive ones for admin)
  static async getAllProducts(): Promise<Product[]> {
    try {
      const { data, error } = await supabase
        .from("products")
        .select(`
          *,
          categories (
            id,
            name,
            description
          )
        `)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return (data || []).map((product) => ({
        ...product,
        images: product.images ? [...product.images] : null,
      })) as Product[];
    } catch (error) {
      console.error("Error fetching all products:", error);
      throw error;
    }
  }

  // Create a new product
  static async createProduct(productData: ProductInsert): Promise<Product> {
    try {
      const { data, error } = await supabase
        .from("products")
        .insert(productData)
        .select(`
          *,
          categories (
            id,
            name,
            description
          )
        `)
        .single();

      if (error) throw error;
      return {
        ...data,
        images: data.images ? [...data.images] : null,
      } as Product;
    } catch (error) {
      console.error("Error creating product:", error);
      throw error;
    }
  }

  // Update a product
  static async updateProduct(id: string, updates: ProductUpdate): Promise<Product> {
    try {
      const { data, error } = await supabase
        .from("products")
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq("id", id)
        .select(`
          *,
          categories (
            id,
            name,
            description
          )
        `)
        .single();

      if (error) throw error;
      return {
        ...data,
        images: data.images ? [...data.images] : null,
      } as Product;
    } catch (error) {
      console.error("Error updating product:", error);
      throw error;
    }
  }

  // Delete a product (soft delete by setting is_active to false)
  static async deleteProduct(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("products")
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq("id", id);

      if (error) throw error;
    } catch (error) {
      console.error("Error deleting product:", error);
      throw error;
    }
  }

  // Restore a product (set is_active to true)
  static async restoreProduct(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("products")
        .update({ is_active: true, updated_at: new Date().toISOString() })
        .eq("id", id);

      if (error) throw error;
    } catch (error) {
      console.error("Error restoring product:", error);
      throw error;
    }
  }

  /**
   * Category Management
   */
  
  // Get all categories
  static async getAllCategories(): Promise<Category[]> {
    try {
      const { data, error } = await supabase
        .from("categories")
        .select("*")
        .order("name");

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error("Error fetching categories:", error);
      throw error;
    }
  }

  // Create a new category
  static async createCategory(categoryData: CategoryInsert): Promise<Category> {
    try {
      const { data, error } = await supabase
        .from("categories")
        .insert(categoryData)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("Error creating category:", error);
      throw error;
    }
  }

  // Update a category
  static async updateCategory(id: string, updates: CategoryUpdate): Promise<Category> {
    try {
      const { data, error } = await supabase
        .from("categories")
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("Error updating category:", error);
      throw error;
    }
  }

  // Delete a category
  static async deleteCategory(id: string): Promise<void> {
    try {
      // Check if category has products
      const { data: products, error: checkError } = await supabase
        .from("products")
        .select("id")
        .eq("category_id", id)
        .eq("is_active", true)
        .limit(1);

      if (checkError) throw checkError;

      if (products && products.length > 0) {
        throw new Error("Cannot delete category with active products");
      }

      const { error } = await supabase
        .from("categories")
        .delete()
        .eq("id", id);

      if (error) throw error;
    } catch (error) {
      console.error("Error deleting category:", error);
      throw error;
    }
  }

  /**
   * Order Management
   */
  
  // Get all orders with pagination and filtering
  static async getAllOrders(options: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  } = {}): Promise<{ orders: Order[]; total: number }> {
    try {
      const { page = 1, limit = 20, status, search } = options;
      const offset = (page - 1) * limit;

      let query = supabase
        .from("orders")
        .select(`
          *,
          profiles!orders_user_id_fkey (
            id,
            email,
            full_name
          ),
          user_addresses!orders_shipping_address_id_fkey (
            id,
            address_line_1,
            address_line_2,
            city,
            postal_code,
            country
          )
        `, { count: 'exact' });

      // Apply filters
      if (status) {
        query = query.eq("status", status);
      }

      if (search) {
        query = query.or(`
          id.ilike.%${search}%,
          profiles.email.ilike.%${search}%,
          profiles.full_name.ilike.%${search}%
        `);
      }

      const { data, error, count } = await query
        .order("created_at", { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw error;

      return {
        orders: data || [],
        total: count || 0,
      };
    } catch (error) {
      console.error("Error fetching orders:", error);
      throw error;
    }
  }

  // Update order status
  static async updateOrderStatus(id: string, status: string): Promise<Order> {
    try {
      const { data, error } = await supabase
        .from("orders")
        .update({ 
          status, 
          updated_at: new Date().toISOString() 
        })
        .eq("id", id)
        .select(`
          *,
          profiles!orders_user_id_fkey (
            id,
            email,
            full_name
          ),
          user_addresses!orders_shipping_address_id_fkey (
            id,
            address_line_1,
            address_line_2,
            city,
            postal_code,
            country
          )
        `)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("Error updating order status:", error);
      throw error;
    }
  }

  /**
   * File Upload (for product images)
   */
  
  // Upload image to Supabase Storage
  static async uploadImage(file: File, bucket: string = 'product-images'): Promise<string> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from(bucket)
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      const { data } = supabase.storage
        .from(bucket)
        .getPublicUrl(filePath);

      return data.publicUrl;
    } catch (error) {
      console.error("Error uploading image:", error);
      throw error;
    }
  }

  // Delete image from Supabase Storage
  static async deleteImage(url: string, bucket: string = 'product-images'): Promise<void> {
    try {
      // Extract file path from URL
      const urlParts = url.split('/');
      const fileName = urlParts[urlParts.length - 1];

      const { error } = await supabase.storage
        .from(bucket)
        .remove([fileName]);

      if (error) throw error;
    } catch (error) {
      console.error("Error deleting image:", error);
      throw error;
    }
  }
}
