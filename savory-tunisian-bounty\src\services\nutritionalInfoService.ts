import { supabase } from "@/lib/supabase";
import type {
  NutritionalInfo,
  NutritionalInfoInsert,
  NutritionalInfoUpdate,
} from "@/types";

export class NutritionalInfoService {
  /**
   * Get nutritional information for a product
   */
  static async getNutritionalInfoByProductId(
    productId: string
  ): Promise<NutritionalInfo | null> {
    try {
      const { data, error } = await supabase
        .from("nutritional_info")
        .select("*")
        .eq("product_id", productId)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          // No nutritional info found
          return null;
        }
        throw error;
      }

      return data as NutritionalInfo;
    } catch (error) {
      console.error("Error fetching nutritional info:", error);
      throw error;
    }
  }

  /**
   * Create nutritional information for a product
   */
  static async createNutritionalInfo(
    nutritionalData: NutritionalInfoInsert
  ): Promise<NutritionalInfo> {
    try {
      const { data, error } = await supabase
        .from("nutritional_info")
        .insert(nutritionalData)
        .select("*")
        .single();

      if (error) throw error;
      return data as NutritionalInfo;
    } catch (error) {
      console.error("Error creating nutritional info:", error);
      throw error;
    }
  }

  /**
   * Update nutritional information
   */
  static async updateNutritionalInfo(
    id: string,
    updates: NutritionalInfoUpdate
  ): Promise<NutritionalInfo> {
    try {
      const { data, error } = await supabase
        .from("nutritional_info")
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq("id", id)
        .select("*")
        .single();

      if (error) throw error;
      return data as NutritionalInfo;
    } catch (error) {
      console.error("Error updating nutritional info:", error);
      throw error;
    }
  }

  /**
   * Delete nutritional information
   */
  static async deleteNutritionalInfo(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("nutritional_info")
        .delete()
        .eq("id", id);

      if (error) throw error;
    } catch (error) {
      console.error("Error deleting nutritional info:", error);
      throw error;
    }
  }

  /**
   * Get all nutritional info (admin only)
   */
  static async getAllNutritionalInfo(): Promise<NutritionalInfo[]> {
    try {
      const { data, error } = await supabase
        .from("nutritional_info")
        .select(`
          *,
          products (
            id,
            name,
            image_url
          )
        `)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data as NutritionalInfo[];
    } catch (error) {
      console.error("Error fetching all nutritional info:", error);
      throw error;
    }
  }

  /**
   * Upsert nutritional information (create or update)
   */
  static async upsertNutritionalInfo(
    productId: string,
    nutritionalData: Omit<NutritionalInfoInsert, "product_id">
  ): Promise<NutritionalInfo> {
    try {
      // Check if nutritional info already exists
      const existing = await this.getNutritionalInfoByProductId(productId);

      if (existing) {
        // Update existing
        return await this.updateNutritionalInfo(existing.id, nutritionalData);
      } else {
        // Create new
        return await this.createNutritionalInfo({
          ...nutritionalData,
          product_id: productId,
        });
      }
    } catch (error) {
      console.error("Error upserting nutritional info:", error);
      throw error;
    }
  }

  /**
   * Calculate nutritional score (simple algorithm for demo)
   */
  static calculateNutritionalScore(nutritionalInfo: NutritionalInfo): number {
    if (!nutritionalInfo) return 0;

    let score = 0;
    const weights = {
      protein: 0.3,
      fiber: 0.25,
      lowSugar: 0.2,
      lowSodium: 0.15,
      lowFat: 0.1,
    };

    // Protein score (higher is better)
    if (nutritionalInfo.protein_per_100g) {
      score += Math.min(nutritionalInfo.protein_per_100g / 20, 1) * weights.protein;
    }

    // Fiber score (higher is better)
    if (nutritionalInfo.fiber_per_100g) {
      score += Math.min(nutritionalInfo.fiber_per_100g / 10, 1) * weights.fiber;
    }

    // Sugar score (lower is better)
    if (nutritionalInfo.sugar_per_100g !== null) {
      score += Math.max(1 - nutritionalInfo.sugar_per_100g / 50, 0) * weights.lowSugar;
    }

    // Sodium score (lower is better)
    if (nutritionalInfo.sodium_per_100g !== null) {
      score += Math.max(1 - nutritionalInfo.sodium_per_100g / 1000, 0) * weights.lowSodium;
    }

    // Fat score (moderate is better)
    if (nutritionalInfo.fat_per_100g !== null) {
      const fatRatio = nutritionalInfo.fat_per_100g / 30;
      score += Math.max(1 - Math.abs(fatRatio - 0.5), 0) * weights.lowFat;
    }

    return Math.round(score * 100);
  }
}
