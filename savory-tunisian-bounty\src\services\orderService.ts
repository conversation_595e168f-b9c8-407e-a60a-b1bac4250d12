import { supabase } from "@/lib/supabase";
import type {
  Order,
  OrderInsert,
  OrderUpdate,
  OrderItem,
  OrderItemInsert,
} from "@/types";
import type { CartItem } from "@/stores/cart";

export interface CreateOrderData {
  userId: string;
  cartItems: CartItem[];
  shippingAddressId: string;
  paymentIntentId?: string;
}

export interface OrderWithItems extends Order {
  order_items: (OrderItem & {
    products: {
      id: string;
      name: string;
      price: number;
      image_url: string | null;
    };
  })[];
  user_addresses: {
    id: string;
    address_line_1: string;
    address_line_2: string | null;
    city: string;
    postal_code: string;
    country: string;
  } | null;
}

export class OrderService {
  /**
   * Create a new order with order items
   */
  static async createOrder(orderData: CreateOrderData): Promise<Order> {
    try {
      const { userId, cartItems, shippingAddressId, paymentIntentId } =
        orderData;

      // Calculate total amount
      const totalAmount = cartItems.reduce(
        (total, item) => total + item.product.price * item.quantity,
        0
      );

      // Create the order
      const orderInsert: OrderInsert = {
        user_id: userId,
        status: "pending",
        total_amount: totalAmount,
        shipping_address_id: shippingAddressId,
        payment_status: paymentIntentId ? "paid" : "pending",
        payment_intent_id: paymentIntentId || null,
      };

      const { data: order, error: orderError } = await supabase
        .from("orders")
        .insert(orderInsert)
        .select()
        .single();

      if (orderError) throw orderError;

      // Create order items
      const orderItems: OrderItemInsert[] = cartItems.map((item) => ({
        order_id: order.id,
        product_id: item.product.id,
        quantity: item.quantity,
        unit_price: item.product.price,
        total_price: item.product.price * item.quantity,
      }));

      const { error: itemsError } = await supabase
        .from("order_items")
        .insert(orderItems);

      if (itemsError) throw itemsError;

      // Update product stock quantities
      await this.updateProductStock(cartItems);

      return order;
    } catch (error) {
      console.error("Error creating order:", error);
      throw error;
    }
  }

  /**
   * Update product stock quantities after order
   */
  private static async updateProductStock(
    cartItems: CartItem[]
  ): Promise<void> {
    try {
      for (const item of cartItems) {
        const newStock = item.product.stock_quantity - item.quantity;

        const { error } = await supabase
          .from("products")
          .update({ stock_quantity: Math.max(0, newStock) })
          .eq("id", item.product.id);

        if (error) {
          console.error(
            `Error updating stock for product ${item.product.id}:`,
            error
          );
          // Continue with other products even if one fails
        }
      }
    } catch (error) {
      console.error("Error updating product stock:", error);
      // Don't throw here as the order was already created
    }
  }

  /**
   * Get order by ID with items and address
   */
  static async getOrderById(orderId: string): Promise<OrderWithItems | null> {
    try {
      const { data, error } = await supabase
        .from("orders")
        .select(
          `
          *,
          order_items (
            *,
            products (
              id,
              name,
              price,
              image_url
            )
          ),
          user_addresses (
            id,
            address_line_1,
            address_line_2,
            city,
            postal_code,
            country
          )
        `
        )
        .eq("id", orderId)
        .single();

      if (error && error.code !== "PGRST116") throw error;
      return (data as OrderWithItems) || null;
    } catch (error) {
      console.error("Error fetching order:", error);
      throw error;
    }
  }

  /**
   * Get orders for a user
   */
  static async getUserOrders(userId: string): Promise<OrderWithItems[]> {
    try {
      const { data, error } = await supabase
        .from("orders")
        .select(
          `
          *,
          order_items (
            *,
            products (
              id,
              name,
              price,
              image_url
            )
          ),
          user_addresses (
            id,
            address_line_1,
            address_line_2,
            city,
            postal_code,
            country
          )
        `
        )
        .eq("user_id", userId)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return (data as OrderWithItems[]) || [];
    } catch (error) {
      console.error("Error fetching user orders:", error);
      throw error;
    }
  }

  /**
   * Update order status
   */
  static async updateOrderStatus(
    orderId: string,
    status: string
  ): Promise<Order> {
    try {
      const { data, error } = await supabase
        .from("orders")
        .update({ status })
        .eq("id", orderId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("Error updating order status:", error);
      throw error;
    }
  }

  /**
   * Update payment status
   */
  static async updatePaymentStatus(
    orderId: string,
    paymentStatus: string,
    paymentIntentId?: string
  ): Promise<Order> {
    try {
      const updates: OrderUpdate = { payment_status: paymentStatus };
      if (paymentIntentId) {
        updates.payment_intent_id = paymentIntentId;
      }

      const { data, error } = await supabase
        .from("orders")
        .update(updates)
        .eq("id", orderId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("Error updating payment status:", error);
      throw error;
    }
  }

  /**
   * Validate cart items before creating order
   */
  static async validateCartItems(cartItems: CartItem[]): Promise<string[]> {
    const errors: string[] = [];

    if (!cartItems.length) {
      errors.push("Cart is empty");
      return errors;
    }

    try {
      // Check stock availability for each item
      for (const item of cartItems) {
        const { data: product, error } = await supabase
          .from("products")
          .select("stock_quantity, is_active")
          .eq("id", item.product.id)
          .single();

        if (error) {
          errors.push(`Product ${item.product.name} not found`);
          continue;
        }

        if (!product.is_active) {
          errors.push(`Product ${item.product.name} is no longer available`);
        }

        if (product.stock_quantity < item.quantity) {
          errors.push(
            `Insufficient stock for ${item.product.name}. Available: ${product.stock_quantity}, Requested: ${item.quantity}`
          );
        }
      }
    } catch (error) {
      console.error("Error validating cart items:", error);
      errors.push("Error validating cart items");
    }

    return errors;
  }
}
