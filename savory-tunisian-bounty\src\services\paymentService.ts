import { loadStripe, type Stripe } from "@stripe/stripe-js";
import type { CartItem } from "@/stores/cart";

// Get Stripe publishable key from environment variables
const STRIPE_PUBLISHABLE_KEY =
  import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || "pk_test_demo_key";

export interface PaymentIntent {
  id: string;
  client_secret: string;
  amount: number;
  currency: string;
  status: string;
}

export class PaymentService {
  private static stripePromise: Promise<Stripe | null> | null = null;

  /**
   * Get Stripe instance
   */
  static getStripe(): Promise<Stripe | null> {
    if (!this.stripePromise) {
      this.stripePromise = loadStripe(STRIPE_PUBLISHABLE_KEY);
    }
    return this.stripePromise;
  }

  /**
   * Create payment intent (this would normally be done on the server)
   * For demo purposes, we'll simulate this
   */
  static async createPaymentIntent(
    amount: number,
    currency: string = "usd",
    cartItems: CartItem[]
  ): Promise<PaymentIntent> {
    try {
      // In a real application, this would be a server-side API call
      // For demo purposes, we'll simulate a payment intent
      const paymentIntent: PaymentIntent = {
        id: `pi_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        client_secret: `pi_${Date.now()}_secret_${Math.random()
          .toString(36)
          .substr(2, 9)}`,
        amount: Math.round(amount * 100), // Stripe expects amount in cents
        currency,
        status: "requires_payment_method",
      };

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      return paymentIntent;
    } catch (error) {
      console.error("Error creating payment intent:", error);
      throw new Error("Failed to create payment intent");
    }
  }

  /**
   * Confirm payment (demo implementation)
   */
  static async confirmPayment(
    clientSecret: string,
    paymentMethodId?: string
  ): Promise<{
    success: boolean;
    paymentIntent?: PaymentIntent;
    error?: string;
  }> {
    try {
      // In a real application, this would use Stripe's confirmPayment
      // For demo purposes, we'll simulate payment confirmation

      // Simulate processing time
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Simulate success (90% success rate for demo)
      const isSuccess = Math.random() > 0.1;

      if (isSuccess) {
        const paymentIntent: PaymentIntent = {
          id: clientSecret.split("_secret_")[0],
          client_secret: clientSecret,
          amount: 0, // Would be filled from actual payment intent
          currency: "usd",
          status: "succeeded",
        };

        return { success: true, paymentIntent };
      } else {
        return {
          success: false,
          error:
            "Your card was declined. Please try a different payment method.",
        };
      }
    } catch (error) {
      console.error("Error confirming payment:", error);
      return {
        success: false,
        error:
          "An error occurred while processing your payment. Please try again.",
      };
    }
  }

  /**
   * Format amount for display
   */
  static formatAmount(amount: number, currency: string = "USD"): string {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase(),
    }).format(amount);
  }

  /**
   * Validate payment data
   */
  static validatePaymentData(data: {
    cardNumber?: string;
    expiryDate?: string;
    cvc?: string;
    name?: string;
  }): string[] {
    const errors: string[] = [];

    if (!data.cardNumber?.trim()) {
      errors.push("Card number is required");
    } else if (!/^\d{13,19}$/.test(data.cardNumber.replace(/\s/g, ""))) {
      errors.push("Invalid card number");
    }

    if (!data.expiryDate?.trim()) {
      errors.push("Expiry date is required");
    } else if (!/^(0[1-9]|1[0-2])\/\d{2}$/.test(data.expiryDate)) {
      errors.push("Invalid expiry date (MM/YY)");
    }

    if (!data.cvc?.trim()) {
      errors.push("CVC is required");
    } else if (!/^\d{3,4}$/.test(data.cvc)) {
      errors.push("Invalid CVC");
    }

    if (!data.name?.trim()) {
      errors.push("Cardholder name is required");
    }

    return errors;
  }

  /**
   * Format card number for display
   */
  static formatCardNumber(cardNumber: string): string {
    const cleaned = cardNumber.replace(/\s/g, "");
    const groups = cleaned.match(/.{1,4}/g) || [];
    return groups.join(" ");
  }

  /**
   * Format expiry date
   */
  static formatExpiryDate(expiryDate: string): string {
    const cleaned = expiryDate.replace(/\D/g, "");
    if (cleaned.length >= 2) {
      return (
        cleaned.substring(0, 2) +
        (cleaned.length > 2 ? "/" + cleaned.substring(2, 4) : "")
      );
    }
    return cleaned;
  }

  /**
   * Get card type from number
   */
  static getCardType(cardNumber: string): string {
    const cleaned = cardNumber.replace(/\s/g, "");

    if (/^4/.test(cleaned)) return "visa";
    if (/^5[1-5]/.test(cleaned)) return "mastercard";
    if (/^3[47]/.test(cleaned)) return "amex";
    if (/^6/.test(cleaned)) return "discover";

    return "unknown";
  }

  /**
   * Calculate processing fee (if applicable)
   */
  static calculateProcessingFee(amount: number): number {
    // Example: 2.9% + $0.30 processing fee
    return Math.round((amount * 0.029 + 0.3) * 100) / 100;
  }
}
