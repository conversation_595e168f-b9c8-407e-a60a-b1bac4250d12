import { supabase } from "@/lib/supabase";
import type {
  ProducerStory,
  ProducerStoryInsert,
  ProducerStoryUpdate,
} from "@/types";

export class ProducerStoryService {
  /**
   * Get all published producer stories
   */
  static async getPublishedStories(): Promise<ProducerStory[]> {
    try {
      const { data, error } = await supabase
        .from("producer_stories")
        .select("*")
        .eq("is_published", true)
        .order("is_featured", { ascending: false })
        .order("created_at", { ascending: false });

      if (error) throw error;
      return (data || []).map((story) => ({
        ...story,
        gallery_images: story.gallery_images ? [...story.gallery_images] : null,
        specialties: story.specialties ? [...story.specialties] : null,
        certifications: story.certifications ? [...story.certifications] : null,
      })) as ProducerStory[];
    } catch (error) {
      console.error("Error fetching published stories:", error);
      throw error;
    }
  }

  /**
   * Get featured producer stories
   */
  static async getFeaturedStories(): Promise<ProducerStory[]> {
    try {
      const { data, error } = await supabase
        .from("producer_stories")
        .select("*")
        .eq("is_published", true)
        .eq("is_featured", true)
        .order("created_at", { ascending: false })
        .limit(6);

      if (error) throw error;
      return (data || []).map((story) => ({
        ...story,
        gallery_images: story.gallery_images ? [...story.gallery_images] : null,
        specialties: story.specialties ? [...story.specialties] : null,
        certifications: story.certifications ? [...story.certifications] : null,
      })) as ProducerStory[];
    } catch (error) {
      console.error("Error fetching featured stories:", error);
      throw error;
    }
  }

  /**
   * Get producer story by slug
   */
  static async getStoryBySlug(slug: string): Promise<ProducerStory | null> {
    try {
      const { data, error } = await supabase
        .from("producer_stories")
        .select("*")
        .eq("slug", slug)
        .eq("is_published", true)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return null;
        }
        throw error;
      }

      return {
        ...data,
        gallery_images: data.gallery_images ? [...data.gallery_images] : null,
        specialties: data.specialties ? [...data.specialties] : null,
        certifications: data.certifications ? [...data.certifications] : null,
      } as ProducerStory;
    } catch (error) {
      console.error("Error fetching story by slug:", error);
      throw error;
    }
  }

  /**
   * Get producer story by ID
   */
  static async getStoryById(id: string): Promise<ProducerStory | null> {
    try {
      const { data, error } = await supabase
        .from("producer_stories")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return null;
        }
        throw error;
      }

      return {
        ...data,
        gallery_images: data.gallery_images ? [...data.gallery_images] : null,
        specialties: data.specialties ? [...data.specialties] : null,
        certifications: data.certifications ? [...data.certifications] : null,
      } as ProducerStory;
    } catch (error) {
      console.error("Error fetching story by ID:", error);
      throw error;
    }
  }

  /**
   * Get all producer stories (admin only)
   */
  static async getAllStories(): Promise<ProducerStory[]> {
    try {
      const { data, error } = await supabase
        .from("producer_stories")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;
      return (data || []).map((story) => ({
        ...story,
        gallery_images: story.gallery_images ? [...story.gallery_images] : null,
        specialties: story.specialties ? [...story.specialties] : null,
        certifications: story.certifications ? [...story.certifications] : null,
      })) as ProducerStory[];
    } catch (error) {
      console.error("Error fetching all stories:", error);
      throw error;
    }
  }

  /**
   * Create a new producer story
   */
  static async createStory(storyData: ProducerStoryInsert): Promise<ProducerStory> {
    try {
      const { data, error } = await supabase
        .from("producer_stories")
        .insert(storyData)
        .select("*")
        .single();

      if (error) throw error;
      return {
        ...data,
        gallery_images: data.gallery_images ? [...data.gallery_images] : null,
        specialties: data.specialties ? [...data.specialties] : null,
        certifications: data.certifications ? [...data.certifications] : null,
      } as ProducerStory;
    } catch (error) {
      console.error("Error creating producer story:", error);
      throw error;
    }
  }

  /**
   * Update a producer story
   */
  static async updateStory(
    id: string,
    updates: ProducerStoryUpdate
  ): Promise<ProducerStory> {
    try {
      const { data, error } = await supabase
        .from("producer_stories")
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq("id", id)
        .select("*")
        .single();

      if (error) throw error;
      return {
        ...data,
        gallery_images: data.gallery_images ? [...data.gallery_images] : null,
        specialties: data.specialties ? [...data.specialties] : null,
        certifications: data.certifications ? [...data.certifications] : null,
      } as ProducerStory;
    } catch (error) {
      console.error("Error updating producer story:", error);
      throw error;
    }
  }

  /**
   * Delete a producer story
   */
  static async deleteStory(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("producer_stories")
        .delete()
        .eq("id", id);

      if (error) throw error;
    } catch (error) {
      console.error("Error deleting producer story:", error);
      throw error;
    }
  }

  /**
   * Generate slug from title
   */
  static generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");
  }

  /**
   * Check if slug is unique
   */
  static async isSlugUnique(slug: string, excludeId?: string): Promise<boolean> {
    try {
      let query = supabase
        .from("producer_stories")
        .select("id")
        .eq("slug", slug);

      if (excludeId) {
        query = query.neq("id", excludeId);
      }

      const { data, error } = await query;

      if (error) throw error;
      return !data || data.length === 0;
    } catch (error) {
      console.error("Error checking slug uniqueness:", error);
      return false;
    }
  }

  /**
   * Get products associated with a producer story
   */
  static async getProductsByStoryId(storyId: string) {
    try {
      const { data, error } = await supabase
        .from("products")
        .select(`
          *,
          categories (
            id,
            name,
            description
          )
        `)
        .eq("producer_story_id", storyId)
        .eq("is_active", true)
        .order("name");

      if (error) throw error;
      return (data || []).map((product) => ({
        ...product,
        images: product.images ? [...product.images] : null,
      }));
    } catch (error) {
      console.error("Error fetching products by story ID:", error);
      throw error;
    }
  }
}
