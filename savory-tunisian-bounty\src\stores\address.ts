import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import { AddressService } from "@/services/addressService";
import { useToastStore } from "./toast";
import type {
  UserAddress,
  UserAddressInsert,
  UserAddressUpdate,
} from "@/types";

export const useAddressStore = defineStore("address", () => {
  const addresses = ref<UserAddress[]>([]);
  const loading = ref(false);
  const defaultAddress = ref<UserAddress | null>(null);

  // Get toast store for notifications
  const getToastStore = () => {
    try {
      return useToastStore();
    } catch {
      return null;
    }
  };

  // Computed properties
  const hasAddresses = computed(() => addresses.value.length > 0);
  const hasDefaultAddress = computed(() => !!defaultAddress.value);

  // Actions
  const fetchAddresses = async (userId: string) => {
    try {
      loading.value = true;
      const fetchedAddresses = await AddressService.getUserAddresses(userId);
      addresses.value = fetchedAddresses;

      // Set default address
      defaultAddress.value =
        fetchedAddresses.find((addr) => addr.is_default) || null;
    } catch (error) {
      console.error("Error fetching addresses:", error);
      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.error("Failed to load addresses");
      }
    } finally {
      loading.value = false;
    }
  };

  const createAddress = async (
    addressData: UserAddressInsert
  ): Promise<UserAddress | null> => {
    try {
      loading.value = true;
      const newAddress = await AddressService.createAddress(addressData);

      // Add to local state
      addresses.value.unshift(newAddress);

      // Update default address if this is the new default
      if (newAddress.is_default) {
        // Unset other addresses as default in local state
        addresses.value.forEach((addr) => {
          if (addr.id !== newAddress.id) {
            addr.is_default = false;
          }
        });
        defaultAddress.value = newAddress;
      }

      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.success("Address added successfully");
      }

      return newAddress;
    } catch (error) {
      console.error("Error creating address:", error);
      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.error("Failed to add address");
      }
      return null;
    } finally {
      loading.value = false;
    }
  };

  const updateAddress = async (
    id: string,
    updates: UserAddressUpdate
  ): Promise<UserAddress | null> => {
    try {
      loading.value = true;
      const updatedAddress = await AddressService.updateAddress(id, updates);

      // Update local state
      const index = addresses.value.findIndex((addr) => addr.id === id);
      if (index !== -1) {
        addresses.value[index] = updatedAddress;
      }

      // Update default address if this is the new default
      if (updatedAddress.is_default) {
        // Unset other addresses as default in local state
        addresses.value.forEach((addr) => {
          if (addr.id !== updatedAddress.id) {
            addr.is_default = false;
          }
        });
        defaultAddress.value = updatedAddress;
      }

      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.success("Address updated successfully");
      }

      return updatedAddress;
    } catch (error) {
      console.error("Error updating address:", error);
      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.error("Failed to update address");
      }
      return null;
    } finally {
      loading.value = false;
    }
  };

  const deleteAddress = async (id: string): Promise<boolean> => {
    try {
      loading.value = true;
      await AddressService.deleteAddress(id);

      // Remove from local state
      const deletedAddress = addresses.value.find((addr) => addr.id === id);
      addresses.value = addresses.value.filter((addr) => addr.id !== id);

      // Update default address if the deleted address was default
      if (deletedAddress?.is_default) {
        defaultAddress.value =
          addresses.value.find((addr) => addr.is_default) || null;
      }

      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.success("Address deleted successfully");
      }

      return true;
    } catch (error) {
      console.error("Error deleting address:", error);
      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.error("Failed to delete address");
      }
      return false;
    } finally {
      loading.value = false;
    }
  };

  const setDefaultAddress = async (
    id: string,
    userId: string
  ): Promise<boolean> => {
    try {
      loading.value = true;
      const updatedAddress = await AddressService.setDefaultAddress(id, userId);

      // Update local state
      addresses.value.forEach((addr) => {
        addr.is_default = addr.id === id;
      });
      defaultAddress.value = updatedAddress;

      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.success("Default address updated");
      }

      return true;
    } catch (error) {
      console.error("Error setting default address:", error);
      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.error("Failed to update default address");
      }
      return false;
    } finally {
      loading.value = false;
    }
  };

  const getAddressById = (id: string): UserAddress | undefined => {
    return addresses.value.find((addr) => addr.id === id);
  };

  const clearAddresses = () => {
    addresses.value = [];
    defaultAddress.value = null;
  };

  const formatAddress = (address: UserAddress): string => {
    const parts = [
      address.address_line_1,
      address.address_line_2,
      address.city,
      address.postal_code,
      address.country,
    ].filter(Boolean);

    return parts.join(", ");
  };

  return {
    addresses: readonly(addresses),
    defaultAddress: readonly(defaultAddress),
    loading: readonly(loading),
    hasAddresses,
    hasDefaultAddress,
    fetchAddresses,
    createAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    getAddressById,
    clearAddresses,
    formatAddress,
  };
});
