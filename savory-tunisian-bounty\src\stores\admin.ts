import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import { AdminService } from "@/services/adminService";
import { useToastStore } from "@/stores/toast";
import type { Product, Category, Order } from "@/types";

export const useAdminStore = defineStore("admin", () => {
  // State
  const products = ref<Product[]>([]);
  const categories = ref<Category[]>([]);
  const orders = ref<Order[]>([]);
  const loading = ref(false);
  const ordersLoading = ref(false);
  const totalOrders = ref(0);
  const currentPage = ref(1);
  const ordersPerPage = ref(20);

  // Computed
  const activeProducts = computed(() => 
    products.value.filter(product => product.is_active)
  );

  const inactiveProducts = computed(() => 
    products.value.filter(product => !product.is_active)
  );

  const orderStats = computed(() => {
    const stats = {
      total: orders.value.length,
      pending: 0,
      confirmed: 0,
      processing: 0,
      shipped: 0,
      delivered: 0,
      cancelled: 0,
    };

    orders.value.forEach(order => {
      if (order.status in stats) {
        stats[order.status as keyof typeof stats]++;
      }
    });

    return stats;
  });

  // Product Actions
  const fetchProducts = async () => {
    try {
      loading.value = true;
      const data = await AdminService.getAllProducts();
      products.value = data;
    } catch (error) {
      console.error("Error fetching products:", error);
      const toastStore = useToastStore();
      toastStore.error("Failed to fetch products");
    } finally {
      loading.value = false;
    }
  };

  const createProduct = async (productData: any): Promise<Product | null> => {
    try {
      loading.value = true;
      const product = await AdminService.createProduct(productData);
      products.value.unshift(product);
      
      const toastStore = useToastStore();
      toastStore.success("Product created successfully");
      return product;
    } catch (error) {
      console.error("Error creating product:", error);
      const toastStore = useToastStore();
      toastStore.error("Failed to create product");
      return null;
    } finally {
      loading.value = false;
    }
  };

  const updateProduct = async (id: string, updates: any): Promise<Product | null> => {
    try {
      loading.value = true;
      const product = await AdminService.updateProduct(id, updates);
      
      const index = products.value.findIndex(p => p.id === id);
      if (index !== -1) {
        products.value[index] = product;
      }
      
      const toastStore = useToastStore();
      toastStore.success("Product updated successfully");
      return product;
    } catch (error) {
      console.error("Error updating product:", error);
      const toastStore = useToastStore();
      toastStore.error("Failed to update product");
      return null;
    } finally {
      loading.value = false;
    }
  };

  const deleteProduct = async (id: string): Promise<boolean> => {
    try {
      loading.value = true;
      await AdminService.deleteProduct(id);
      
      const index = products.value.findIndex(p => p.id === id);
      if (index !== -1) {
        products.value[index].is_active = false;
      }
      
      const toastStore = useToastStore();
      toastStore.success("Product deleted successfully");
      return true;
    } catch (error) {
      console.error("Error deleting product:", error);
      const toastStore = useToastStore();
      toastStore.error("Failed to delete product");
      return false;
    } finally {
      loading.value = false;
    }
  };

  const restoreProduct = async (id: string): Promise<boolean> => {
    try {
      loading.value = true;
      await AdminService.restoreProduct(id);
      
      const index = products.value.findIndex(p => p.id === id);
      if (index !== -1) {
        products.value[index].is_active = true;
      }
      
      const toastStore = useToastStore();
      toastStore.success("Product restored successfully");
      return true;
    } catch (error) {
      console.error("Error restoring product:", error);
      const toastStore = useToastStore();
      toastStore.error("Failed to restore product");
      return false;
    } finally {
      loading.value = false;
    }
  };

  // Category Actions
  const fetchCategories = async () => {
    try {
      const data = await AdminService.getAllCategories();
      categories.value = data;
    } catch (error) {
      console.error("Error fetching categories:", error);
      const toastStore = useToastStore();
      toastStore.error("Failed to fetch categories");
    }
  };

  const createCategory = async (categoryData: any): Promise<Category | null> => {
    try {
      const category = await AdminService.createCategory(categoryData);
      categories.value.push(category);
      
      const toastStore = useToastStore();
      toastStore.success("Category created successfully");
      return category;
    } catch (error) {
      console.error("Error creating category:", error);
      const toastStore = useToastStore();
      toastStore.error("Failed to create category");
      return null;
    }
  };

  const updateCategory = async (id: string, updates: any): Promise<Category | null> => {
    try {
      const category = await AdminService.updateCategory(id, updates);
      
      const index = categories.value.findIndex(c => c.id === id);
      if (index !== -1) {
        categories.value[index] = category;
      }
      
      const toastStore = useToastStore();
      toastStore.success("Category updated successfully");
      return category;
    } catch (error) {
      console.error("Error updating category:", error);
      const toastStore = useToastStore();
      toastStore.error("Failed to update category");
      return null;
    }
  };

  const deleteCategory = async (id: string): Promise<boolean> => {
    try {
      await AdminService.deleteCategory(id);
      
      const index = categories.value.findIndex(c => c.id === id);
      if (index !== -1) {
        categories.value.splice(index, 1);
      }
      
      const toastStore = useToastStore();
      toastStore.success("Category deleted successfully");
      return true;
    } catch (error) {
      console.error("Error deleting category:", error);
      const toastStore = useToastStore();
      toastStore.error(error instanceof Error ? error.message : "Failed to delete category");
      return false;
    }
  };

  // Order Actions
  const fetchOrders = async (options: {
    page?: number;
    status?: string;
    search?: string;
  } = {}) => {
    try {
      ordersLoading.value = true;
      const { page = 1, status, search } = options;
      
      const { orders: data, total } = await AdminService.getAllOrders({
        page,
        limit: ordersPerPage.value,
        status,
        search,
      });
      
      orders.value = data;
      totalOrders.value = total;
      currentPage.value = page;
    } catch (error) {
      console.error("Error fetching orders:", error);
      const toastStore = useToastStore();
      toastStore.error("Failed to fetch orders");
    } finally {
      ordersLoading.value = false;
    }
  };

  const updateOrderStatus = async (id: string, status: string): Promise<boolean> => {
    try {
      const order = await AdminService.updateOrderStatus(id, status);
      
      const index = orders.value.findIndex(o => o.id === id);
      if (index !== -1) {
        orders.value[index] = order;
      }
      
      const toastStore = useToastStore();
      toastStore.success(`Order status updated to ${status}`);
      return true;
    } catch (error) {
      console.error("Error updating order status:", error);
      const toastStore = useToastStore();
      toastStore.error("Failed to update order status");
      return false;
    }
  };

  // File Upload Actions
  const uploadImage = async (file: File): Promise<string | null> => {
    try {
      const url = await AdminService.uploadImage(file);
      return url;
    } catch (error) {
      console.error("Error uploading image:", error);
      const toastStore = useToastStore();
      toastStore.error("Failed to upload image");
      return null;
    }
  };

  const deleteImage = async (url: string): Promise<boolean> => {
    try {
      await AdminService.deleteImage(url);
      return true;
    } catch (error) {
      console.error("Error deleting image:", error);
      const toastStore = useToastStore();
      toastStore.error("Failed to delete image");
      return false;
    }
  };

  // Initialize
  const initialize = async () => {
    await Promise.all([
      fetchProducts(),
      fetchCategories(),
      fetchOrders(),
    ]);
  };

  return {
    // State
    products: readonly(products),
    categories: readonly(categories),
    orders: readonly(orders),
    loading: readonly(loading),
    ordersLoading: readonly(ordersLoading),
    totalOrders: readonly(totalOrders),
    currentPage: readonly(currentPage),
    ordersPerPage: readonly(ordersPerPage),
    
    // Computed
    activeProducts,
    inactiveProducts,
    orderStats,
    
    // Actions
    fetchProducts,
    createProduct,
    updateProduct,
    deleteProduct,
    restoreProduct,
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    fetchOrders,
    updateOrderStatus,
    uploadImage,
    deleteImage,
    initialize,
  };
});
