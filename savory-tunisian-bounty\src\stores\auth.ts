import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import { supabase } from "@/lib/supabase";
import type { User, Session } from "@supabase/supabase-js";
import type { Database } from "@/lib/supabase";

type Profile = Database["public"]["Tables"]["profiles"]["Row"];

export const useAuthStore = defineStore("auth", () => {
  const user = ref<User | null>(null);
  const session = ref<Session | null>(null);
  const profile = ref<Profile | null>(null);
  const loading = ref(true);

  const isAuthenticated = computed(() => !!user.value);
  const isAdmin = computed(() => profile.value?.role === "admin");

  // Initialize auth state
  const initialize = async () => {
    try {
      loading.value = true;

      // Get initial session
      const {
        data: { session: initialSession },
      } = await supabase.auth.getSession();

      if (initialSession) {
        session.value = initialSession;
        user.value = initialSession.user;
        await fetchProfile();
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange(async (event, newSession) => {
        session.value = newSession;
        user.value = newSession?.user ?? null;

        if (event === "SIGNED_IN" && newSession?.user) {
          await fetchProfile();
        } else if (event === "SIGNED_OUT") {
          profile.value = null;
        }
      });
    } catch (error) {
      console.error("Error initializing auth:", error);
    } finally {
      loading.value = false;
    }
  };

  // Fetch user profile
  const fetchProfile = async () => {
    if (!user.value) return;

    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", user.value.id)
        .single();

      if (error) {
        // If profile doesn't exist, create one
        if (error.code === "PGRST116") {
          await createProfile();
        } else {
          throw error;
        }
      } else {
        profile.value = data;
      }
    } catch (error) {
      console.error("Error fetching profile:", error);
    }
  };

  // Create user profile
  const createProfile = async () => {
    if (!user.value) return;

    try {
      const { data, error } = await supabase
        .from("profiles")
        .insert({
          id: user.value.id,
          email: user.value.email!,
          full_name: user.value.user_metadata?.full_name || null,
        })
        .select()
        .single();

      if (error) throw error;
      profile.value = data;
    } catch (error) {
      console.error("Error creating profile:", error);
    }
  };

  // Sign up
  const signUp = async (email: string, password: string, fullName?: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error("Error signing up:", error);
      return { data: null, error };
    }
  };

  // Sign in
  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error("Error signing in:", error);
      return { data: null, error };
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      user.value = null;
      session.value = null;
      profile.value = null;
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  // Update profile
  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user.value) return;

    try {
      const { data, error } = await supabase
        .from("profiles")
        .update(updates)
        .eq("id", user.value.id)
        .select()
        .single();

      if (error) throw error;
      profile.value = data;
      return { data, error: null };
    } catch (error) {
      console.error("Error updating profile:", error);
      return { data: null, error };
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) throw error;
      return { error: null };
    } catch (error) {
      console.error("Error resetting password:", error);
      return { error };
    }
  };

  return {
    user: readonly(user),
    session: readonly(session),
    profile: readonly(profile),
    loading: readonly(loading),
    isAuthenticated,
    isAdmin,
    initialize,
    signUp,
    signIn,
    signOut,
    updateProfile,
    resetPassword,
  };
});
