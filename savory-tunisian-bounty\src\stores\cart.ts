import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import type { Product } from "@/types";
import { useToastStore } from "./toast";

export interface CartItem {
  product: Product;
  quantity: number;
}

export const useCartStore = defineStore("cart", () => {
  const items = ref<CartItem[]>([]);

  // Get toast store for notifications
  const getToastStore = () => {
    try {
      return useToastStore();
    } catch {
      // Return null if toast store is not available (during SSR or initialization)
      return null;
    }
  };

  // Computed properties
  const itemCount = computed(() =>
    items.value.reduce((total, item) => total + item.quantity, 0)
  );

  const totalAmount = computed(() =>
    items.value.reduce(
      (total, item) => total + item.product.price * item.quantity,
      0
    )
  );

  const isEmpty = computed(() => items.value.length === 0);

  // Actions
  const addItem = (product: Product, quantity: number = 1) => {
    const existingItem = items.value.find(
      (item) => item.product.id === product.id
    );

    if (existingItem) {
      existingItem.quantity += quantity;
    } else {
      items.value.push({ product, quantity });
    }

    saveToLocalStorage();

    // Show success notification
    const toastStore = getToastStore();
    if (toastStore) {
      const message = existingItem
        ? `Updated ${product.name} quantity in cart`
        : `Added ${product.name} to cart`;
      toastStore.success(message);
    }
  };

  const removeItem = (productId: string) => {
    const index = items.value.findIndex(
      (item) => item.product.id === productId
    );
    if (index > -1) {
      const removedItem = items.value[index];
      items.value.splice(index, 1);
      saveToLocalStorage();

      // Show notification
      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.info(`Removed ${removedItem.product.name} from cart`);
      }
    }
  };

  const updateQuantity = (productId: string, quantity: number) => {
    const item = items.value.find((item) => item.product.id === productId);
    if (item) {
      if (quantity <= 0) {
        removeItem(productId);
      } else {
        item.quantity = quantity;
        saveToLocalStorage();
      }
    }
  };

  const clearCart = () => {
    const itemCount = items.value.length;
    items.value = [];
    saveToLocalStorage();

    // Show notification
    const toastStore = getToastStore();
    if (toastStore && itemCount > 0) {
      toastStore.info(
        `Cleared ${itemCount} item${itemCount !== 1 ? "s" : ""} from cart`
      );
    }
  };

  const getItemQuantity = (productId: string): number => {
    const item = items.value.find((item) => item.product.id === productId);
    return item ? item.quantity : 0;
  };

  const isInCart = (productId: string): boolean => {
    return items.value.some((item) => item.product.id === productId);
  };

  // Persistence
  const saveToLocalStorage = () => {
    try {
      localStorage.setItem("cart", JSON.stringify(items.value));
    } catch (error) {
      console.error("Error saving cart to localStorage:", error);
    }
  };

  const loadFromLocalStorage = () => {
    try {
      const saved = localStorage.getItem("cart");
      if (saved) {
        items.value = JSON.parse(saved);
      }
    } catch (error) {
      console.error("Error loading cart from localStorage:", error);
      items.value = [];
    }
  };

  // Initialize cart from localStorage
  const initialize = () => {
    loadFromLocalStorage();
  };

  return {
    items: computed(() => [...items.value]), // Return mutable copy
    itemCount,
    totalAmount,
    isEmpty,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    getItemQuantity,
    isInCart,
    initialize,
  };
});
