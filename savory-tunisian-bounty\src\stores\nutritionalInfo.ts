import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import { NutritionalInfoService } from "@/services/nutritionalInfoService";
import { useToastStore } from "./toast";
import type { NutritionalInfo, NutritionalInfoInsert } from "@/types";

export const useNutritionalInfoStore = defineStore("nutritionalInfo", () => {
  const nutritionalInfoMap = ref<Map<string, NutritionalInfo>>(new Map());
  const loading = ref(false);
  const toastStore = useToastStore();

  // Computed properties
  const getNutritionalInfoByProductId = computed(() => {
    return (productId: string) => nutritionalInfoMap.value.get(productId);
  });

  // Actions
  const fetchNutritionalInfo = async (
    productId: string
  ): Promise<NutritionalInfo | null> => {
    try {
      loading.value = true;

      // Check if already cached
      const cached = nutritionalInfoMap.value.get(productId);
      if (cached) {
        return cached;
      }

      const nutritionalInfo =
        await NutritionalInfoService.getNutritionalInfoByProductId(productId);

      if (nutritionalInfo) {
        nutritionalInfoMap.value.set(productId, nutritionalInfo);
      }

      return nutritionalInfo;
    } catch (error) {
      console.error("Error fetching nutritional info:", error);
      toastStore.error("Failed to load nutritional information");
      return null;
    } finally {
      loading.value = false;
    }
  };

  const createNutritionalInfo = async (
    productId: string,
    nutritionalData: Omit<NutritionalInfoInsert, "product_id">
  ): Promise<boolean> => {
    try {
      loading.value = true;

      const nutritionalInfo =
        await NutritionalInfoService.upsertNutritionalInfo(
          productId,
          nutritionalData
        );

      nutritionalInfoMap.value.set(productId, nutritionalInfo);

      toastStore.success("Nutritional information saved successfully");

      return true;
    } catch (error) {
      console.error("Error creating nutritional info:", error);
      toastStore.error("Failed to save nutritional information");
      return false;
    } finally {
      loading.value = false;
    }
  };

  const updateNutritionalInfo = async (
    id: string,
    productId: string,
    updates: Partial<NutritionalInfoInsert>
  ): Promise<boolean> => {
    try {
      loading.value = true;

      const nutritionalInfo =
        await NutritionalInfoService.updateNutritionalInfo(id, updates);

      nutritionalInfoMap.value.set(productId, nutritionalInfo);

      toastStore.success("Nutritional information updated successfully");

      return true;
    } catch (error) {
      console.error("Error updating nutritional info:", error);
      toastStore.error("Failed to update nutritional information");
      return false;
    } finally {
      loading.value = false;
    }
  };

  const deleteNutritionalInfo = async (
    id: string,
    productId: string
  ): Promise<boolean> => {
    try {
      loading.value = true;

      await NutritionalInfoService.deleteNutritionalInfo(id);

      nutritionalInfoMap.value.delete(productId);

      toastStore.success("Nutritional information deleted successfully");

      return true;
    } catch (error) {
      console.error("Error deleting nutritional info:", error);
      toastStore.error("Failed to delete nutritional information");
      return false;
    } finally {
      loading.value = false;
    }
  };

  const calculateNutritionalScore = (productId: string): number => {
    const nutritionalInfo = nutritionalInfoMap.value.get(productId);
    if (!nutritionalInfo) return 0;

    return NutritionalInfoService.calculateNutritionalScore(nutritionalInfo);
  };

  const clearCache = () => {
    nutritionalInfoMap.value.clear();
  };

  return {
    // State
    loading: readonly(loading),

    // Computed
    getNutritionalInfoByProductId,

    // Actions
    fetchNutritionalInfo,
    createNutritionalInfo,
    updateNutritionalInfo,
    deleteNutritionalInfo,
    calculateNutritionalScore,
    clearCache,
  };
});
