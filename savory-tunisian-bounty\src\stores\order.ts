import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import {
  OrderService,
  type CreateOrderData,
  type OrderWithItems,
} from "@/services/orderService";
import { useToastStore } from "./toast";
import type { Order } from "@/types";

export const useOrderStore = defineStore("order", () => {
  const orders = ref<OrderWithItems[]>([]);
  const currentOrder = ref<OrderWithItems | null>(null);
  const loading = ref(false);
  const creating = ref(false);

  // Get toast store for notifications
  const getToastStore = () => {
    try {
      return useToastStore();
    } catch {
      return null;
    }
  };

  // Computed properties
  const hasOrders = computed(() => orders.value.length > 0);
  const recentOrders = computed(() => orders.value.slice(0, 5));
  const orderCount = computed(() => orders.value.length);

  // Actions
  const createOrder = async (
    orderData: CreateOrderData
  ): Promise<Order | null> => {
    try {
      creating.value = true;

      // Validate cart items first
      const validationErrors = await OrderService.validateCartItems(
        orderData.cartItems
      );
      if (validationErrors.length > 0) {
        const toastStore = getToastStore();
        if (toastStore) {
          validationErrors.forEach((error) => toastStore.error(error));
        }
        return null;
      }

      const order = await OrderService.createOrder(orderData);

      // Fetch the complete order with items
      const completeOrder = await OrderService.getOrderById(order.id);
      if (completeOrder) {
        orders.value.unshift(completeOrder);
        currentOrder.value = completeOrder;
      }

      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.success("Order placed successfully!");
      }

      return order;
    } catch (error) {
      console.error("Error creating order:", error);
      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.error("Failed to place order. Please try again.");
      }
      return null;
    } finally {
      creating.value = false;
    }
  };

  const fetchUserOrders = async (userId: string) => {
    try {
      loading.value = true;
      const userOrders = await OrderService.getUserOrders(userId);
      orders.value = userOrders;
    } catch (error) {
      console.error("Error fetching user orders:", error);
      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.error("Failed to load orders");
      }
    } finally {
      loading.value = false;
    }
  };

  const fetchOrderById = async (
    orderId: string
  ): Promise<OrderWithItems | null> => {
    try {
      loading.value = true;
      const order = await OrderService.getOrderById(orderId);

      if (order) {
        currentOrder.value = order;

        // Update in orders list if it exists
        const index = orders.value.findIndex((o) => o.id === orderId);
        if (index !== -1) {
          orders.value[index] = order;
        } else {
          orders.value.unshift(order);
        }
      }

      return order;
    } catch (error) {
      console.error("Error fetching order:", error);
      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.error("Failed to load order details");
      }
      return null;
    } finally {
      loading.value = false;
    }
  };

  const updateOrderStatus = async (
    orderId: string,
    status: string
  ): Promise<boolean> => {
    try {
      const updatedOrder = await OrderService.updateOrderStatus(
        orderId,
        status
      );

      // Update in local state
      const index = orders.value.findIndex((o) => o.id === orderId);
      if (index !== -1) {
        orders.value[index] = { ...orders.value[index], ...updatedOrder };
      }

      if (currentOrder.value?.id === orderId) {
        currentOrder.value = { ...currentOrder.value, ...updatedOrder };
      }

      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.success("Order status updated");
      }

      return true;
    } catch (error) {
      console.error("Error updating order status:", error);
      const toastStore = getToastStore();
      if (toastStore) {
        toastStore.error("Failed to update order status");
      }
      return false;
    }
  };

  const updatePaymentStatus = async (
    orderId: string,
    paymentStatus: string,
    paymentIntentId?: string
  ): Promise<boolean> => {
    try {
      const updatedOrder = await OrderService.updatePaymentStatus(
        orderId,
        paymentStatus,
        paymentIntentId
      );

      // Update in local state
      const index = orders.value.findIndex((o) => o.id === orderId);
      if (index !== -1) {
        orders.value[index] = { ...orders.value[index], ...updatedOrder };
      }

      if (currentOrder.value?.id === orderId) {
        currentOrder.value = { ...currentOrder.value, ...updatedOrder };
      }

      return true;
    } catch (error) {
      console.error("Error updating payment status:", error);
      return false;
    }
  };

  const clearOrders = () => {
    orders.value = [];
    currentOrder.value = null;
  };

  const clearCurrentOrder = () => {
    currentOrder.value = null;
  };

  // Helper functions
  const getOrderStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case "pending":
        return "text-yellow-600 bg-yellow-100";
      case "confirmed":
        return "text-blue-600 bg-blue-100";
      case "processing":
        return "text-purple-600 bg-purple-100";
      case "shipped":
        return "text-indigo-600 bg-indigo-100";
      case "delivered":
        return "text-green-600 bg-green-100";
      case "cancelled":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getPaymentStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case "paid":
        return "text-green-600 bg-green-100";
      case "pending":
        return "text-yellow-600 bg-yellow-100";
      case "failed":
        return "text-red-600 bg-red-100";
      case "refunded":
        return "text-gray-600 bg-gray-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const formatOrderNumber = (order: Order): string => {
    return `#${order.id.slice(-8).toUpperCase()}`;
  };

  const calculateOrderTotal = (order: OrderWithItems): number => {
    return order.order_items.reduce(
      (total, item) => total + item.total_price,
      0
    );
  };

  return {
    orders: computed(() => [...orders.value]), // Return mutable copy
    currentOrder: readonly(currentOrder),
    loading: readonly(loading),
    creating: readonly(creating),
    hasOrders,
    recentOrders,
    orderCount,
    createOrder,
    fetchUserOrders,
    fetchOrderById,
    updateOrderStatus,
    updatePaymentStatus,
    clearOrders,
    clearCurrentOrder,
    getOrderStatusColor,
    getPaymentStatusColor,
    formatOrderNumber,
    calculateOrderTotal,
  };
});
