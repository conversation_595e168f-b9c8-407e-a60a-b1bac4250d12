import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import { ProducerStoryService } from "@/services/producerStoryService";
import { useToastStore } from "./toast";
import type {
  ProducerStory,
  ProducerStoryInsert,
  ProducerStoryUpdate,
} from "@/types";

export const useProducerStoriesStore = defineStore("producerStories", () => {
  const stories = ref<ProducerStory[]>([]);
  const featuredStories = ref<ProducerStory[]>([]);
  const currentStory = ref<ProducerStory | null>(null);
  const loading = ref(false);
  const toastStore = useToastStore();

  // Computed properties
  const publishedStories = computed(() =>
    stories.value.filter((story) => story.is_published)
  );

  const getStoryBySlug = computed(() => {
    return (slug: string) => stories.value.find((story) => story.slug === slug);
  });

  // Actions
  const fetchPublishedStories = async (): Promise<void> => {
    try {
      loading.value = true;
      const data = await ProducerStoryService.getPublishedStories();
      stories.value = data;
    } catch (error) {
      console.error("Error fetching published stories:", error);
      toastStore.error("Failed to load producer stories");
    } finally {
      loading.value = false;
    }
  };

  const fetchFeaturedStories = async (): Promise<void> => {
    try {
      loading.value = true;
      const data = await ProducerStoryService.getFeaturedStories();
      featuredStories.value = data;
    } catch (error) {
      console.error("Error fetching featured stories:", error);
      toastStore.error("Failed to load featured stories");
    } finally {
      loading.value = false;
    }
  };

  const fetchStoryBySlug = async (
    slug: string
  ): Promise<ProducerStory | null> => {
    try {
      loading.value = true;

      // Check if already in store
      const existing = getStoryBySlug.value(slug);
      if (existing) {
        currentStory.value = existing;
        return existing;
      }

      const story = await ProducerStoryService.getStoryBySlug(slug);
      if (story) {
        currentStory.value = story;
        // Add to stories array if not already there
        const existingIndex = stories.value.findIndex((s) => s.id === story.id);
        if (existingIndex === -1) {
          stories.value.push(story);
        }
      }

      return story;
    } catch (error) {
      console.error("Error fetching story by slug:", error);
      toastStore.error("Failed to load producer story");
      return null;
    } finally {
      loading.value = false;
    }
  };

  const fetchAllStories = async (): Promise<void> => {
    try {
      loading.value = true;
      const data = await ProducerStoryService.getAllStories();
      stories.value = data;
    } catch (error) {
      console.error("Error fetching all stories:", error);
      toastStore.error("Failed to load producer stories");
    } finally {
      loading.value = false;
    }
  };

  const createStory = async (
    storyData: ProducerStoryInsert
  ): Promise<boolean> => {
    try {
      loading.value = true;

      const story = await ProducerStoryService.createStory(storyData);
      stories.value.unshift(story);

      toastStore.success("Producer story created successfully");

      return true;
    } catch (error) {
      console.error("Error creating story:", error);
      toastStore.error("Failed to create producer story");
      return false;
    } finally {
      loading.value = false;
    }
  };

  const updateStory = async (
    id: string,
    updates: ProducerStoryUpdate
  ): Promise<boolean> => {
    try {
      loading.value = true;

      const updatedStory = await ProducerStoryService.updateStory(id, updates);

      const index = stories.value.findIndex((story) => story.id === id);
      if (index !== -1) {
        stories.value[index] = updatedStory;
      }

      if (currentStory.value?.id === id) {
        currentStory.value = updatedStory;
      }

      toastStore.success("Producer story updated successfully");

      return true;
    } catch (error) {
      console.error("Error updating story:", error);
      toastStore.error("Failed to update producer story");
      return false;
    } finally {
      loading.value = false;
    }
  };

  const deleteStory = async (id: string): Promise<boolean> => {
    try {
      loading.value = true;

      await ProducerStoryService.deleteStory(id);

      stories.value = stories.value.filter((story) => story.id !== id);
      featuredStories.value = featuredStories.value.filter(
        (story) => story.id !== id
      );

      if (currentStory.value?.id === id) {
        currentStory.value = null;
      }

      toastStore.success("Producer story deleted successfully");

      return true;
    } catch (error) {
      console.error("Error deleting story:", error);
      toastStore.error("Failed to delete producer story");
      return false;
    } finally {
      loading.value = false;
    }
  };

  const generateSlug = (title: string): string => {
    return ProducerStoryService.generateSlug(title);
  };

  const checkSlugUniqueness = async (
    slug: string,
    excludeId?: string
  ): Promise<boolean> => {
    return await ProducerStoryService.isSlugUnique(slug, excludeId);
  };

  const getProductsByStoryId = async (storyId: string) => {
    try {
      return await ProducerStoryService.getProductsByStoryId(storyId);
    } catch (error) {
      console.error("Error fetching products by story ID:", error);
      toastStore.error("Failed to load related products");
      return [];
    }
  };

  const clearCurrentStory = () => {
    currentStory.value = null;
  };

  // Initialize store
  const initialize = async () => {
    await Promise.all([fetchPublishedStories(), fetchFeaturedStories()]);
  };

  return {
    // State
    stories: readonly(stories),
    featuredStories: readonly(featuredStories),
    currentStory: readonly(currentStory),
    loading: readonly(loading),

    // Computed
    publishedStories,
    getStoryBySlug,

    // Actions
    fetchPublishedStories,
    fetchFeaturedStories,
    fetchStoryBySlug,
    fetchAllStories,
    createStory,
    updateStory,
    deleteStory,
    generateSlug,
    checkSlugUniqueness,
    getProductsByStoryId,
    clearCurrentStory,
    initialize,
  };
});
