import type { Database } from "@/lib/supabase";

// Utility type to make readonly arrays mutable
type Mutable<T> = {
  -readonly [P in keyof T]: T[P] extends readonly (infer U)[] ? U[] : T[P];
};

// Export mutable versions of database types
export type Product = Mutable<Database["public"]["Tables"]["products"]["Row"]>;
export type Category = Mutable<
  Database["public"]["Tables"]["categories"]["Row"]
>;
export type Profile = Mutable<Database["public"]["Tables"]["profiles"]["Row"]>;
export type UserAddress = Mutable<
  Database["public"]["Tables"]["user_addresses"]["Row"]
>;
export type Order = Mutable<Database["public"]["Tables"]["orders"]["Row"]>;
export type OrderItem = Mutable<
  Database["public"]["Tables"]["order_items"]["Row"]
>;
export type NutritionalInfo = Mutable<
  Database["public"]["Tables"]["nutritional_info"]["Row"]
>;
export type ProducerStory = Mutable<
  Database["public"]["Tables"]["producer_stories"]["Row"]
>;
export type ProductProducerStory = Mutable<
  Database["public"]["Tables"]["product_producer_stories"]["Row"]
>;

// Insert and Update types (these are already mutable)
export type ProductInsert = Database["public"]["Tables"]["products"]["Insert"];
export type ProductUpdate = Database["public"]["Tables"]["products"]["Update"];
export type CategoryInsert =
  Database["public"]["Tables"]["categories"]["Insert"];
export type CategoryUpdate =
  Database["public"]["Tables"]["categories"]["Update"];
export type ProfileInsert = Database["public"]["Tables"]["profiles"]["Insert"];
export type ProfileUpdate = Database["public"]["Tables"]["profiles"]["Update"];
export type UserAddressInsert =
  Database["public"]["Tables"]["user_addresses"]["Insert"];
export type UserAddressUpdate =
  Database["public"]["Tables"]["user_addresses"]["Update"];
export type OrderInsert = Database["public"]["Tables"]["orders"]["Insert"];
export type OrderUpdate = Database["public"]["Tables"]["orders"]["Update"];
export type OrderItemInsert =
  Database["public"]["Tables"]["order_items"]["Insert"];
export type OrderItemUpdate =
  Database["public"]["Tables"]["order_items"]["Update"];
export type NutritionalInfoInsert =
  Database["public"]["Tables"]["nutritional_info"]["Insert"];
export type NutritionalInfoUpdate =
  Database["public"]["Tables"]["nutritional_info"]["Update"];
export type ProducerStoryInsert =
  Database["public"]["Tables"]["producer_stories"]["Insert"];
export type ProducerStoryUpdate =
  Database["public"]["Tables"]["producer_stories"]["Update"];
export type ProductProducerStoryInsert =
  Database["public"]["Tables"]["product_producer_stories"]["Insert"];
export type ProductProducerStoryUpdate =
  Database["public"]["Tables"]["product_producer_stories"]["Update"];

// Additional types for Sprint 7 features
export interface ProductFilters {
  category?: string | null;
  search?: string;
  origin_region?: string | null;
  is_organic?: boolean;
  is_gluten_free?: boolean;
  is_vegan?: boolean;
  is_fair_trade?: boolean;
  price_min?: number;
  price_max?: number;
  tags?: string[];
}

export interface NutritionalData {
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
  vitamins?: Record<string, number>;
  minerals?: Record<string, number>;
}

// Re-export Database type for direct access when needed
export type { Database };
