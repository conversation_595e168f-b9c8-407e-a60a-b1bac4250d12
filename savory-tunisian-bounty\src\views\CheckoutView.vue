<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-serif font-bold text-gray-900">Checkout</h1>
        <p class="text-gray-600 mt-2">
          Complete your order for authentic Tunisian products
        </p>
      </div>

      <!-- Progress Steps -->
      <div class="mb-8">
        <nav aria-label="Progress">
          <ol class="flex items-center">
            <li v-for="(step, index) in steps" :key="step.id" class="relative">
              <div
                v-if="index !== steps.length - 1"
                class="absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-300"
                aria-hidden="true"
              ></div>
              <div class="relative flex items-start group">
                <span class="h-9 flex items-center">
                  <span
                    class="relative z-10 w-8 h-8 flex items-center justify-center rounded-full text-sm font-semibold"
                    :class="[
                      currentStep >= index + 1
                        ? 'bg-amber-600 text-white'
                        : 'bg-white border-2 border-gray-300 text-gray-500',
                    ]"
                  >
                    <svg
                      v-if="currentStep > index + 1"
                      class="w-5 h-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    <span v-else>{{ index + 1 }}</span>
                  </span>
                </span>
                <span class="ml-4 min-w-0 flex flex-col">
                  <span
                    class="text-sm font-medium"
                    :class="
                      currentStep >= index + 1
                        ? 'text-amber-600'
                        : 'text-gray-500'
                    "
                  >
                    {{ step.name }}
                  </span>
                  <span class="text-sm text-gray-500">{{
                    step.description
                  }}</span>
                </span>
              </div>
            </li>
          </ol>
        </nav>
      </div>

      <!-- Main Content -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Checkout Form -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-lg shadow-sm p-6">
            <!-- Step 1: Shipping Address -->
            <div v-if="currentStep === 1" class="space-y-6">
              <h2 class="text-xl font-semibold text-gray-900">
                Shipping Address
              </h2>

              <AddressSelector
                v-model="selectedAddressId"
                :user-id="authStore.user?.id || ''"
                @address-selected="handleAddressSelected"
              />

              <div class="flex justify-between pt-6">
                <router-link to="/cart" class="btn-outline">
                  Back to Cart
                </router-link>
                <button
                  @click="nextStep"
                  :disabled="!selectedAddressId"
                  class="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Continue to Payment
                </button>
              </div>
            </div>

            <!-- Step 2: Payment -->
            <div v-if="currentStep === 2" class="space-y-6">
              <h2 class="text-xl font-semibold text-gray-900">
                Payment Information
              </h2>

              <PaymentForm
                ref="paymentFormRef"
                :amount="cartStore.totalAmount"
                :loading="processing"
                @submit="handlePaymentSubmit"
              />

              <div class="flex justify-between pt-6">
                <button @click="previousStep" class="btn-outline">
                  Back to Shipping
                </button>
                <button
                  @click="submitPayment"
                  :disabled="processing"
                  class="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="processing" class="flex items-center">
                    <svg
                      class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Processing...
                  </span>
                  <span v-else>Place Order</span>
                </button>
              </div>
            </div>

            <!-- Step 3: Order Review -->
            <div v-if="currentStep === 3" class="space-y-6">
              <h2 class="text-xl font-semibold text-gray-900">
                Review Your Order
              </h2>

              <!-- Order Items -->
              <div class="space-y-4">
                <div
                  v-for="item in cartStore.items"
                  :key="item.product.id"
                  class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg"
                >
                  <img
                    :src="
                      item.product.image_url ||
                      'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=100'
                    "
                    :alt="item.product.name"
                    class="w-16 h-16 object-cover rounded-md"
                  />
                  <div class="flex-1">
                    <h3 class="font-medium text-gray-900">
                      {{ item.product.name }}
                    </h3>
                    <p class="text-sm text-gray-500">
                      Quantity: {{ item.quantity }}
                    </p>
                  </div>
                  <div class="text-right">
                    <p class="font-medium text-gray-900">
                      ${{ (item.product.price * item.quantity).toFixed(2) }}
                    </p>
                    <p class="text-sm text-gray-500">
                      ${{ item.product.price.toFixed(2) }} each
                    </p>
                  </div>
                </div>
              </div>

              <!-- Shipping Address -->
              <div
                v-if="selectedAddress"
                class="p-4 border border-gray-200 rounded-lg"
              >
                <h3 class="font-medium text-gray-900 mb-2">Shipping Address</h3>
                <p class="text-sm text-gray-600">
                  {{ formatAddress(selectedAddress) }}
                </p>
              </div>

              <div class="flex justify-between pt-6">
                <button @click="previousStep" class="btn-outline">
                  Back to Payment
                </button>
                <button
                  @click="confirmOrder"
                  :disabled="processing"
                  class="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="processing" class="flex items-center">
                    <svg
                      class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Placing Order...
                  </span>
                  <span v-else>Confirm Order</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Order Summary -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-sm p-6 sticky top-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
              Order Summary
            </h3>

            <!-- Cart Items -->
            <div class="space-y-3 mb-4">
              <div
                v-for="item in cartStore.items"
                :key="item.product.id"
                class="flex items-center space-x-3"
              >
                <img
                  :src="
                    item.product.image_url ||
                    'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=60'
                  "
                  :alt="item.product.name"
                  class="w-12 h-12 object-cover rounded-md"
                />
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    {{ item.product.name }}
                  </p>
                  <p class="text-sm text-gray-500">Qty: {{ item.quantity }}</p>
                </div>
                <p class="text-sm font-medium text-gray-900">
                  ${{ (item.product.price * item.quantity).toFixed(2) }}
                </p>
              </div>
            </div>

            <!-- Order Totals -->
            <div class="border-t border-gray-200 pt-4 space-y-2">
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Subtotal</span>
                <span class="text-gray-900"
                  >${{ cartStore.totalAmount.toFixed(2) }}</span
                >
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Shipping</span>
                <span class="text-gray-900">Free</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Tax</span>
                <span class="text-gray-900">Calculated at checkout</span>
              </div>
              <div class="border-t border-gray-200 pt-2">
                <div class="flex justify-between">
                  <span class="text-base font-semibold text-gray-900"
                    >Total</span
                  >
                  <span class="text-base font-semibold text-gray-900"
                    >${{ cartStore.totalAmount.toFixed(2) }}</span
                  >
                </div>
              </div>
            </div>

            <!-- Security Notice -->
            <div
              class="mt-6 p-3 bg-green-50 border border-green-200 rounded-md"
            >
              <div class="flex items-center">
                <svg
                  class="h-5 w-5 text-green-600 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  />
                </svg>
                <span class="text-sm text-green-700">Secure checkout</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import { useCartStore } from "@/stores/cart";
import { useOrderStore } from "@/stores/order";
import { useAddressStore } from "@/stores/address";
import { useToastStore } from "@/stores/toast";
import { PaymentService } from "@/services/paymentService";
import AddressSelector from "@/components/AddressSelector.vue";
import PaymentForm from "@/components/PaymentForm.vue";
import type { UserAddress } from "@/types";

const router = useRouter();
const authStore = useAuthStore();
const cartStore = useCartStore();
const orderStore = useOrderStore();
const addressStore = useAddressStore();
const toastStore = useToastStore();

// Component refs
const paymentFormRef = ref<InstanceType<typeof PaymentForm> | null>(null);

// State
const currentStep = ref(1);
const selectedAddressId = ref<string | null>(null);
const selectedAddress = ref<UserAddress | null>(null);
const paymentData = ref<any>(null);
const processing = ref(false);

// Steps configuration
const steps = [
  { id: 1, name: "Shipping", description: "Delivery address" },
  { id: 2, name: "Payment", description: "Payment method" },
  { id: 3, name: "Review", description: "Confirm order" },
];

// Computed
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 1:
      return !!selectedAddressId.value;
    case 2:
      return !!paymentData.value;
    case 3:
      return true;
    default:
      return false;
  }
});

// Lifecycle
onMounted(async () => {
  // Check if user is authenticated
  if (!authStore.isAuthenticated) {
    router.push("/auth/login");
    return;
  }

  // Check if cart has items
  if (cartStore.isEmpty) {
    toastStore.error("Your cart is empty");
    router.push("/cart");
    return;
  }

  // Load user addresses
  if (authStore.user?.id) {
    await addressStore.fetchAddresses(authStore.user.id);

    // Auto-select default address
    if (addressStore.defaultAddress) {
      selectedAddressId.value = addressStore.defaultAddress.id;
      selectedAddress.value = addressStore.defaultAddress;
    }
  }
});

// Methods
const nextStep = () => {
  if (canProceed.value && currentStep.value < steps.length) {
    currentStep.value++;
  }
};

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const handleAddressSelected = (address: UserAddress | null) => {
  selectedAddress.value = address;
};

const handlePaymentSubmit = (data: any) => {
  paymentData.value = data;
};

const submitPayment = () => {
  if (paymentFormRef.value) {
    paymentFormRef.value.submit();
  }
};

const confirmOrder = async () => {
  if (!authStore.user?.id || !selectedAddressId.value || !paymentData.value) {
    toastStore.error("Missing required information");
    return;
  }

  try {
    processing.value = true;

    // Create payment intent
    const paymentIntent = await PaymentService.createPaymentIntent(
      cartStore.totalAmount,
      "usd",
      cartStore.items
    );

    // Confirm payment
    const paymentResult = await PaymentService.confirmPayment(
      paymentIntent.client_secret,
      paymentData.value.cardData
    );

    if (!paymentResult.success) {
      toastStore.error(paymentResult.error || "Payment failed");
      return;
    }

    // Create order
    const order = await orderStore.createOrder({
      userId: authStore.user.id,
      cartItems: cartStore.items,
      shippingAddressId: selectedAddressId.value,
      paymentIntentId: paymentResult.paymentIntent?.id,
    });

    if (order) {
      // Clear cart
      cartStore.clearCart();

      // Redirect to order confirmation
      router.push(`/orders/${order.id}`);
    }
  } catch (error) {
    console.error("Error confirming order:", error);
    toastStore.error("Failed to place order. Please try again.");
  } finally {
    processing.value = false;
  }
};

const formatAddress = (address: UserAddress): string => {
  return addressStore.formatAddress(address);
};
</script>
