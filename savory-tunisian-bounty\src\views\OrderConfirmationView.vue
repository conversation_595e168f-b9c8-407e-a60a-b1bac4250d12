<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading State -->
      <div v-if="loading" class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto"></div>
        <p class="mt-4 text-gray-600">Loading order details...</p>
      </div>

      <!-- Order Not Found -->
      <div v-else-if="!order" class="text-center py-12">
        <div class="text-6xl mb-4">❌</div>
        <h2 class="text-2xl font-semibold text-gray-900 mb-4">Order Not Found</h2>
        <p class="text-gray-600 mb-8">
          We couldn't find the order you're looking for.
        </p>
        <router-link to="/orders" class="btn-primary">
          View All Orders
        </router-link>
      </div>

      <!-- Order Confirmation -->
      <div v-else class="space-y-8">
        <!-- Success Header -->
        <div class="text-center">
          <div class="text-6xl mb-4">✅</div>
          <h1 class="text-3xl font-serif font-bold text-gray-900 mb-2">Order Confirmed!</h1>
          <p class="text-lg text-gray-600">
            Thank you for your order. We'll send you a confirmation email shortly.
          </p>
        </div>

        <!-- Order Details Card -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
          <!-- Order Header -->
          <div class="bg-amber-50 px-6 py-4 border-b border-amber-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h2 class="text-xl font-semibold text-gray-900">
                  Order {{ formatOrderNumber(order) }}
                </h2>
                <p class="text-sm text-gray-600 mt-1">
                  Placed on {{ formatDate(order.created_at) }}
                </p>
              </div>
              <div class="mt-4 sm:mt-0">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                  :class="getOrderStatusColor(order.status)"
                >
                  {{ order.status.charAt(0).toUpperCase() + order.status.slice(1) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Order Content -->
          <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Order Items -->
              <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Order Items</h3>
                <div class="space-y-4">
                  <div
                    v-for="item in order.order_items"
                    :key="item.id"
                    class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg"
                  >
                    <img
                      :src="item.products.image_url || 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=80'"
                      :alt="item.products.name"
                      class="w-16 h-16 object-cover rounded-md"
                    />
                    <div class="flex-1">
                      <h4 class="font-medium text-gray-900">{{ item.products.name }}</h4>
                      <p class="text-sm text-gray-500">Quantity: {{ item.quantity }}</p>
                      <p class="text-sm text-gray-500">${{ item.unit_price.toFixed(2) }} each</p>
                    </div>
                    <div class="text-right">
                      <p class="font-medium text-gray-900">${{ item.total_price.toFixed(2) }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Order Summary & Shipping -->
              <div class="space-y-6">
                <!-- Shipping Address -->
                <div v-if="order.user_addresses">
                  <h3 class="text-lg font-medium text-gray-900 mb-3">Shipping Address</h3>
                  <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
                    <p class="text-sm text-gray-900">{{ formatAddress(order.user_addresses) }}</p>
                  </div>
                </div>

                <!-- Payment Information -->
                <div>
                  <h3 class="text-lg font-medium text-gray-900 mb-3">Payment</h3>
                  <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-600">Payment Status</span>
                      <span
                        class="inline-flex items-center px-2 py-1 rounded text-xs font-medium"
                        :class="getPaymentStatusColor(order.payment_status)"
                      >
                        {{ order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1) }}
                      </span>
                    </div>
                    <div v-if="order.payment_intent_id" class="mt-2">
                      <span class="text-sm text-gray-600">Payment ID: {{ order.payment_intent_id }}</span>
                    </div>
                  </div>
                </div>

                <!-- Order Total -->
                <div>
                  <h3 class="text-lg font-medium text-gray-900 mb-3">Order Summary</h3>
                  <div class="p-4 border border-gray-200 rounded-lg bg-gray-50 space-y-2">
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Subtotal</span>
                      <span class="text-gray-900">${{ calculateSubtotal().toFixed(2) }}</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Shipping</span>
                      <span class="text-gray-900">Free</span>
                    </div>
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-600">Tax</span>
                      <span class="text-gray-900">$0.00</span>
                    </div>
                    <div class="border-t border-gray-300 pt-2">
                      <div class="flex justify-between">
                        <span class="text-base font-semibold text-gray-900">Total</span>
                        <span class="text-base font-semibold text-gray-900">${{ order.total_amount.toFixed(2) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link to="/orders" class="btn-outline text-center">
            View All Orders
          </router-link>
          <router-link to="/products" class="btn-primary text-center">
            Continue Shopping
          </router-link>
        </div>

        <!-- Email Confirmation Notice -->
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div class="flex items-start">
            <svg class="h-5 w-5 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <div>
              <h3 class="text-sm font-medium text-blue-800">Order Confirmation Email</h3>
              <p class="text-sm text-blue-700 mt-1">
                We've sent a confirmation email to {{ authStore.user?.email }}. 
                If you don't see it in your inbox, please check your spam folder.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useOrderStore } from '@/stores/order'
import { useToastStore } from '@/stores/toast'
import type { OrderWithItems } from '@/services/orderService'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const orderStore = useOrderStore()
const toastStore = useToastStore()

const loading = ref(true)
const order = ref<OrderWithItems | null>(null)

const orderId = computed(() => route.params.id as string)

onMounted(async () => {
  try {
    if (!authStore.isAuthenticated) {
      router.push('/auth/login')
      return
    }

    if (!orderId.value) {
      router.push('/orders')
      return
    }

    const fetchedOrder = await orderStore.fetchOrderById(orderId.value)
    if (fetchedOrder) {
      order.value = fetchedOrder
    } else {
      toastStore.error('Order not found')
    }
  } catch (error) {
    console.error('Error loading order:', error)
    toastStore.error('Failed to load order details')
  } finally {
    loading.value = false
  }
})

const formatOrderNumber = (order: OrderWithItems): string => {
  return orderStore.formatOrderNumber(order)
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getOrderStatusColor = (status: string): string => {
  return orderStore.getOrderStatusColor(status)
}

const getPaymentStatusColor = (status: string): string => {
  return orderStore.getPaymentStatusColor(status)
}

const formatAddress = (address: any): string => {
  const parts = [
    address.address_line_1,
    address.address_line_2,
    address.city,
    address.postal_code,
    address.country
  ].filter(Boolean)
  
  return parts.join(', ')
}

const calculateSubtotal = (): number => {
  if (!order.value) return 0
  return order.value.order_items.reduce((total, item) => total + item.total_price, 0)
}
</script>
