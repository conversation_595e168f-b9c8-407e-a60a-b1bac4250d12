<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 class="text-3xl font-serif font-bold text-gray-900 mb-8">
        Order History
      </h1>

      <!-- Loading State -->
      <div v-if="loading" class="text-center py-12">
        <div
          class="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto"
        ></div>
        <p class="mt-4 text-gray-600">Loading your orders...</p>
      </div>

      <!-- No Orders -->
      <div v-else-if="!hasOrders" class="text-center py-12">
        <div class="text-6xl mb-4">📦</div>
        <h2 class="text-2xl font-semibold text-gray-900 mb-4">No Orders Yet</h2>
        <p class="text-gray-600 mb-8">
          You haven't placed any orders yet. Start shopping to see your order
          history here.
        </p>
        <router-link to="/products" class="btn-primary">
          Start Shopping
        </router-link>
      </div>

      <!-- Orders List -->
      <div v-else class="space-y-6">
        <div
          v-for="order in orders"
          :key="order.id"
          class="bg-white rounded-lg shadow-sm overflow-hidden"
        >
          <!-- Order Header -->
          <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <div
              class="flex flex-col sm:flex-row sm:items-center sm:justify-between"
            >
              <div>
                <h3 class="text-lg font-semibold text-gray-900">
                  Order {{ formatOrderNumber(order) }}
                </h3>
                <p class="text-sm text-gray-600 mt-1">
                  Placed on {{ formatDate(order.created_at) }}
                </p>
              </div>
              <div class="mt-4 sm:mt-0 flex items-center space-x-4">
                <span
                  class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                  :class="getOrderStatusColor(order.status)"
                >
                  {{
                    order.status.charAt(0).toUpperCase() + order.status.slice(1)
                  }}
                </span>
                <span class="text-lg font-semibold text-gray-900">
                  ${{ order.total_amount.toFixed(2) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Order Items -->
          <div class="p-6">
            <div
              class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6"
            >
              <div
                v-for="item in order.order_items.slice(0, 3)"
                :key="item.id"
                class="flex items-center space-x-3"
              >
                <img
                  :src="
                    item.products.image_url ||
                    'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=60'
                  "
                  :alt="item.products.name"
                  class="w-12 h-12 object-cover rounded-md"
                />
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    {{ item.products.name }}
                  </p>
                  <p class="text-sm text-gray-500">Qty: {{ item.quantity }}</p>
                </div>
              </div>

              <!-- Show more items indicator -->
              <div
                v-if="order.order_items.length > 3"
                class="flex items-center justify-center text-sm text-gray-500"
              >
                +{{ order.order_items.length - 3 }} more item{{
                  order.order_items.length - 3 > 1 ? "s" : ""
                }}
              </div>
            </div>

            <!-- Order Actions -->
            <div class="flex flex-col sm:flex-row gap-3">
              <router-link
                :to="`/orders/${order.id}`"
                class="btn-outline text-center"
              >
                View Details
              </router-link>

              <button
                v-if="order.status === 'delivered'"
                @click="reorderItems(order)"
                class="btn-primary"
              >
                Reorder
              </button>

              <button
                v-if="
                  order.status === 'pending' || order.status === 'confirmed'
                "
                @click="cancelOrder(order.id)"
                class="text-sm text-red-600 hover:text-red-700 px-4 py-2"
              >
                Cancel Order
              </button>
            </div>
          </div>
        </div>

        <!-- Load More (if needed) -->
        <div v-if="orders.length >= 10" class="text-center">
          <button class="btn-outline">Load More Orders</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import { useOrderStore } from "@/stores/order";
import { useCartStore } from "@/stores/cart";
import { useToastStore } from "@/stores/toast";
import type { OrderWithItems } from "@/services/orderService";

const router = useRouter();
const authStore = useAuthStore();
const orderStore = useOrderStore();
const cartStore = useCartStore();
const toastStore = useToastStore();

const loading = computed(() => orderStore.loading);
const orders = computed(() => orderStore.orders);
const hasOrders = computed(() => orderStore.hasOrders);

onMounted(async () => {
  if (!authStore.isAuthenticated) {
    router.push("/auth/login");
    return;
  }

  if (authStore.user?.id) {
    await orderStore.fetchUserOrders(authStore.user.id);
  }
});

const formatOrderNumber = (order: OrderWithItems): string => {
  return orderStore.formatOrderNumber(order);
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

const getOrderStatusColor = (status: string): string => {
  return orderStore.getOrderStatusColor(status);
};

const reorderItems = async (order: OrderWithItems) => {
  try {
    // Add all items from the order to the cart
    for (const item of order.order_items) {
      // Create a complete product object from the order item
      const product = {
        id: item.products.id,
        name: item.products.name,
        description: null,
        price: item.products.price,
        stock_quantity: 100, // Default stock for reorder
        category_id: null,
        image_url: item.products.image_url,
        images: null,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      cartStore.addItem(product, item.quantity);
    }

    toastStore.success("Items added to cart!");
    router.push("/cart");
  } catch (error) {
    console.error("Error reordering items:", error);
    toastStore.error("Failed to add items to cart");
  }
};

const cancelOrder = async (orderId: string) => {
  if (confirm("Are you sure you want to cancel this order?")) {
    const success = await orderStore.updateOrderStatus(orderId, "cancelled");
    if (success) {
      toastStore.success("Order cancelled successfully");
    }
  }
};
</script>
