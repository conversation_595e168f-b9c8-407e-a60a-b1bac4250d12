<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-amber-600 to-orange-600 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <h1 class="text-4xl font-bold mb-4">Meet Our Producers</h1>
          <p class="text-xl text-amber-100 max-w-3xl mx-auto">
            Discover the passionate artisans and farmers behind our authentic Tunisian specialties. 
            Each product tells a story of tradition, quality, and dedication to excellence.
          </p>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <!-- Featured Stories Section -->
      <div v-if="featuredStories.length > 0" class="mb-16">
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Stories</h2>
          <p class="text-lg text-gray-600">Spotlight on our most inspiring producer partnerships</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <ProducerStoryCard
            v-for="story in featuredStories"
            :key="story.id"
            :story="story"
          />
        </div>
      </div>

      <!-- All Stories Section -->
      <div>
        <div class="flex items-center justify-between mb-8">
          <div>
            <h2 class="text-3xl font-bold text-gray-900 mb-2">All Producer Stories</h2>
            <p class="text-lg text-gray-600">{{ stories.length }} stories of tradition and excellence</p>
          </div>
          
          <!-- Search and Sort -->
          <div class="flex items-center space-x-4">
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search stories..."
                class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            
            <select
              v-model="sortBy"
              class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="name">Producer Name</option>
              <option value="location">Location</option>
            </select>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600"></div>
        </div>

        <!-- Empty State -->
        <div v-else-if="filteredStories.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No stories found</h3>
          <p class="text-gray-600">
            {{ searchQuery ? 'Try adjusting your search terms.' : 'Check back soon for new producer stories.' }}
          </p>
        </div>

        <!-- Stories Grid -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <ProducerStoryCard
            v-for="story in filteredStories"
            :key="story.id"
            :story="story"
          />
        </div>

        <!-- Load More Button (if needed for pagination) -->
        <div v-if="filteredStories.length >= 9" class="text-center mt-12">
          <button
            @click="loadMore"
            :disabled="loadingMore"
            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="loadingMore" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
            {{ loadingMore ? 'Loading...' : 'Load More Stories' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useProducerStoriesStore } from '@/stores/producerStories';
import ProducerStoryCard from '@/components/ProducerStoryCard.vue';

const producerStoriesStore = useProducerStoriesStore();

const searchQuery = ref('');
const sortBy = ref('newest');
const loadingMore = ref(false);

const loading = computed(() => producerStoriesStore.loading);
const stories = computed(() => producerStoriesStore.stories);
const featuredStories = computed(() => producerStoriesStore.featuredStories);

const filteredStories = computed(() => {
  let filtered = stories.value;

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(story =>
      story.title.toLowerCase().includes(query) ||
      story.producer_name.toLowerCase().includes(query) ||
      story.producer_location?.toLowerCase().includes(query) ||
      story.story_summary?.toLowerCase().includes(query) ||
      story.specialties?.some(specialty => specialty.toLowerCase().includes(query))
    );
  }

  // Apply sorting
  switch (sortBy.value) {
    case 'newest':
      filtered = [...filtered].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      break;
    case 'oldest':
      filtered = [...filtered].sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
      break;
    case 'name':
      filtered = [...filtered].sort((a, b) => a.producer_name.localeCompare(b.producer_name));
      break;
    case 'location':
      filtered = [...filtered].sort((a, b) => {
        const locationA = a.producer_location || '';
        const locationB = b.producer_location || '';
        return locationA.localeCompare(locationB);
      });
      break;
  }

  return filtered;
});

const loadMore = async () => {
  loadingMore.value = true;
  // Implement pagination logic here if needed
  setTimeout(() => {
    loadingMore.value = false;
  }, 1000);
};

onMounted(async () => {
  await producerStoriesStore.initialize();
});
</script>
