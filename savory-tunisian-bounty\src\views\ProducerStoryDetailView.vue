<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center min-h-screen">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600"></div>
    </div>

    <!-- Not Found State -->
    <div v-else-if="!story" class="flex flex-col items-center justify-center min-h-screen">
      <svg class="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <h1 class="text-2xl font-bold text-gray-900 mb-2">Story Not Found</h1>
      <p class="text-gray-600 mb-6">The producer story you're looking for doesn't exist.</p>
      <router-link
        to="/producer-stories"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-amber-600 hover:bg-amber-700"
      >
        Back to Stories
      </router-link>
    </div>

    <!-- Story Content -->
    <div v-else>
      <!-- Hero Section -->
      <div class="relative">
        <div class="aspect-w-16 aspect-h-6 bg-gray-200">
          <img
            v-if="story.featured_image_url"
            :src="story.featured_image_url"
            :alt="story.title"
            class="w-full h-96 object-cover"
          />
          <div v-else class="w-full h-96 bg-gradient-to-br from-amber-100 to-amber-200"></div>
        </div>
        
        <!-- Overlay Content -->
        <div class="absolute inset-0 bg-black bg-opacity-40 flex items-end">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12 w-full">
            <div class="text-white">
              <!-- Breadcrumb -->
              <nav class="mb-4">
                <ol class="flex items-center space-x-2 text-sm">
                  <li>
                    <router-link to="/" class="hover:text-amber-200">Home</router-link>
                  </li>
                  <li class="text-amber-200">/</li>
                  <li>
                    <router-link to="/producer-stories" class="hover:text-amber-200">Producer Stories</router-link>
                  </li>
                  <li class="text-amber-200">/</li>
                  <li class="text-amber-100">{{ story.producer_name }}</li>
                </ol>
              </nav>
              
              <h1 class="text-4xl md:text-5xl font-bold mb-4">{{ story.title }}</h1>
              <div class="flex items-center space-x-6 text-lg">
                <div class="flex items-center">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  {{ story.producer_location }}
                </div>
                <div v-if="story.established_year" class="flex items-center">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  Established {{ story.established_year }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
          <!-- Story Content -->
          <div class="lg:col-span-2">
            <!-- Summary -->
            <div v-if="story.story_summary" class="mb-8">
              <p class="text-xl text-gray-700 leading-relaxed">{{ story.story_summary }}</p>
            </div>

            <!-- Main Story -->
            <div class="prose prose-lg max-w-none">
              <div v-html="formatStoryContent(story.story_content)"></div>
            </div>

            <!-- Video Section -->
            <div v-if="story.video_url" class="mt-12">
              <h3 class="text-2xl font-bold text-gray-900 mb-6">Watch Their Story</h3>
              <div class="aspect-w-16 aspect-h-9">
                <iframe
                  :src="getEmbedUrl(story.video_url)"
                  frameborder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowfullscreen
                  class="w-full h-96 rounded-lg"
                ></iframe>
              </div>
            </div>

            <!-- Gallery -->
            <div v-if="story.gallery_images && story.gallery_images.length > 0" class="mt-12">
              <h3 class="text-2xl font-bold text-gray-900 mb-6">Gallery</h3>
              <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div
                  v-for="(image, index) in story.gallery_images"
                  :key="index"
                  class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden cursor-pointer hover:opacity-75 transition-opacity"
                  @click="openGallery(index)"
                >
                  <img :src="image" :alt="`Gallery image ${index + 1}`" class="w-full h-full object-cover" />
                </div>
              </div>
            </div>
          </div>

          <!-- Sidebar -->
          <div class="lg:col-span-1">
            <div class="sticky top-8 space-y-8">
              <!-- Producer Info Card -->
              <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">About {{ story.producer_name }}</h3>
                
                <!-- Specialties -->
                <div v-if="story.specialties && story.specialties.length > 0" class="mb-4">
                  <h4 class="text-sm font-medium text-gray-700 mb-2">Specialties</h4>
                  <div class="flex flex-wrap gap-2">
                    <span
                      v-for="specialty in story.specialties"
                      :key="specialty"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                    >
                      {{ specialty }}
                    </span>
                  </div>
                </div>

                <!-- Certifications -->
                <div v-if="story.certifications && story.certifications.length > 0" class="mb-4">
                  <h4 class="text-sm font-medium text-gray-700 mb-2">Certifications</h4>
                  <div class="flex flex-wrap gap-2">
                    <span
                      v-for="cert in story.certifications"
                      :key="cert"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                      {{ formatCertification(cert) }}
                    </span>
                  </div>
                </div>

                <!-- Contact Info -->
                <div v-if="story.contact_info" class="pt-4 border-t">
                  <h4 class="text-sm font-medium text-gray-700 mb-2">Contact</h4>
                  <div class="space-y-2 text-sm text-gray-600">
                    <div v-if="story.contact_info.email" class="flex items-center">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      <a :href="`mailto:${story.contact_info.email}`" class="hover:text-amber-600">
                        {{ story.contact_info.email }}
                      </a>
                    </div>
                    <div v-if="story.contact_info.phone" class="flex items-center">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                      {{ story.contact_info.phone }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Related Products -->
              <div v-if="relatedProducts.length > 0" class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Products from {{ story.producer_name }}</h3>
                <div class="space-y-4">
                  <div
                    v-for="product in relatedProducts.slice(0, 3)"
                    :key="product.id"
                    class="flex items-center space-x-3"
                  >
                    <img
                      :src="product.image_url || '/placeholder-product.jpg'"
                      :alt="product.name"
                      class="w-12 h-12 object-cover rounded-lg"
                    />
                    <div class="flex-1 min-w-0">
                      <router-link
                        :to="{ name: 'product-detail', params: { id: product.id } }"
                        class="text-sm font-medium text-gray-900 hover:text-amber-600 block truncate"
                      >
                        {{ product.name }}
                      </router-link>
                      <p class="text-sm text-gray-500">${{ product.price }}</p>
                    </div>
                  </div>
                </div>
                <router-link
                  :to="{ name: 'products', query: { producer: story.id } }"
                  class="mt-4 block text-center text-sm font-medium text-amber-600 hover:text-amber-700"
                >
                  View All Products
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useProducerStoriesStore } from '@/stores/producerStories';

const route = useRoute();
const producerStoriesStore = useProducerStoriesStore();

const relatedProducts = ref<any[]>([]);

const loading = computed(() => producerStoriesStore.loading);
const story = computed(() => producerStoriesStore.currentStory);

const formatStoryContent = (content: string): string => {
  // Simple formatting - convert line breaks to paragraphs
  return content
    .split('\n\n')
    .map(paragraph => `<p class="mb-4">${paragraph.replace(/\n/g, '<br>')}</p>`)
    .join('');
};

const formatCertification = (cert: string): string => {
  return cert.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const getEmbedUrl = (url: string): string => {
  // Convert YouTube URLs to embed format
  if (url.includes('youtube.com/watch?v=')) {
    const videoId = url.split('v=')[1].split('&')[0];
    return `https://www.youtube.com/embed/${videoId}`;
  }
  if (url.includes('youtu.be/')) {
    const videoId = url.split('youtu.be/')[1];
    return `https://www.youtube.com/embed/${videoId}`;
  }
  return url;
};

const openGallery = (index: number) => {
  // Implement gallery modal here
  console.log('Open gallery at index:', index);
};

const loadRelatedProducts = async () => {
  if (story.value) {
    try {
      relatedProducts.value = await producerStoriesStore.getProductsByStoryId(story.value.id);
    } catch (error) {
      console.error('Error loading related products:', error);
    }
  }
};

watch(story, (newStory) => {
  if (newStory) {
    loadRelatedProducts();
  }
}, { immediate: true });

onMounted(async () => {
  const slug = route.params.slug as string;
  await producerStoriesStore.fetchStoryBySlug(slug);
});
</script>
