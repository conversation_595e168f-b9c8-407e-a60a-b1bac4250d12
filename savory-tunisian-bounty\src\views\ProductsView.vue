<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-serif font-bold text-gray-900 mb-4">
          Our Products
        </h1>
        <p class="text-lg text-gray-600">
          Discover authentic Tunisian flavors and traditional specialties
        </p>
      </div>

      <!-- Search and Filters -->
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-8">
        <!-- Search Bar -->
        <div class="lg:col-span-3">
          <div class="bg-white rounded-lg shadow-sm p-6">
            <SearchBar
              v-model="searchQuery"
              @search="handleSearch"
              placeholder="Search products, categories, or keywords..."
            />
          </div>
        </div>

        <!-- Advanced Filters -->
        <div class="lg:col-span-1">
          <ProductFilters />
        </div>
      </div>

      <!-- Results Info -->
      <div class="flex justify-between items-center mb-6">
        <p class="text-gray-600">
          {{ filteredProducts.length }} product{{
            filteredProducts.length !== 1 ? "s" : ""
          }}
          found
        </p>

        <div class="flex items-center space-x-4">
          <!-- Sort (placeholder for future implementation) -->
          <select class="input-field w-auto">
            <option>Sort by Name</option>
            <option>Sort by Price</option>
            <option>Sort by Newest</option>
          </select>
        </div>
      </div>

      <!-- Products Grid -->
      <div
        v-if="!loading && filteredProducts.length > 0"
        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
      >
        <ProductCard
          v-for="product in filteredProducts"
          :key="product.id"
          :product="product"
        />
      </div>

      <!-- Loading State -->
      <div
        v-else-if="loading"
        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
      >
        <div v-for="i in 8" :key="i" class="card animate-pulse">
          <div class="h-48 bg-gray-200"></div>
          <div class="p-4">
            <div class="h-6 bg-gray-200 rounded mb-2"></div>
            <div class="h-4 bg-gray-200 rounded mb-2"></div>
            <div class="h-6 bg-gray-200 rounded w-20"></div>
          </div>
        </div>
      </div>

      <!-- No Results -->
      <div v-else class="text-center py-12">
        <div class="text-6xl mb-4">🔍</div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">
          No products found
        </h3>
        <p class="text-gray-600 mb-6">
          Try adjusting your search criteria or browse our categories
        </p>
        <button @click="productsStore.clearFilters" class="btn-primary">
          Clear Filters
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useProductsStore } from "@/stores/products";
import ProductCard from "@/components/ProductCard.vue";
import SearchBar from "@/components/SearchBar.vue";
import ProductFilters from "@/components/ProductFilters.vue";

const route = useRoute();
const router = useRouter();
const productsStore = useProductsStore();

const searchQuery = ref("");
const loading = ref(true);

const filteredProducts = computed(() => productsStore.filteredProducts);

const handleSearch = () => {
  productsStore.updateFilter("search", searchQuery.value);
  updateURL();
};

const updateURL = () => {
  const query: Record<string, string> = {};

  if (searchQuery.value) {
    query.search = searchQuery.value;
  }

  router.push({ name: "products", query });
};

// Initialize from URL parameters
const initializeFromURL = () => {
  if (route.query.search) {
    searchQuery.value = route.query.search as string;
    productsStore.updateFilter("search", searchQuery.value);
  }
};

onMounted(async () => {
  try {
    await productsStore.initialize();
    initializeFromURL();
  } catch (error) {
    console.error("Error loading products:", error);
  } finally {
    loading.value = false;
  }
});

// Watch for route changes
watch(
  () => route.query,
  () => {
    initializeFromURL();
  },
  { deep: true }
);
</script>
