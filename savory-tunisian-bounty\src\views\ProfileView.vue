<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 class="text-3xl font-serif font-bold text-gray-900 mb-8">
        My Profile
      </h1>

      <!-- Navigation Tabs -->
      <div class="mb-8">
        <nav class="flex space-x-8" aria-label="Tabs">
          <button
            @click="activeTab = 'profile'"
            :class="[
              'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'profile'
                ? 'border-amber-500 text-amber-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
            ]"
          >
            Profile Information
          </button>
          <button
            @click="activeTab = 'addresses'"
            :class="[
              'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'addresses'
                ? 'border-amber-500 text-amber-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
            ]"
          >
            Addresses
          </button>
          <button
            @click="activeTab = 'orders'"
            :class="[
              'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'orders'
                ? 'border-amber-500 text-amber-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
            ]"
          >
            Order History
          </button>
          <button
            @click="activeTab = 'security'"
            :class="[
              'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === 'security'
                ? 'border-amber-500 text-amber-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
            ]"
          >
            Security
          </button>
        </nav>
      </div>

      <!-- Profile Information Tab -->
      <div
        v-if="activeTab === 'profile'"
        class="bg-white rounded-lg shadow-sm p-6"
      >
        <div class="space-y-6">
          <div>
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
              Profile Information
            </h2>

            <form @submit.prevent="updateProfile" class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    for="fullName"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Full Name
                  </label>
                  <input
                    id="fullName"
                    v-model="form.fullName"
                    type="text"
                    class="input-field"
                  />
                </div>

                <div>
                  <label
                    for="email"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Email
                  </label>
                  <input
                    id="email"
                    v-model="form.email"
                    type="email"
                    disabled
                    class="input-field bg-gray-50"
                  />
                </div>

                <div>
                  <label
                    for="phone"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Phone Number
                  </label>
                  <input
                    id="phone"
                    v-model="form.phone"
                    type="tel"
                    class="input-field"
                  />
                </div>
              </div>

              <div class="flex justify-end">
                <button
                  type="submit"
                  :disabled="updating"
                  class="btn-primary disabled:opacity-50"
                >
                  <span v-if="updating">Updating...</span>
                  <span v-else>Update Profile</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Addresses Tab -->
      <div v-if="activeTab === 'addresses'" class="space-y-6">
        <!-- Add New Address Button -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-900">My Addresses</h2>
            <button @click="showAddressForm = true" class="btn-primary">
              Add New Address
            </button>
          </div>

          <!-- Addresses List -->
          <div v-if="addressStore.loading" class="text-center py-8">
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto"
            ></div>
            <p class="mt-2 text-gray-600">Loading addresses...</p>
          </div>

          <div v-else-if="!addressStore.hasAddresses" class="text-center py-8">
            <div class="text-4xl mb-4">📍</div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">
              No addresses yet
            </h3>
            <p class="text-gray-600 mb-4">
              Add your first address to make checkout faster
            </p>
          </div>

          <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              v-for="address in addressStore.addresses"
              :key="address.id"
              class="border border-gray-200 rounded-lg p-4 relative"
              :class="{ 'border-amber-500 bg-amber-50': address.is_default }"
            >
              <div v-if="address.is_default" class="absolute top-2 right-2">
                <span
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800"
                >
                  Default
                </span>
              </div>

              <div class="space-y-2">
                <p class="font-medium text-gray-900">
                  {{ address.address_line_1 }}
                </p>
                <p v-if="address.address_line_2" class="text-gray-600">
                  {{ address.address_line_2 }}
                </p>
                <p class="text-gray-600">
                  {{ address.city }}, {{ address.postal_code }}
                </p>
                <p class="text-gray-600">{{ address.country }}</p>
              </div>

              <div class="mt-4 flex space-x-2">
                <button
                  @click="editAddress(address)"
                  class="text-sm text-amber-600 hover:text-amber-700"
                >
                  Edit
                </button>
                <button
                  v-if="!address.is_default"
                  @click="setDefaultAddress(address.id)"
                  class="text-sm text-blue-600 hover:text-blue-700"
                >
                  Set as Default
                </button>
                <button
                  @click="deleteAddress(address.id)"
                  class="text-sm text-red-600 hover:text-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Order History Tab -->
      <div v-if="activeTab === 'orders'" class="space-y-6">
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">
            Recent Orders
          </h2>
          <div class="text-center py-8">
            <p class="text-gray-600">
              View your complete order history in the
              <router-link
                to="/orders"
                class="text-amber-600 hover:text-amber-700 font-medium"
              >
                Orders section
              </router-link>
            </p>
          </div>
        </div>
      </div>

      <!-- Security Tab -->
      <div v-if="activeTab === 'security'" class="space-y-6">
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">
            Security Settings
          </h2>

          <div class="space-y-6">
            <!-- Password Reset -->
            <div class="border-b border-gray-200 pb-6">
              <h3 class="text-lg font-medium text-gray-900 mb-2">Password</h3>
              <p class="text-sm text-gray-600 mb-4">
                Reset your password to keep your account secure
              </p>
              <button @click="showPasswordReset = true" class="btn-outline">
                Reset Password
              </button>
            </div>

            <!-- Account Information -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">
                Account Information
              </h3>
              <div class="space-y-2 text-sm text-gray-600">
                <p>
                  <span class="font-medium">Account created:</span>
                  {{ formatDate(authStore.profile?.created_at) }}
                </p>
                <p>
                  <span class="font-medium">Last updated:</span>
                  {{ formatDate(authStore.profile?.updated_at) }}
                </p>
                <p>
                  <span class="font-medium">Role:</span>
                  {{ authStore.profile?.role || "Customer" }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Address Form Modal -->
      <div
        v-if="showAddressForm"
        class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      >
        <div
          class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white"
        >
          <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
              {{ editingAddress ? "Edit Address" : "Add New Address" }}
            </h3>
            <form @submit.prevent="saveAddress">
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Address Line 1 *
                  </label>
                  <input
                    v-model="addressForm.address_line_1"
                    type="text"
                    required
                    class="input-field"
                    placeholder="Street address"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Address Line 2
                  </label>
                  <input
                    v-model="addressForm.address_line_2"
                    type="text"
                    class="input-field"
                    placeholder="Apartment, suite, etc."
                  />
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      City *
                    </label>
                    <input
                      v-model="addressForm.city"
                      type="text"
                      required
                      class="input-field"
                    />
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      Postal Code *
                    </label>
                    <input
                      v-model="addressForm.postal_code"
                      type="text"
                      required
                      class="input-field"
                    />
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Country *
                  </label>
                  <select
                    v-model="addressForm.country"
                    required
                    class="input-field"
                  >
                    <option value="">Select Country</option>
                    <option value="Tunisia">Tunisia</option>
                    <option value="France">France</option>
                    <option value="Germany">Germany</option>
                    <option value="United States">United States</option>
                    <option value="Canada">Canada</option>
                    <option value="United Kingdom">United Kingdom</option>
                  </select>
                </div>

                <div class="flex items-center">
                  <input
                    id="is_default"
                    v-model="addressForm.is_default"
                    type="checkbox"
                    class="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded"
                  />
                  <label
                    for="is_default"
                    class="ml-2 block text-sm text-gray-900"
                  >
                    Set as default address
                  </label>
                </div>
              </div>

              <div class="flex space-x-3 mt-6">
                <button
                  type="submit"
                  :disabled="addressLoading"
                  class="flex-1 btn-primary py-2 disabled:opacity-50"
                >
                  <span v-if="addressLoading">Saving...</span>
                  <span v-else
                    >{{ editingAddress ? "Update" : "Add" }} Address</span
                  >
                </button>
                <button
                  type="button"
                  @click="cancelAddressForm"
                  class="flex-1 btn-outline py-2"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Password Reset Modal -->
      <div
        v-if="showPasswordReset"
        class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      >
        <div
          class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"
        >
          <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
              Reset Password
            </h3>
            <form @submit.prevent="handlePasswordReset">
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Email address
                </label>
                <input
                  v-model="passwordResetEmail"
                  type="email"
                  required
                  class="input-field"
                  placeholder="Enter your email"
                />
              </div>
              <div class="flex space-x-3">
                <button
                  type="submit"
                  :disabled="passwordResetLoading"
                  class="flex-1 btn-primary py-2 disabled:opacity-50"
                >
                  <span v-if="passwordResetLoading">Sending...</span>
                  <span v-else>Send Reset Link</span>
                </button>
                <button
                  type="button"
                  @click="showPasswordReset = false"
                  class="flex-1 btn-outline py-2"
                >
                  Cancel
                </button>
              </div>
            </form>
            <div
              v-if="passwordResetMessage"
              class="mt-4 text-sm text-green-600"
            >
              {{ passwordResetMessage }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { useAuthStore } from "@/stores/auth";
import { useAddressStore } from "@/stores/address";
import { useToastStore } from "@/stores/toast";
import type {
  UserAddress,
  UserAddressInsert,
  UserAddressUpdate,
} from "@/types";

const authStore = useAuthStore();
const addressStore = useAddressStore();
const toastStore = useToastStore();

// Tab management
const activeTab = ref("profile");

// Profile form
const updating = ref(false);
const form = reactive({
  fullName: "",
  email: "",
  phone: "",
});

// Address management
const showAddressForm = ref(false);
const editingAddress = ref<UserAddress | null>(null);
const addressLoading = ref(false);
const addressForm = reactive({
  address_line_1: "",
  address_line_2: "",
  city: "",
  postal_code: "",
  country: "",
  is_default: false,
});

// Password reset
const showPasswordReset = ref(false);
const passwordResetEmail = ref("");
const passwordResetLoading = ref(false);
const passwordResetMessage = ref("");

// Profile methods
const updateProfile = async () => {
  try {
    updating.value = true;
    const result = await authStore.updateProfile({
      full_name: form.fullName,
      phone: form.phone,
    });

    if (result?.error) {
      toastStore.error("Failed to update profile");
    } else {
      toastStore.success("Profile updated successfully");
    }
  } catch (error) {
    console.error("Error updating profile:", error);
    toastStore.error("Failed to update profile");
  } finally {
    updating.value = false;
  }
};

// Address methods
const editAddress = (address: UserAddress) => {
  editingAddress.value = address;
  addressForm.address_line_1 = address.address_line_1;
  addressForm.address_line_2 = address.address_line_2 || "";
  addressForm.city = address.city;
  addressForm.postal_code = address.postal_code;
  addressForm.country = address.country;
  addressForm.is_default = address.is_default;
  showAddressForm.value = true;
};

const saveAddress = async () => {
  if (!authStore.user?.id) return;

  try {
    addressLoading.value = true;

    if (editingAddress.value) {
      // Update existing address
      const updates: UserAddressUpdate = {
        address_line_1: addressForm.address_line_1,
        address_line_2: addressForm.address_line_2 || null,
        city: addressForm.city,
        postal_code: addressForm.postal_code,
        country: addressForm.country,
        is_default: addressForm.is_default,
      };

      await addressStore.updateAddress(editingAddress.value.id, updates);
    } else {
      // Create new address
      const newAddress: UserAddressInsert = {
        user_id: authStore.user.id,
        address_line_1: addressForm.address_line_1,
        address_line_2: addressForm.address_line_2 || null,
        city: addressForm.city,
        postal_code: addressForm.postal_code,
        country: addressForm.country,
        is_default: addressForm.is_default,
      };

      await addressStore.createAddress(newAddress);
    }

    cancelAddressForm();
  } catch (error) {
    console.error("Error saving address:", error);
  } finally {
    addressLoading.value = false;
  }
};

const cancelAddressForm = () => {
  showAddressForm.value = false;
  editingAddress.value = null;
  Object.keys(addressForm).forEach((key) => {
    if (typeof addressForm[key as keyof typeof addressForm] === "string") {
      (addressForm as any)[key] = "";
    } else if (
      typeof addressForm[key as keyof typeof addressForm] === "boolean"
    ) {
      (addressForm as any)[key] = false;
    }
  });
};

const setDefaultAddress = async (addressId: string) => {
  if (!authStore.user?.id) return;

  try {
    await addressStore.setDefaultAddress(addressId, authStore.user.id);
  } catch (error) {
    console.error("Error setting default address:", error);
  }
};

const deleteAddress = async (addressId: string) => {
  if (confirm("Are you sure you want to delete this address?")) {
    try {
      await addressStore.deleteAddress(addressId);
    } catch (error) {
      console.error("Error deleting address:", error);
    }
  }
};

// Password reset methods
const handlePasswordReset = async () => {
  try {
    passwordResetLoading.value = true;
    passwordResetMessage.value = "";

    const { error } = await authStore.resetPassword(passwordResetEmail.value);

    if (error) {
      toastStore.error("Failed to send reset email");
      return;
    }

    passwordResetMessage.value = "Password reset link sent to your email";
    setTimeout(() => {
      showPasswordReset.value = false;
      passwordResetEmail.value = "";
      passwordResetMessage.value = "";
    }, 3000);
  } catch (err) {
    console.error("Password reset error:", err);
    toastStore.error("Failed to send reset email");
  } finally {
    passwordResetLoading.value = false;
  }
};

// Utility methods
const formatDate = (dateString?: string) => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// Initialize data
onMounted(async () => {
  if (authStore.profile) {
    form.fullName = authStore.profile.full_name || "";
    form.email = authStore.profile.email;
    form.phone = authStore.profile.phone || "";
    passwordResetEmail.value = authStore.profile.email;
  }

  // Load addresses if user is authenticated
  if (authStore.user?.id) {
    await addressStore.fetchAddresses(authStore.user.id);
  }
});
</script>
