<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Categories</h1>
            <p class="text-gray-600">Manage product categories</p>
          </div>
          <div class="flex items-center space-x-4">
            <router-link
              to="/admin/dashboard"
              class="btn-outline text-sm"
            >
              ← Dashboard
            </router-link>
            <button
              @click="openCreateModal"
              class="btn-primary text-sm"
            >
              Add Category
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Categories Grid -->
      <div v-if="loading" class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
        <p class="text-gray-600 mt-4">Loading categories...</p>
      </div>

      <div v-else-if="categories.length === 0" class="text-center py-12">
        <div class="text-gray-400 mb-4">
          <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No categories found</h3>
        <p class="text-gray-600 mb-6">Get started by creating your first category.</p>
        <button
          @click="openCreateModal"
          class="btn-primary"
        >
          Add Category
        </button>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          v-for="category in categories"
          :key="category.id"
          class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow"
        >
          <!-- Category Image -->
          <div class="aspect-w-16 aspect-h-9 w-full overflow-hidden rounded-t-lg bg-gray-200">
            <img
              v-if="category.image_url"
              :src="category.image_url"
              :alt="category.name"
              class="w-full h-32 object-cover"
              @error="handleImageError"
            />
            <div v-else class="w-full h-32 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
              <svg class="w-12 h-12 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
            </div>
          </div>

          <!-- Category Info -->
          <div class="p-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">
              {{ category.name }}
            </h3>
            <p class="text-gray-600 text-sm mb-4 line-clamp-2">
              {{ category.description || 'No description' }}
            </p>

            <!-- Product Count -->
            <div class="flex items-center justify-between mb-4">
              <span class="text-sm text-gray-500">
                {{ getProductCount(category.id) }} products
              </span>
              <span class="text-xs text-gray-400">
                {{ formatDate(category.created_at) }}
              </span>
            </div>

            <!-- Actions -->
            <div class="flex space-x-2">
              <button
                @click="openEditModal(category)"
                class="flex-1 btn-outline text-sm"
              >
                Edit
              </button>
              <button
                @click="handleDeleteCategory(category.id)"
                :disabled="getProductCount(category.id) > 0"
                class="flex-1 bg-red-50 text-red-600 hover:bg-red-100 border border-red-200 px-3 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Category Modal -->
    <div
      v-if="showModal"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="closeModal"
    >
      <div
        class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-md bg-white"
        @click.stop
      >
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">
            {{ isEditing ? 'Edit Category' : 'Add New Category' }}
          </h3>
          <button
            @click="closeModal"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-4">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
              Category Name *
            </label>
            <input
              id="name"
              v-model="form.name"
              type="text"
              required
              class="input-field"
              placeholder="Enter category name"
            />
          </div>

          <div>
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="description"
              v-model="form.description"
              rows="3"
              class="input-field"
              placeholder="Enter category description"
            ></textarea>
          </div>

          <div>
            <label for="image_url" class="block text-sm font-medium text-gray-700 mb-2">
              Image URL
            </label>
            <input
              id="image_url"
              v-model="form.image_url"
              type="url"
              class="input-field"
              placeholder="https://example.com/image.jpg"
            />
          </div>

          <!-- Image Preview -->
          <div v-if="form.image_url" class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Preview</label>
            <div class="w-full h-32 bg-gray-100 rounded-lg overflow-hidden">
              <img
                :src="form.image_url"
                :alt="form.name"
                class="w-full h-full object-cover"
                @error="handleImageError"
              />
            </div>
          </div>

          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              @click="closeModal"
              class="btn-outline"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="formLoading"
              class="btn-primary"
            >
              <span v-if="formLoading" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ isEditing ? 'Updating...' : 'Creating...' }}
              </span>
              <span v-else>
                {{ isEditing ? 'Update Category' : 'Create Category' }}
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from "vue";
import { useAdminStore } from "@/stores/admin";
import type { Category, CategoryInsert, CategoryUpdate } from "@/types";

const adminStore = useAdminStore();

// State
const showModal = ref(false);
const formLoading = ref(false);
const editingCategory = ref<Category | null>(null);

const form = reactive({
  name: "",
  description: "",
  image_url: "",
});

// Computed
const categories = computed(() => adminStore.categories);
const products = computed(() => adminStore.products);
const loading = computed(() => adminStore.loading);
const isEditing = computed(() => !!editingCategory.value);

// Methods
const getProductCount = (categoryId: string) => {
  return products.value.filter(product => product.category_id === categoryId && product.is_active).length;
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

const openCreateModal = () => {
  editingCategory.value = null;
  form.name = "";
  form.description = "";
  form.image_url = "";
  showModal.value = true;
};

const openEditModal = (category: Category) => {
  editingCategory.value = category;
  form.name = category.name;
  form.description = category.description || "";
  form.image_url = category.image_url || "";
  showModal.value = true;
};

const closeModal = () => {
  showModal.value = false;
  editingCategory.value = null;
  form.name = "";
  form.description = "";
  form.image_url = "";
};

const handleSubmit = async () => {
  try {
    formLoading.value = true;
    
    const categoryData = {
      name: form.name,
      description: form.description || null,
      image_url: form.image_url || null,
    };

    let success = false;
    
    if (isEditing.value) {
      const result = await adminStore.updateCategory(editingCategory.value!.id, categoryData as CategoryUpdate);
      success = !!result;
    } else {
      const result = await adminStore.createCategory(categoryData as CategoryInsert);
      success = !!result;
    }
    
    if (success) {
      closeModal();
    }
  } catch (error) {
    console.error("Error saving category:", error);
  } finally {
    formLoading.value = false;
  }
};

const handleDeleteCategory = async (id: string) => {
  const productCount = getProductCount(id);
  
  if (productCount > 0) {
    alert(`Cannot delete category with ${productCount} active products. Please move or delete the products first.`);
    return;
  }
  
  if (confirm("Are you sure you want to delete this category? This action cannot be undone.")) {
    await adminStore.deleteCategory(id);
  }
};

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.style.display = "none";
};

onMounted(() => {
  adminStore.fetchCategories();
  adminStore.fetchProducts();
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
