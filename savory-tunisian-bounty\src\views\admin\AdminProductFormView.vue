<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">
              {{ isEditing ? 'Edit Product' : 'Add New Product' }}
            </h1>
            <p class="text-gray-600">
              {{ isEditing ? 'Update product information' : 'Create a new product listing' }}
            </p>
          </div>
          <div class="flex items-center space-x-4">
            <router-link
              to="/admin/products"
              class="btn-outline text-sm"
            >
              ← Back to Products
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <form @submit.prevent="handleSubmit" class="space-y-8">
        <!-- Basic Information -->
        <div class="card">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">Basic Information</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                Product Name *
              </label>
              <input
                id="name"
                v-model="form.name"
                type="text"
                required
                class="input-field"
                placeholder="Enter product name"
              />
            </div>

            <div>
              <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                Category *
              </label>
              <select
                id="category"
                v-model="form.category_id"
                required
                class="input-field"
              >
                <option value="">Select a category</option>
                <option
                  v-for="category in categories"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.name }}
                </option>
              </select>
            </div>

            <div>
              <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
                Price *
              </label>
              <div class="relative">
                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                <input
                  id="price"
                  v-model.number="form.price"
                  type="number"
                  step="0.01"
                  min="0"
                  required
                  class="input-field pl-8"
                  placeholder="0.00"
                />
              </div>
            </div>

            <div>
              <label for="stock" class="block text-sm font-medium text-gray-700 mb-2">
                Stock Quantity *
              </label>
              <input
                id="stock"
                v-model.number="form.stock_quantity"
                type="number"
                min="0"
                required
                class="input-field"
                placeholder="0"
              />
            </div>
          </div>

          <div class="mt-6">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="description"
              v-model="form.description"
              rows="4"
              class="input-field"
              placeholder="Enter product description"
            ></textarea>
          </div>

          <div class="mt-6">
            <label class="flex items-center">
              <input
                v-model="form.is_active"
                type="checkbox"
                class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span class="ml-2 text-sm text-gray-700">Product is active</span>
            </label>
          </div>
        </div>

        <!-- Images -->
        <div class="card">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">Product Images</h3>
          
          <!-- Main Image URL -->
          <div class="mb-6">
            <label for="image_url" class="block text-sm font-medium text-gray-700 mb-2">
              Main Image URL
            </label>
            <input
              id="image_url"
              v-model="form.image_url"
              type="url"
              class="input-field"
              placeholder="https://example.com/image.jpg"
            />
          </div>

          <!-- Image Upload -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Upload Images
            </label>
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <input
                ref="fileInput"
                type="file"
                multiple
                accept="image/*"
                class="hidden"
                @change="handleFileUpload"
              />
              <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              <p class="text-gray-600 mb-2">Click to upload images or drag and drop</p>
              <p class="text-sm text-gray-500">PNG, JPG, GIF up to 10MB</p>
              <button
                type="button"
                @click="$refs.fileInput.click()"
                class="btn-outline mt-4"
              >
                Choose Files
              </button>
            </div>
          </div>

          <!-- Additional Image URLs -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Additional Image URLs
            </label>
            <div class="space-y-3">
              <div
                v-for="(image, index) in form.images"
                :key="index"
                class="flex items-center space-x-3"
              >
                <input
                  v-model="form.images[index]"
                  type="url"
                  class="input-field flex-1"
                  placeholder="https://example.com/image.jpg"
                />
                <button
                  type="button"
                  @click="removeImage(index)"
                  class="text-red-600 hover:text-red-700"
                >
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
              <button
                type="button"
                @click="addImageUrl"
                class="btn-outline text-sm"
              >
                Add Image URL
              </button>
            </div>
          </div>

          <!-- Image Preview -->
          <div v-if="allImages.length > 0" class="mt-6">
            <h4 class="text-sm font-medium text-gray-700 mb-3">Image Preview</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div
                v-for="(image, index) in allImages"
                :key="index"
                class="relative aspect-square bg-gray-100 rounded-lg overflow-hidden"
              >
                <img
                  :src="image"
                  :alt="`Product image ${index + 1}`"
                  class="w-full h-full object-cover"
                  @error="handleImageError"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-4">
          <router-link
            to="/admin/products"
            class="btn-outline"
          >
            Cancel
          </router-link>
          <button
            type="submit"
            :disabled="loading"
            class="btn-primary"
          >
            <span v-if="loading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isEditing ? 'Updating...' : 'Creating...' }}
            </span>
            <span v-else>
              {{ isEditing ? 'Update Product' : 'Create Product' }}
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useAdminStore } from "@/stores/admin";
import type { ProductInsert, ProductUpdate } from "@/types";

const route = useRoute();
const router = useRouter();
const adminStore = useAdminStore();

// Props
const props = defineProps<{
  id?: string;
}>();

// State
const loading = ref(false);
const fileInput = ref<HTMLInputElement>();

const form = reactive({
  name: "",
  description: "",
  price: 0,
  stock_quantity: 0,
  category_id: "",
  image_url: "",
  images: [] as string[],
  is_active: true,
});

// Computed
const isEditing = computed(() => !!props.id);
const categories = computed(() => adminStore.categories);

const allImages = computed(() => {
  const images = [];
  if (form.image_url) images.push(form.image_url);
  if (form.images) images.push(...form.images.filter(img => img.trim()));
  return images;
});

// Methods
const loadProduct = async () => {
  if (!props.id) return;
  
  try {
    loading.value = true;
    const product = adminStore.products.find(p => p.id === props.id);
    
    if (product) {
      form.name = product.name;
      form.description = product.description || "";
      form.price = product.price;
      form.stock_quantity = product.stock_quantity;
      form.category_id = product.category_id || "";
      form.image_url = product.image_url || "";
      form.images = product.images ? [...product.images] : [];
      form.is_active = product.is_active;
    }
  } catch (error) {
    console.error("Error loading product:", error);
  } finally {
    loading.value = false;
  }
};

const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;
  
  if (!files) return;
  
  try {
    loading.value = true;
    
    for (const file of Array.from(files)) {
      const url = await adminStore.uploadImage(file);
      if (url) {
        if (!form.image_url) {
          form.image_url = url;
        } else {
          form.images.push(url);
        }
      }
    }
  } catch (error) {
    console.error("Error uploading files:", error);
  } finally {
    loading.value = false;
  }
};

const addImageUrl = () => {
  form.images.push("");
};

const removeImage = (index: number) => {
  form.images.splice(index, 1);
};

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.style.display = "none";
};

const handleSubmit = async () => {
  try {
    loading.value = true;
    
    const productData = {
      name: form.name,
      description: form.description || null,
      price: form.price,
      stock_quantity: form.stock_quantity,
      category_id: form.category_id || null,
      image_url: form.image_url || null,
      images: form.images.filter(img => img.trim()) || null,
      is_active: form.is_active,
    };

    let success = false;
    
    if (isEditing.value) {
      const result = await adminStore.updateProduct(props.id!, productData as ProductUpdate);
      success = !!result;
    } else {
      const result = await adminStore.createProduct(productData as ProductInsert);
      success = !!result;
    }
    
    if (success) {
      router.push("/admin/products");
    }
  } catch (error) {
    console.error("Error saving product:", error);
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  await adminStore.fetchCategories();
  if (isEditing.value) {
    await adminStore.fetchProducts();
    await loadProduct();
  }
});
</script>
