<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Products</h1>
            <p class="text-gray-600">Manage your product catalog</p>
          </div>
          <div class="flex items-center space-x-4">
            <router-link
              to="/admin/dashboard"
              class="btn-outline text-sm"
            >
              ← Dashboard
            </router-link>
            <router-link
              to="/admin/products/new"
              class="btn-primary text-sm"
            >
              Add Product
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Filters and Search -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div class="flex flex-col lg:flex-row gap-4">
          <div class="flex-1">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search products..."
              class="input-field"
              @input="handleSearch"
            />
          </div>
          <div class="lg:w-48">
            <select
              v-model="selectedCategory"
              class="input-field"
              @change="handleCategoryFilter"
            >
              <option value="">All Categories</option>
              <option
                v-for="category in categories"
                :key="category.id"
                :value="category.id"
              >
                {{ category.name }}
              </option>
            </select>
          </div>
          <div class="lg:w-32">
            <select
              v-model="statusFilter"
              class="input-field"
              @change="handleStatusFilter"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Products Grid -->
      <div v-if="loading" class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
        <p class="text-gray-600 mt-4">Loading products...</p>
      </div>

      <div v-else-if="filteredProducts.length === 0" class="text-center py-12">
        <div class="text-gray-400 mb-4">
          <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No products found</h3>
        <p class="text-gray-600 mb-6">Get started by creating your first product.</p>
        <router-link
          to="/admin/products/new"
          class="btn-primary"
        >
          Add Product
        </router-link>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <div
          v-for="product in filteredProducts"
          :key="product.id"
          class="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow"
        >
          <!-- Product Image -->
          <div class="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-t-lg bg-gray-200">
            <img
              v-if="product.image_url || (product.images && product.images.length > 0)"
              :src="product.image_url || product.images?.[0]"
              :alt="product.name"
              class="w-full h-48 object-cover"
              @error="handleImageError"
            />
            <div v-else class="w-full h-48 bg-gray-200 flex items-center justify-center">
              <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
          </div>

          <!-- Product Info -->
          <div class="p-4">
            <div class="flex items-start justify-between mb-2">
              <h3 class="text-lg font-semibold text-gray-900 truncate">
                {{ product.name }}
              </h3>
              <span
                :class="product.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                class="px-2 py-1 text-xs rounded-full ml-2"
              >
                {{ product.is_active ? 'Active' : 'Inactive' }}
              </span>
            </div>

            <p class="text-gray-600 text-sm mb-3 line-clamp-2">
              {{ product.description || 'No description' }}
            </p>

            <div class="flex items-center justify-between mb-4">
              <span class="text-xl font-bold text-primary-600">
                ${{ product.price.toFixed(2) }}
              </span>
              <span class="text-sm text-gray-500">
                Stock: {{ product.stock_quantity }}
              </span>
            </div>

            <!-- Actions -->
            <div class="flex space-x-2">
              <router-link
                :to="{ name: 'admin-product-edit', params: { id: product.id } }"
                class="flex-1 btn-outline text-sm text-center"
              >
                Edit
              </router-link>
              <button
                v-if="product.is_active"
                @click="handleDeleteProduct(product.id)"
                class="flex-1 bg-red-50 text-red-600 hover:bg-red-100 border border-red-200 px-3 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Delete
              </button>
              <button
                v-else
                @click="handleRestoreProduct(product.id)"
                class="flex-1 bg-green-50 text-green-600 hover:bg-green-100 border border-green-200 px-3 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Restore
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useAdminStore } from "@/stores/admin";

const adminStore = useAdminStore();

// State
const searchQuery = ref("");
const selectedCategory = ref("");
const statusFilter = ref("");

// Computed
const products = computed(() => adminStore.products);
const categories = computed(() => adminStore.categories);
const loading = computed(() => adminStore.loading);

const filteredProducts = computed(() => {
  let filtered = products.value;

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(
      (product) =>
        product.name.toLowerCase().includes(query) ||
        product.description?.toLowerCase().includes(query)
    );
  }

  // Filter by category
  if (selectedCategory.value) {
    filtered = filtered.filter(
      (product) => product.category_id === selectedCategory.value
    );
  }

  // Filter by status
  if (statusFilter.value === "active") {
    filtered = filtered.filter((product) => product.is_active);
  } else if (statusFilter.value === "inactive") {
    filtered = filtered.filter((product) => !product.is_active);
  }

  return filtered;
});

// Methods
const handleSearch = () => {
  // Search is reactive through computed property
};

const handleCategoryFilter = () => {
  // Filter is reactive through computed property
};

const handleStatusFilter = () => {
  // Filter is reactive through computed property
};

const handleDeleteProduct = async (id: string) => {
  if (confirm("Are you sure you want to delete this product?")) {
    await adminStore.deleteProduct(id);
  }
};

const handleRestoreProduct = async (id: string) => {
  if (confirm("Are you sure you want to restore this product?")) {
    await adminStore.restoreProduct(id);
  }
};

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.style.display = "none";
};

onMounted(() => {
  adminStore.fetchProducts();
  adminStore.fetchCategories();
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
