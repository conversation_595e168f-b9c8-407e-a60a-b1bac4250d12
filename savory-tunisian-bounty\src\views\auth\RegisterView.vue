<template>
  <div
    class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8"
  >
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="text-center">
        <h2 class="text-3xl font-serif font-bold text-gray-900">
          Create Account
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          Join us to discover authentic Tunisian flavors
        </p>
      </div>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div>
            <label
              for="fullName"
              class="block text-sm font-medium text-gray-700"
            >
              Full Name
            </label>
            <div class="mt-1">
              <input
                id="fullName"
                v-model="form.fullName"
                type="text"
                autocomplete="name"
                required
                class="input-field"
                :class="{ 'border-red-500': errors.fullName }"
              />
              <p v-if="errors.fullName" class="mt-1 text-sm text-red-600">
                {{ errors.fullName }}
              </p>
            </div>
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">
              Email address
            </label>
            <div class="mt-1">
              <input
                id="email"
                v-model="form.email"
                type="email"
                autocomplete="email"
                required
                class="input-field"
                :class="{ 'border-red-500': errors.email }"
              />
              <p v-if="errors.email" class="mt-1 text-sm text-red-600">
                {{ errors.email }}
              </p>
            </div>
          </div>

          <div>
            <label
              for="password"
              class="block text-sm font-medium text-gray-700"
            >
              Password
            </label>
            <div class="mt-1">
              <input
                id="password"
                v-model="form.password"
                type="password"
                autocomplete="new-password"
                required
                class="input-field"
                :class="{ 'border-red-500': errors.password }"
              />
              <p v-if="errors.password" class="mt-1 text-sm text-red-600">
                {{ errors.password }}
              </p>
            </div>
          </div>

          <div>
            <label
              for="confirmPassword"
              class="block text-sm font-medium text-gray-700"
            >
              Confirm Password
            </label>
            <div class="mt-1">
              <input
                id="confirmPassword"
                v-model="form.confirmPassword"
                type="password"
                autocomplete="new-password"
                required
                class="input-field"
                :class="{ 'border-red-500': errors.confirmPassword }"
              />
              <p
                v-if="errors.confirmPassword"
                class="mt-1 text-sm text-red-600"
              >
                {{ errors.confirmPassword }}
              </p>
            </div>
          </div>

          <div class="flex items-center">
            <input
              id="terms"
              v-model="form.acceptTerms"
              type="checkbox"
              required
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label for="terms" class="ml-2 block text-sm text-gray-900">
              I agree to the
              <a href="#" class="text-primary-600 hover:text-primary-500"
                >Terms of Service</a
              >
              and
              <a href="#" class="text-primary-600 hover:text-primary-500"
                >Privacy Policy</a
              >
            </label>
          </div>

          <div>
            <button
              type="submit"
              :disabled="loading"
              class="w-full btn-primary py-3 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="loading">Creating account...</span>
              <span v-else>Create account</span>
            </button>
          </div>

          <div v-if="error" class="rounded-md bg-red-50 p-4">
            <div class="text-sm text-red-700">
              {{ error }}
            </div>
          </div>

          <div v-if="success" class="rounded-md bg-green-50 p-4">
            <div class="text-sm text-green-700">
              {{ success }}
            </div>
          </div>
        </form>

        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300" />
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500"
                >Already have an account?</span
              >
            </div>
          </div>

          <div class="mt-6">
            <router-link
              to="/login"
              class="w-full btn-outline text-center block py-3"
            >
              Sign in instead
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";

const router = useRouter();
const authStore = useAuthStore();

const loading = ref(false);
const error = ref("");
const success = ref("");

const form = reactive({
  fullName: "",
  email: "",
  password: "",
  confirmPassword: "",
  acceptTerms: false,
});

const errors = reactive({
  fullName: "",
  email: "",
  password: "",
  confirmPassword: "",
});

const validateForm = () => {
  errors.fullName = "";
  errors.email = "";
  errors.password = "";
  errors.confirmPassword = "";

  let isValid = true;

  if (!form.fullName.trim()) {
    errors.fullName = "Full name is required";
    isValid = false;
  }

  if (!form.email) {
    errors.email = "Email is required";
    isValid = false;
  }

  if (!form.password) {
    errors.password = "Password is required";
    isValid = false;
  } else if (form.password.length < 6) {
    errors.password = "Password must be at least 6 characters";
    isValid = false;
  }

  if (!form.confirmPassword) {
    errors.confirmPassword = "Please confirm your password";
    isValid = false;
  } else if (form.password !== form.confirmPassword) {
    errors.confirmPassword = "Passwords do not match";
    isValid = false;
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  try {
    loading.value = true;
    error.value = "";
    success.value = "";

    const { data, error: signUpError } = await authStore.signUp(
      form.email,
      form.password,
      form.fullName
    );

    if (signUpError) {
      error.value = (signUpError as any)?.message || "Registration failed";
      return;
    }

    if (data?.user && !data.session) {
      success.value =
        "Please check your email to confirm your account before signing in.";
      // Clear form
      Object.keys(form).forEach((key) => {
        if (typeof form[key as keyof typeof form] === "string") {
          (form as any)[key] = "";
        } else if (typeof form[key as keyof typeof form] === "boolean") {
          (form as any)[key] = false;
        }
      });
    } else {
      // User is automatically signed in
      router.push("/");
    }
  } catch (err) {
    error.value = "An unexpected error occurred";
    console.error("Registration error:", err);
  } finally {
    loading.value = false;
  }
};
</script>
