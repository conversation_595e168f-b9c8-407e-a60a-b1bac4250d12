<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="text-center">
        <h1 class="text-4xl font-serif font-bold text-amber-600 mb-2">
          Savory Tunisian Bounty
        </h1>
        <h2 class="text-2xl font-bold text-gray-900">Reset Your Password</h2>
        <p class="mt-2 text-sm text-gray-600">
          Enter your new password below
        </p>
      </div>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <!-- Success Message -->
        <div v-if="success" class="rounded-md bg-green-50 p-4 mb-6">
          <div class="text-sm text-green-700">
            {{ success }}
          </div>
          <div class="mt-4">
            <router-link to="/login" class="btn-primary w-full text-center block">
              Go to Login
            </router-link>
          </div>
        </div>

        <!-- Reset Form -->
        <form v-else @submit.prevent="handleSubmit" class="space-y-6">
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">
              New Password
            </label>
            <div class="mt-1">
              <input
                id="password"
                v-model="form.password"
                type="password"
                required
                class="input-field"
                :class="{ 'border-red-300': errors.password }"
                placeholder="Enter your new password"
              />
              <p v-if="errors.password" class="mt-1 text-sm text-red-600">
                {{ errors.password }}
              </p>
            </div>
          </div>

          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700">
              Confirm New Password
            </label>
            <div class="mt-1">
              <input
                id="confirmPassword"
                v-model="form.confirmPassword"
                type="password"
                required
                class="input-field"
                :class="{ 'border-red-300': errors.confirmPassword }"
                placeholder="Confirm your new password"
              />
              <p v-if="errors.confirmPassword" class="mt-1 text-sm text-red-600">
                {{ errors.confirmPassword }}
              </p>
            </div>
          </div>

          <div>
            <button
              type="submit"
              :disabled="loading"
              class="w-full btn-primary py-3 disabled:opacity-50"
            >
              <span v-if="loading">Updating Password...</span>
              <span v-else>Update Password</span>
            </button>
          </div>

          <div v-if="error" class="rounded-md bg-red-50 p-4">
            <div class="text-sm text-red-700">
              {{ error }}
            </div>
          </div>
        </form>

        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300" />
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">
                Remember your password?
              </span>
            </div>
          </div>

          <div class="mt-6">
            <router-link
              to="/login"
              class="w-full btn-outline text-center block py-3"
            >
              Back to Login
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { supabase } from '@/lib/supabase'

const router = useRouter()
const route = useRoute()

const loading = ref(false)
const error = ref('')
const success = ref('')

const form = reactive({
  password: '',
  confirmPassword: ''
})

const errors = reactive({
  password: '',
  confirmPassword: ''
})

const validateForm = (): boolean => {
  // Reset errors
  Object.keys(errors).forEach(key => {
    (errors as any)[key] = ''
  })

  let isValid = true

  // Password validation
  if (form.password.length < 6) {
    errors.password = 'Password must be at least 6 characters long'
    isValid = false
  }

  // Confirm password validation
  if (form.password !== form.confirmPassword) {
    errors.confirmPassword = 'Passwords do not match'
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  if (!validateForm()) return

  try {
    loading.value = true
    error.value = ''

    const { error: updateError } = await supabase.auth.updateUser({
      password: form.password
    })

    if (updateError) {
      error.value = updateError.message || 'Failed to update password'
      return
    }

    success.value = 'Password updated successfully! You can now log in with your new password.'
    
    // Clear form
    form.password = ''
    form.confirmPassword = ''
  } catch (err) {
    error.value = 'An unexpected error occurred'
    console.error('Password reset error:', err)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // Check if we have the necessary tokens in the URL
  const accessToken = route.query.access_token as string
  const refreshToken = route.query.refresh_token as string
  
  if (!accessToken || !refreshToken) {
    error.value = 'Invalid or expired reset link. Please request a new password reset.'
    return
  }

  // Set the session with the tokens from the URL
  supabase.auth.setSession({
    access_token: accessToken,
    refresh_token: refreshToken
  })
})
</script>
